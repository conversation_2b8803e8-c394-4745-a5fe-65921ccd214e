import { boot } from "quasar/wrappers";
import constantRoutes from "src/router/constantRoutes";
import { asyncRootRoute, asyncRoutesChildren } from "src/router/routes";
import constructionRouters from "src/router/utils/permissionUtils";
import { useRouterStore } from "src/stores/permission";
import { useUserStore } from "src/stores/user";
import { deepClone } from "src/utils";
import { RouteRecordRaw } from "vue-router";

export default boot(async ({ router }) => {
  const routerStore = useRouterStore();
  const userStore = useUserStore();
  router.beforeEach(async (to, from, next) => {
    const token = userStore.GetToken();
    if (token) {
      if (to.path === "/login") {
        next({ path: "/" });
        return;
      }

      if (userStore.GetUserRole() && routerStore.getPermissionRoutes.length) {
        next();
        return;
      }

      try {
        await routerStore.getUserMenu();
        const accessRoutes: any = deepClone(asyncRoutesChildren);
        if (asyncRootRoute[0]) {
          asyncRootRoute[0].children = constructionRouters(accessRoutes);
          routerStore.setRoutes(asyncRootRoute);

          // 添加路由
          for (let item of asyncRootRoute) {
            router.addRoute(item as RouteRecordRaw);
          }
        }
        // 直接导航到目标路由，不使用 replace
        next(to.fullPath);
      } catch (error) {
        console.error("Failed to setup routes:", error);
        next("/login");
      }
    } else {
      if (constantRoutes.some((item) => item.path === to.path)) {
        next();
      } else {
        next({ path: "/login" });
      }
    }
  });
});
