<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table
          v-model:pagination="pagination"
          row-key="id"
          separator="cell"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          @request="onRequest"
        >
          <template #top="props">
            <q-btn color="primary" label="添加公司" @click="createCompany" />
            <q-space />
            <q-btn
              flat
              round
              dense
              :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
              class="q-ml-md"
              @click="props.toggleFullscreen"
            />
          </template>

          <template #body-cell-company_type="props">
            <q-td :props="props">
              <span>{{ props.row.company_type || "未指定" }}</span>
            </q-td>
          </template>

          <template #body-cell-corporate="props">
            <q-td :props="props">
              <span>{{ props.row.corporate || "未指定" }}</span>
            </q-td>
          </template>

          <template #body-cell-social_code="props">
            <q-td :props="props">
              <span>{{ props.row.social_code || "未指定" }}</span>
            </q-td>
          </template>

          <template #body-cell-industry_type="props">
            <q-td :props="props">
              <span>{{ props.row.industry_type || "未指定" }}</span>
            </q-td>
          </template>

          <template #body-cell-created_at="props">
            <q-td :props="props">
              {{ showDateTime(props.row.created_at) }}
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    color="primary"
                    size="xs"
                    label="查看"
                    @click="handleDetail(props.row)"
                  />
                  <q-btn
                    color="warning"
                    size="xs"
                    label="编辑"
                    @click="handleEdit(props.row)"
                  />
                  <q-btn
                    color="negative"
                    size="xs"
                    label="删除"
                    @click="handleDelete(props.row)"
                  />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { FormatDateTime } from "src/utils/date";
import { computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const router = useRouter();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/company/list",
  status: "/api/company/status",
};
const columns = computed(() => {
  return [
    {
      name: "name",
      required: true,
      align: "left",
      label: "公司名称",
      field: "name",
    },
    {
      name: "serial",
      required: true,
      align: "center",
      label: "公司编号",
      field: "serial",
    },
    {
      name: "company_type",
      align: "center",
      label: "公司类型",
      field: "company_type",
    },
    {
      name: "corporate",
      align: "center",
      label: "法人代表",
      field: "corporate",
    },
    {
      name: "social_code",
      align: "center",
      label: "社会信用代码",
      field: "social_code",
    },
    {
      name: "industry_type",
      align: "center",
      label: "行业类型",
      field: "industry_type",
    },
    {
      name: "created_at",
      align: "center",
      label: "创建时间",
      field: "created_at",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: "操作",
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(() => {
  pagination.value.sortBy = "created_at";
  getTableData();
});

const handleDetail = (item) => {
  router.push({ name: "companyDetail", query: { id: item.id } });
};

const handleEdit = (item) => {
  router.push({ name: "companyEdit", query: { id: item.id } });
};

const createCompany = () => {
  router.push({ name: "companyCreate" });
};

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatDateTime(datetime);
  };
});
</script>

<style lang="scss" scoped></style>
