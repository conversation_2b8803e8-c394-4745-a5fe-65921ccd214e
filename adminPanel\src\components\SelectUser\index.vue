<template>
  <div>
    <q-input
      v-model="newName" :label="label || $t('Select') + $t('User')"
      :class="className"
      readonly
    >
      <template #append>
        <q-btn
          dense color="primary"
          :label="$t('Select')"
          @click="showSelectUserDialog"
        />
      </template>
    </q-input>
    <SelectUserDialog ref="selectUserDialog" :selection="selection" @handleSelectUser="handleSelectUser" />
  </div>
</template>

<script setup>
import { computed, ref, toRefs } from 'vue'
import SelectUserDialog from './SelectUserDialog'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  selectUser: {
    type: [Object, Array],
    required: false,
  },
  selectUserId: {
    type: [Number, Array],
    required: false,
  },
  selectUsername: {
    type: [String, Array],
    required: false,
  },
  label: {
    type: String,
    required: false,
    default: function() {
      return this.$t('Select') + this.$t('User')
    },
  },
  className: {
    type: String,
    required: false,
    default: '',
  },
  selection: {
    type: String,
    required: false,
    default: 'single',
  },
})

const { selectUser, selectUserId, selectUsername, label, className, selection } = toRefs(props)
// const newName = ref()
const newName = computed(() => {
  if (selection === 'multiple') {
    let nickname = ''
    let realName = ''
    let id = ''
    for (const u of selectUser.value) {
      if (u.nickname) {
        nickname += u.nickname + ' '
      }
      else if (u.real_name) {
        realName += u.real_name + ' '
      }
      else if (u.id) {
        id += u.id + ' '
      }
      else {
        return ''
      }
    }
    return nickname || realName || id
  }
  else {
    return selectUsername
  }
})
const selectUserDialog = ref(null)
const showSelectUserDialog = () => {
  selectUserDialog.value.show(selectUser.value)
}

const emit = defineEmits(['update:selectUser', 'update:selectUserId', 'update:selectUsername'])

const handleSelectUser = (event) => {
  console.log(event, '返回是否成功？')
  if (selection === 'multiple') {
    const usernameList = []
    const userIdList = []
    for (const i of event) {
      userIdList.push(i.id)
      usernameList.push(i.username)
    }
    emit('update:selectUser', event)
    emit('update:selectUserId', userIdList)
    emit('update:selectUsername', usernameList)
  }
  else {
    if (event.length) {
      newName.value = event[0].username
      emit('update:selectUser', event[0])
      emit('update:selectUserId', event[0].id)
      emit('update:selectUsername', event[0].username)
    }
    else {
      emit('update:selectUser', {})
      emit('update:selectUserId', '')
      emit('update:selectUsername', '')
    }
  }
}
</script>
