<template>
  <q-dialog
    v-model="rolePermissionVisible"
    position="top"
    @hide="$emit('handleFinish')"
  >
    <q-card style="min-width: 900px; max-width: 50vw">
      <q-card-section>
        <div class="text-h6">{{ $t("admin.Permission") }} {{ row.name }}</div>
      </q-card-section>

      <q-tabs
        v-model="tab"
        dense
        class="text-grey"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
      >
        <q-tab name="menu" :label="$t('admin.Menu') + $t('admin.Permission')" />
        <q-tab name="api" :label="$t('admin.Api') + $t('admin.Permission')" />
      </q-tabs>

      <q-separator />

      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="menu">
          <role-permission-menu v-if="tab === 'menu'" :row="row" />
        </q-tab-panel>

        <q-tab-panel name="api">
          <role-permission-api v-if="tab === 'api'" :row="row" />
        </q-tab-panel>

        <!--        <q-tab-panel name="data">-->
        <!--          <role-permission-data v-if="tab === 'data'" :row="row" />-->
        <!--        </q-tab-panel>-->
      </q-tab-panels>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref } from "vue";
import RolePermissionApi from "./RolePermissionApi.vue";
import RolePermissionMenu from "./RolePermissionMenu.vue";

const rolePermissionVisible = ref(false);
const row = ref({});
const tab = ref("menu");
const show = (record) => {
  row.value = record;
  rolePermissionVisible.value = true;
};

defineExpose({
  show,
});
</script>
