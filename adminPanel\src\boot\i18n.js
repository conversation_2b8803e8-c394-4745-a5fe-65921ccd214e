import { boot } from 'quasar/wrappers'
import { createI18n } from 'vue-i18n'
import messages from 'src/i18n'
import { useSettingStore } from 'src/stores/settings'

export const settingStore = useSettingStore()

export default boot(({ app }) => {
  const i18n = createI18n({
    locale: settingStore.GetLanguage(),
    fallbackLocale: 'zh-CN',
    messages,
    silentTranslationWarn: true,
    silentFallbackWarn: true
  })

  // Set i18n instance on app
  app.use(i18n)
})
