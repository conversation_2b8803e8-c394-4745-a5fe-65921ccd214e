<template>
  <base-content scrollable>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section class="row">
        <div class="text-h6 q-mx-sm col-auto">创建合作项目</div>
        <div class="col">
          <q-btn label="保存提交" color="primary" class="q-mx-sm" @click="handleSaveAction" />
          <q-btn v-close-popup label="取消" color="negative" class="q-mx-sm" @click="closeTab" />
          <q-btn label="生成示例数据" color="positive" class="q-mx-sm" @click="createDemoData" />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input v-model="itemDetail.name" maxlength="250" counter outlined required class="col q-ma-sm"
              label="合作项目名称 *" :rules="[(val) => !!val || '合作项目名称为必填项']" />
          </div>
          <div class="row">
            <q-input v-model="itemDetail.serial" maxlength="250" counter outlined class="col q-ma-sm" label="合作项目编号"
              hint="可以不输入，系统会自动生成" />
            <q-input v-model="itemDetail.contract_type" maxlength="250" counter outlined class="col q-ma-sm"
              label="合作项目类型" />
            <div class="col q-ma-sm">
              <div class="q-mb-sm text-body2">项目状态</div>
              <q-option-group v-model="itemDetail.status" :options="statusDict" color="primary" inline />
            </div>
          </div>
          <div class="row">
            <q-input v-model="itemDetail.sign_time" type="date" outlined class="col q-ma-sm" label="签订时间" />
            <q-input v-model="itemDetail.begin_time" type="date" outlined class="col q-ma-sm" label="项目开始时间" />
            <q-input v-model="itemDetail.end_time" type="date" outlined class="col q-ma-sm" label="项目结束时间" />
          </div>
          <div class="row">
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">申请企业信息</div>
                  <q-option-group v-model="selectSysApplier" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSerialItem v-if="selectSysApplier" v-model:selectItem="selectApplier" selection="single"
                    :can-edit="true" :url="url.company_list" />
                  <q-input v-else v-model="itemDetail.applier" maxlength="250" counter outlined hint="手动输入申请企业名称" />
                </q-card-section>
              </q-card>
            </div>
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">资金提供企业信息</div>
                  <q-option-group v-model="selectSysFunder" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSerialItem v-if="selectSysFunder" v-model:selectItem="selectFunder" selection="single"
                    :can-edit="true" :url="url.company_list" />
                  <q-input v-else v-model="itemDetail.funder" maxlength="250" counter outlined hint="手动输入资金提供企业名称" />
                </q-card-section>
              </q-card>
            </div>
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">业务运营企业信息</div>
                  <q-option-group v-model="selectSysScfCompany" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSerialItem v-if="selectSysScfCompany" v-model:selectItem="selectScfCompany" selection="single"
                    :can-edit="true" :url="url.company_list" />
                  <q-input v-else v-model="itemDetail.scf_company" maxlength="250" counter outlined
                    hint="手动输入业务运营企业名称" />
                </q-card-section>
              </q-card>
            </div>
          </div>
          <div class="row">
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">供应商信息</div>
                  <q-option-group v-model="selectSysSupplier" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSupplier v-if="selectSysSupplier" v-model:selectItem="selectSupplier" selection="single"
                    :can-edit="true" />
                  <q-input v-else v-model="itemDetail.supplier_name" maxlength="250" counter outlined
                    hint="手动输入供应商名称" />
                </q-card-section>
              </q-card>
            </div>
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">物流/仓储方信息</div>
                  <q-option-group v-model="selectSysWarehouse" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSerialItem v-if="selectSysWarehouse" v-model:selectItem="selectWarehouse" selection="single"
                    :can-edit="true" :url="url.warehouse_list" />
                  <q-input v-else v-model="itemDetail.warehouse_name" maxlength="250" counter outlined
                    hint="手动输入物流/仓储方名称" />
                </q-card-section>
              </q-card>
            </div>
          </div>
          <div class="row">
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">中标方信息</div>
                  <q-option-group v-model="selectSysBidWinner" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSerialItem v-if="selectSysBidWinner" v-model:selectItem="selectBidWinner" selection="single"
                    :can-edit="true" :url="url.company_list" />
                  <q-input v-else v-model="itemDetail.bid_winner_name" maxlength="250" counter outlined
                    hint="手动输入中标方名称" />
                </q-card-section>
              </q-card>
            </div>
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">招标方信息</div>
                  <q-option-group v-model="selectSysBidOwner" :options="[
                    { label: '系统选择', value: true },
                    { label: '手动输入', value: false },
                  ]" color="primary" inline class="q-mb-md" />
                  <SelectSerialItem v-if="selectSysBidOwner" v-model:selectItem="selectBidOwner" selection="single"
                    :can-edit="true" :url="url.company_list" />
                  <q-input v-else v-model="itemDetail.bid_owner_name" maxlength="250" counter outlined
                    hint="手动输入招标方名称" />
                </q-card-section>
              </q-card>
            </div>
          </div>
          <div class="row">
            <q-input v-model.number="itemDetail.application_quota" type="number" step="0.01" outlined required
              class="col q-ma-sm" label="申请资金额度 *" :rules="[
                (val) =>
                  (val !== null && val !== undefined && val !== '') ||
                  '申请资金额度为必填项',
              ]" />
            <q-input v-model.number="itemDetail.confirm_quota" type="number" step="0.01" outlined class="col q-ma-sm"
              label="确认资金额度" />
          </div>
          <div class="row">
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">服务费用信息</div>
                  <div class="text-body2 q-mb-xs">计算周期</div>
                  <q-option-group v-model="itemDetail.profit_calc_period" :options="calcPeriodDict" color="primary" inline />
                  <q-input v-model.number="profitCalcFeePercent" type="number" min="0" max="100" outlined
                    hint="输入费用费率（0-100整数，如输入7表示7%即0.07）" :rules="[
                      (val) => (val !== null && val !== undefined && val !== '') || '费用费率为必填项',
                      (val) => (val >= 0 && val <= 100) || '费用费率必须在0-100之间'
                    ]" />
                </q-card-section>
              </q-card>
            </div>
            <div class="col q-ma-sm">
              <q-card class="q-pa-md q-mb-md" bordered>
                <q-card-section class="q-pa-none">
                  <div class="text-subtitle1 q-mb-sm">违约金信息</div>
                  <div class="text-body2 q-mb-xs">计算周期</div>
                  <q-option-group v-model="itemDetail.penalty_calc_period" label="计算周期" :options="calcPeriodDict" color="primary" inline />
                  <q-input v-model.number="penaltyCalcFeePercent" type="number" min="0" max="100" outlined
                    hint="输入违约金费率（0-100整数，如输入7表示7%即0.07）" :rules="[
                      (val) => (val !== null && val !== undefined && val !== '') || '违约金费率为必填项',
                      (val) => (val >= 0 && val <= 100) || '违约金费率必须在0-100之间'
                    ]" />
                </q-card-section>
              </q-card>
            </div>
          </div>
          <div class="row">
            <q-select v-model="itemDetail.product_category" multiple use-chips class="col q-ma-sm" label="商品目录"
              hint="可选择多个商品类别" />
          </div>
          <q-input v-model="itemDetail.desc" type="textarea" maxlength="250" counter outlined label="合作项目补充信息"
            class="q-mx-sm" />
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions class="q-ma-md">
        <q-btn label="保存提交" color="primary" @click="handleSaveAction" />
        <q-btn v-close-popup label="取消" color="negative" @click="closeTab" />
      </q-card-actions>
    </q-card>
  </base-content>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import SelectSerialItem from "src/components/SelectSerialItem/index.vue";
import SelectSupplier from "src/components/SelectSupplier/index.vue";
import { useTagViewStore } from "src/stores/tagView";
import { onMounted, ref, computed } from "vue";
import { useRoute } from "vue-router";

const tabMenuStore = useTagViewStore();
const route = useRoute();
const url = {
  item: "/api/financial_contract",
  create: "/api/financial_contract",
  warehouse_list: "/api/warehouse/list",
  company_list: "/api/company/list",
};

const itemDetail = ref({
  name: "",
  serial: "",
  begin_time: null,
  end_time: null,
  applier: null,
  funder: null,
  scf_company: null,
  contract_type: null,
  status: "draft",
  application_quota: 0.0,
  confirm_quota: 0.0,
  used_quota: 0.0,
  is_delete: false,
  desc: null,
  sign_time: null,
  supplier_id: null,
  supplier_name: null,
  bid_winner_id: null,
  bid_winner_name: null,
  bid_owner_id: null,
  bid_owner_name: null,
  warehouse_id: null,
  warehouse_name: null,
  profit_calc_fee: 0.0,
  penalty_calc_fee: 0.0,
  profit_calc_period: 'ONCE',
  penalty_calc_period: 'ONCE',
  product_category: [],
});
const selectSupplier = ref({});
const selectWarehouse = ref({});
const selectBidWinner = ref({});
const selectBidOwner = ref({});
const selectApplier = ref({});
const selectFunder = ref({});
const selectScfCompany = ref({});

// 控制显示选择组件还是输入框的布尔字段
const selectSysSupplier = ref(true);
const selectSysWarehouse = ref(true);
const selectSysBidWinner = ref(true);
const selectSysBidOwner = ref(true);
const selectSysApplier = ref(true);
const selectSysFunder = ref(true);
const selectSysScfCompany = ref(true);

// 计算属性：将0-100的整数转换为两位小数
const profitCalcFeePercent = computed({
  get: () => {
    // 将小数转换为百分比整数显示（如0.07显示为7）
    return Math.round((itemDetail.value.profit_calc_fee || 0) * 100);
  },
  set: (value) => {
    // 将百分比整数转换为小数存储（如7转换为0.07）
    itemDetail.value.profit_calc_fee = (value || 0) / 100;
  }
});

const penaltyCalcFeePercent = computed({
  get: () => {
    // 将小数转换为百分比整数显示（如0.07显示为7）
    return Math.round((itemDetail.value.penalty_calc_fee || 0) * 100);
  },
  set: (value) => {
    // 将百分比整数转换为小数存储（如7转换为0.07）
    itemDetail.value.penalty_calc_fee = (value || 0) / 100;
  }
});

onMounted(() => { });
const recordDetailForm = ref();

// 项目状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "已完成", value: "done" },
  { label: "已过期", value: "expired" },
];

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" },
];

// 创建示例数据函数
const createDemoData = () => {
  itemDetail.value = {
    name: "智能制造供应链金融合作项目",
    serial: "SCF-2024-001",
    begin_time: "2024-01-01",
    end_time: "2024-12-31",
    applier: "上海智造科技有限公司",
    funder: "中国银行上海分行",
    scf_company: "供应链金融服务有限公司",
    contract_type: "应收账款融资",
    status: "draft",
    application_quota: 5000000.0,
    confirm_quota: 4500000.0,
    used_quota: 0.0,
    is_delete: false,
    desc: "基于智能制造产业链的供应链金融服务项目，为上下游企业提供融资支持，促进产业链协同发展。",
    sign_time: "2024-01-15",
    supplier_id: null,
    supplier_name: "深圳精密制造有限公司",
    bid_winner_id: null,
    bid_winner_name: "华为技术有限公司",
    bid_owner_id: null,
    bid_owner_name: "中国移动通信集团",
    warehouse_id: null,
    warehouse_name: "顺丰物流仓储中心",
    profit_calc_fee: 0.035,
    penalty_calc_fee: 0.05,
    profit_calc_period: "MONTH",
    penalty_calc_period: "DAY",
    product_category: ["电子产品", "通信设备", "智能硬件"],
  };

  Notify.create({
    type: "positive",
    message: "示例数据已填充完成",
  });
};

const handleSaveAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.item) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    if (selectSupplier.value.id) {
      itemDetail.value.supplier_id = selectSupplier.value.id;
      itemDetail.value.supplier_name = selectSupplier.value.name;
    }
    if (selectWarehouse.value.id) {
      itemDetail.value.warehouse_id = selectWarehouse.value.id;
      itemDetail.value.warehouse_name = selectWarehouse.value.name;
    }
    if (selectBidWinner.value.id) {
      itemDetail.value.bid_winner_id = selectBidWinner.value.id;
      itemDetail.value.bid_winner_name = selectBidWinner.value.name;
    }
    if (selectBidOwner.value.id) {
      itemDetail.value.bid_owner_id = selectBidOwner.value.id;
      itemDetail.value.bid_owner_name = selectBidOwner.value.name;
    }
    if (selectApplier.value.id) {
      itemDetail.value.applier_id = selectApplier.value.id;
      itemDetail.value.applier = selectApplier.value.name;
    }
    if (selectFunder.value.id) {
      itemDetail.value.funder_id = selectFunder.value.id;
      itemDetail.value.funder = selectFunder.value.name;
    }
    if (selectScfCompany.value.id) {
      itemDetail.value.scf_company_id = selectScfCompany.value.id;
      itemDetail.value.scf_company = selectScfCompany.value.name;
    }
    const res = await postAction(url.item, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const closeTab = () => {
  tabMenuStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
