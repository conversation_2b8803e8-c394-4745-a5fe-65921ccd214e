<template>
  <q-dialog
    v-model="recordDetailVisible"
    position="right"
    persistent
    :allow-focus-outside="true"
  >
    <q-card style="width: 50vw; max-width: 50vw">
      <q-card-section>
        <div class="text-h6">
          手动导入订单
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <q-form ref="recordDetailForm">
            <div class="row q-my-sm">
              <div class="col">
                <strong>选择关联合同</strong>
                <SelectSerialItem  v-model:selectItem="selectContract" selection="single"
                    :can-edit="true" :url="url.contract" />
              </div>
            </div>
            <div class="row q-my-sm">
              <div class="col">
                <strong>选择订单导入的平台</strong>
                <q-option-group
                  :options="platform"
                  v-model="selectPlatform"
                  inline
                  outlined
                  label="选择订单导入的平台"
                  name="platform"
                />
              </div>
            </div>
            <div class="row q-my-sm">
              <div class="col">
                <strong>输入要导入的工作表名字</strong>
                <q-input v-model="sheetName" outlined label="导入文件的工作表的Sheet名字，不输入就默认为sheet1, 注意大小写！" />
              </div>
            </div>
            <div class="row q-my-sm">
              <div class="col">
                <strong>上传文件</strong>
                <q-file outlined v-model="uploadFile" label="选择要导入的文件" accept=".xlsx,.xls,.csv" />
              </div>
            </div>
          </q-form>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          label="提交"
          color="primary"
          @click="handleCreateAction"
        />
        <q-btn v-close-popup label="取消" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { getAction, postAction, postFormDataAction, putAction } from "src/api/manage";
import useRecordDetail from "src/composables/useRecordDetail";
import SelectSerialItem from "src/components/SelectSerialItem/index.vue";
import {  ref } from "vue";

const $q = useQuasar();
const emit = defineEmits(["handleFinish"]);
const url = {
  contract: "/api/financial_contract/list",
  import: "/api/sales_order/import",
};

const selectContract = ref({});
const selectPlatform = ref('gk');
const sheetName = ref('');
const uploadFile = ref(null);
const recordDetailDialog = ref(null);
const platform = [{ 'label': '广垦商城', 'value': 'gk' }, { 'label': '唯品会', 'value': 'vip' } ]

const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
} = useRecordDetail(url, emit);

const show = async (row) => {
  loading.value = true;
  recordDetailVisible.value = true;
  loading.value = false;
};

defineExpose({
  show,
  formType,
});

const handleCreateAction = async () => {
  if (!selectContract.value || !selectContract.value.id) {
    $q.notify({
      type: 'negative',
      message: '请选择关联合同'
    });
    return;
  }

  if (!uploadFile.value) {
    $q.notify({
      type: 'negative',
      message: '请选择要上传的文件'
    });
    return;
  }

  try {
    loading.value = true;

    const formData = new FormData();
    formData.append('file', uploadFile.value);
    formData.append('contract_id', selectContract.value.id);
    formData.append('contract_name', selectContract.value.name);
    formData.append('platform', selectPlatform.value);
    formData.append('sheet_name', sheetName.value);

    const response = await postFormDataAction(url.import, formData);

    if (response.code === 200) {
      $q.notify({
        type: 'positive',
        message: '文件导入成功'
      });
      recordDetailVisible.value = false;
      emit('handleFinish');
    } else {
      $q.notify({
        type: 'negative',
        message: response.message || '导入失败'
      });
    }
  } catch (error) {
    console.error('导入失败:', error);
    $q.notify({
      type: 'negative',
      message: '导入失败，请重试'
    });
  } finally {
    loading.value = false;
  }
};

</script>
