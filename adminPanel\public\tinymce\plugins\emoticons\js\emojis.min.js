// Source: npm package: emojilib, file:emojis.json
window.tinymce.Resource.add("tinymce.plugins.emoticons", {
  grinning: {
    keywords: ["face", "smile", "happy", "joy", ":D", "grin"],
    char: "\u{1f600}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  grimacing: {
    keywords: ["face", "grimace", "teeth"],
    char: "\u{1f62c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  grin: {
    keywords: ["face", "happy", "smile", "joy", "kawaii"],
    char: "\u{1f601}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  joy: {
    keywords: ["face", "cry", "tears", "weep", "happy", "happytears", "haha"],
    char: "\u{1f602}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  rofl: {
    keywords: ["face", "rolling", "floor", "laughing", "lol", "haha"],
    char: "\u{1f923}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  partying: {
    keywords: ["face", "celebration", "woohoo"],
    char: "\u{1f973}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smiley: {
    keywords: ["face", "happy", "joy", "haha", ":D", ":)", "smile", "funny"],
    char: "\u{1f603}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smile: {
    keywords: [
      "face",
      "happy",
      "joy",
      "funny",
      "haha",
      "laugh",
      "like",
      ":D",
      ":)",
    ],
    char: "\u{1f604}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sweat_smile: {
    keywords: ["face", "hot", "happy", "laugh", "sweat", "smile", "relief"],
    char: "\u{1f605}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  laughing: {
    keywords: [
      "happy",
      "joy",
      "lol",
      "satisfied",
      "haha",
      "face",
      "glad",
      "XD",
      "laugh",
    ],
    char: "\u{1f606}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  innocent: {
    keywords: ["face", "angel", "heaven", "halo"],
    char: "\u{1f607}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  wink: {
    keywords: ["face", "happy", "mischievous", "secret", ";)", "smile", "eye"],
    char: "\u{1f609}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  blush: {
    keywords: [
      "face",
      "smile",
      "happy",
      "flushed",
      "crush",
      "embarrassed",
      "shy",
      "joy",
    ],
    char: "\u{1f60a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  slightly_smiling_face: {
    keywords: ["face", "smile"],
    char: "\u{1f642}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  upside_down_face: {
    keywords: ["face", "flipped", "silly", "smile"],
    char: "\u{1f643}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  relaxed: {
    keywords: ["face", "blush", "massage", "happiness"],
    char: "\u263a\ufe0f",
    fitzpatrick_scale: !1,
    category: "people",
  },
  yum: {
    keywords: [
      "happy",
      "joy",
      "tongue",
      "smile",
      "face",
      "silly",
      "yummy",
      "nom",
      "delicious",
      "savouring",
    ],
    char: "\u{1f60b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  relieved: {
    keywords: ["face", "relaxed", "phew", "massage", "happiness"],
    char: "\u{1f60c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  heart_eyes: {
    keywords: [
      "face",
      "love",
      "like",
      "affection",
      "valentines",
      "infatuation",
      "crush",
      "heart",
    ],
    char: "\u{1f60d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smiling_face_with_three_hearts: {
    keywords: [
      "face",
      "love",
      "like",
      "affection",
      "valentines",
      "infatuation",
      "crush",
      "hearts",
      "adore",
    ],
    char: "\u{1f970}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kissing_heart: {
    keywords: [
      "face",
      "love",
      "like",
      "affection",
      "valentines",
      "infatuation",
      "kiss",
    ],
    char: "\u{1f618}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kissing: {
    keywords: [
      "love",
      "like",
      "face",
      "3",
      "valentines",
      "infatuation",
      "kiss",
    ],
    char: "\u{1f617}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kissing_smiling_eyes: {
    keywords: ["face", "affection", "valentines", "infatuation", "kiss"],
    char: "\u{1f619}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kissing_closed_eyes: {
    keywords: [
      "face",
      "love",
      "like",
      "affection",
      "valentines",
      "infatuation",
      "kiss",
    ],
    char: "\u{1f61a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  stuck_out_tongue_winking_eye: {
    keywords: [
      "face",
      "prank",
      "childish",
      "playful",
      "mischievous",
      "smile",
      "wink",
      "tongue",
    ],
    char: "\u{1f61c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  zany: {
    keywords: ["face", "goofy", "crazy"],
    char: "\u{1f92a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  raised_eyebrow: {
    keywords: [
      "face",
      "distrust",
      "scepticism",
      "disapproval",
      "disbelief",
      "surprise",
    ],
    char: "\u{1f928}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  monocle: {
    keywords: ["face", "stuffy", "wealthy"],
    char: "\u{1f9d0}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  stuck_out_tongue_closed_eyes: {
    keywords: ["face", "prank", "playful", "mischievous", "smile", "tongue"],
    char: "\u{1f61d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  stuck_out_tongue: {
    keywords: [
      "face",
      "prank",
      "childish",
      "playful",
      "mischievous",
      "smile",
      "tongue",
    ],
    char: "\u{1f61b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  money_mouth_face: {
    keywords: ["face", "rich", "dollar", "money"],
    char: "\u{1f911}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  nerd_face: {
    keywords: ["face", "nerdy", "geek", "dork"],
    char: "\u{1f913}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sunglasses: {
    keywords: ["face", "cool", "smile", "summer", "beach", "sunglass"],
    char: "\u{1f60e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  star_struck: {
    keywords: ["face", "smile", "starry", "eyes", "grinning"],
    char: "\u{1f929}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  clown_face: {
    keywords: ["face"],
    char: "\u{1f921}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  cowboy_hat_face: {
    keywords: ["face", "cowgirl", "hat"],
    char: "\u{1f920}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  hugs: {
    keywords: ["face", "smile", "hug"],
    char: "\u{1f917}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smirk: {
    keywords: ["face", "smile", "mean", "prank", "smug", "sarcasm"],
    char: "\u{1f60f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  no_mouth: {
    keywords: ["face", "hellokitty"],
    char: "\u{1f636}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  neutral_face: {
    keywords: ["indifference", "meh", ":|", "neutral"],
    char: "\u{1f610}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  expressionless: {
    keywords: ["face", "indifferent", "-_-", "meh", "deadpan"],
    char: "\u{1f611}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  unamused: {
    keywords: [
      "indifference",
      "bored",
      "straight face",
      "serious",
      "sarcasm",
      "unimpressed",
      "skeptical",
      "dubious",
      "side_eye",
    ],
    char: "\u{1f612}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  roll_eyes: {
    keywords: ["face", "eyeroll", "frustrated"],
    char: "\u{1f644}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  thinking: {
    keywords: ["face", "hmmm", "think", "consider"],
    char: "\u{1f914}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  lying_face: {
    keywords: ["face", "lie", "pinocchio"],
    char: "\u{1f925}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  hand_over_mouth: {
    keywords: ["face", "whoops", "shock", "surprise"],
    char: "\u{1f92d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  shushing: {
    keywords: ["face", "quiet", "shhh"],
    char: "\u{1f92b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  symbols_over_mouth: {
    keywords: [
      "face",
      "swearing",
      "cursing",
      "cussing",
      "profanity",
      "expletive",
    ],
    char: "\u{1f92c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  exploding_head: {
    keywords: ["face", "shocked", "mind", "blown"],
    char: "\u{1f92f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  flushed: {
    keywords: ["face", "blush", "shy", "flattered"],
    char: "\u{1f633}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  disappointed: {
    keywords: ["face", "sad", "upset", "depressed", ":("],
    char: "\u{1f61e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  worried: {
    keywords: ["face", "concern", "nervous", ":("],
    char: "\u{1f61f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  angry: {
    keywords: ["mad", "face", "annoyed", "frustrated"],
    char: "\u{1f620}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  rage: {
    keywords: ["angry", "mad", "hate", "despise"],
    char: "\u{1f621}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  pensive: {
    keywords: ["face", "sad", "depressed", "upset"],
    char: "\u{1f614}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  confused: {
    keywords: ["face", "indifference", "huh", "weird", "hmmm", ":/"],
    char: "\u{1f615}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  slightly_frowning_face: {
    keywords: ["face", "frowning", "disappointed", "sad", "upset"],
    char: "\u{1f641}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  frowning_face: {
    keywords: ["face", "sad", "upset", "frown"],
    char: "\u2639",
    fitzpatrick_scale: !1,
    category: "people",
  },
  persevere: {
    keywords: ["face", "sick", "no", "upset", "oops"],
    char: "\u{1f623}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  confounded: {
    keywords: ["face", "confused", "sick", "unwell", "oops", ":S"],
    char: "\u{1f616}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  tired_face: {
    keywords: ["sick", "whine", "upset", "frustrated"],
    char: "\u{1f62b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  weary: {
    keywords: ["face", "tired", "sleepy", "sad", "frustrated", "upset"],
    char: "\u{1f629}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  pleading: {
    keywords: ["face", "begging", "mercy"],
    char: "\u{1f97a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  triumph: {
    keywords: ["face", "gas", "phew", "proud", "pride"],
    char: "\u{1f624}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  open_mouth: {
    keywords: ["face", "surprise", "impressed", "wow", "whoa", ":O"],
    char: "\u{1f62e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  scream: {
    keywords: ["face", "munch", "scared", "omg"],
    char: "\u{1f631}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  fearful: {
    keywords: ["face", "scared", "terrified", "nervous", "oops", "huh"],
    char: "\u{1f628}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  cold_sweat: {
    keywords: ["face", "nervous", "sweat"],
    char: "\u{1f630}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  hushed: {
    keywords: ["face", "woo", "shh"],
    char: "\u{1f62f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  frowning: {
    keywords: ["face", "aw", "what"],
    char: "\u{1f626}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  anguished: {
    keywords: ["face", "stunned", "nervous"],
    char: "\u{1f627}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  cry: {
    keywords: ["face", "tears", "sad", "depressed", "upset", ":'("],
    char: "\u{1f622}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  disappointed_relieved: {
    keywords: ["face", "phew", "sweat", "nervous"],
    char: "\u{1f625}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  drooling_face: {
    keywords: ["face"],
    char: "\u{1f924}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sleepy: {
    keywords: ["face", "tired", "rest", "nap"],
    char: "\u{1f62a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sweat: {
    keywords: ["face", "hot", "sad", "tired", "exercise"],
    char: "\u{1f613}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  hot: {
    keywords: ["face", "feverish", "heat", "red", "sweating"],
    char: "\u{1f975}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  cold: {
    keywords: ["face", "blue", "freezing", "frozen", "frostbite", "icicles"],
    char: "\u{1f976}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sob: {
    keywords: ["face", "cry", "tears", "sad", "upset", "depressed"],
    char: "\u{1f62d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  dizzy_face: {
    keywords: ["spent", "unconscious", "xox", "dizzy"],
    char: "\u{1f635}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  astonished: {
    keywords: ["face", "xox", "surprised", "poisoned"],
    char: "\u{1f632}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  zipper_mouth_face: {
    keywords: ["face", "sealed", "zipper", "secret"],
    char: "\u{1f910}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  nauseated_face: {
    keywords: ["face", "vomit", "gross", "green", "sick", "throw up", "ill"],
    char: "\u{1f922}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sneezing_face: {
    keywords: ["face", "gesundheit", "sneeze", "sick", "allergy"],
    char: "\u{1f927}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  vomiting: {
    keywords: ["face", "sick"],
    char: "\u{1f92e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  mask: {
    keywords: ["face", "sick", "ill", "disease"],
    char: "\u{1f637}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  face_with_thermometer: {
    keywords: ["sick", "temperature", "thermometer", "cold", "fever"],
    char: "\u{1f912}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  face_with_head_bandage: {
    keywords: ["injured", "clumsy", "bandage", "hurt"],
    char: "\u{1f915}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  woozy: {
    keywords: ["face", "dizzy", "intoxicated", "tipsy", "wavy"],
    char: "\u{1f974}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sleeping: {
    keywords: ["face", "tired", "sleepy", "night", "zzz"],
    char: "\u{1f634}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  zzz: {
    keywords: ["sleepy", "tired", "dream"],
    char: "\u{1f4a4}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  poop: {
    keywords: ["hankey", "shitface", "fail", "turd", "shit"],
    char: "\u{1f4a9}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smiling_imp: {
    keywords: ["devil", "horns"],
    char: "\u{1f608}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  imp: {
    keywords: ["devil", "angry", "horns"],
    char: "\u{1f47f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  japanese_ogre: {
    keywords: [
      "monster",
      "red",
      "mask",
      "halloween",
      "scary",
      "creepy",
      "devil",
      "demon",
      "japanese",
      "ogre",
    ],
    char: "\u{1f479}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  japanese_goblin: {
    keywords: [
      "red",
      "evil",
      "mask",
      "monster",
      "scary",
      "creepy",
      "japanese",
      "goblin",
    ],
    char: "\u{1f47a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  skull: {
    keywords: ["dead", "skeleton", "creepy", "death"],
    char: "\u{1f480}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  ghost: {
    keywords: ["halloween", "spooky", "scary"],
    char: "\u{1f47b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  alien: {
    keywords: ["UFO", "paul", "weird", "outer_space"],
    char: "\u{1f47d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  robot: {
    keywords: ["computer", "machine", "bot"],
    char: "\u{1f916}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smiley_cat: {
    keywords: ["animal", "cats", "happy", "smile"],
    char: "\u{1f63a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smile_cat: {
    keywords: ["animal", "cats", "smile"],
    char: "\u{1f638}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  joy_cat: {
    keywords: ["animal", "cats", "haha", "happy", "tears"],
    char: "\u{1f639}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  heart_eyes_cat: {
    keywords: [
      "animal",
      "love",
      "like",
      "affection",
      "cats",
      "valentines",
      "heart",
    ],
    char: "\u{1f63b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  smirk_cat: {
    keywords: ["animal", "cats", "smirk"],
    char: "\u{1f63c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kissing_cat: {
    keywords: ["animal", "cats", "kiss"],
    char: "\u{1f63d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  scream_cat: {
    keywords: ["animal", "cats", "munch", "scared", "scream"],
    char: "\u{1f640}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  crying_cat_face: {
    keywords: ["animal", "tears", "weep", "sad", "cats", "upset", "cry"],
    char: "\u{1f63f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  pouting_cat: {
    keywords: ["animal", "cats"],
    char: "\u{1f63e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  palms_up: {
    keywords: ["hands", "gesture", "cupped", "prayer"],
    char: "\u{1f932}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  raised_hands: {
    keywords: ["gesture", "hooray", "yea", "celebration", "hands"],
    char: "\u{1f64c}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  clap: {
    keywords: ["hands", "praise", "applause", "congrats", "yay"],
    char: "\u{1f44f}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  wave: {
    keywords: [
      "hands",
      "gesture",
      "goodbye",
      "solong",
      "farewell",
      "hello",
      "hi",
      "palm",
    ],
    char: "\u{1f44b}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  call_me_hand: {
    keywords: ["hands", "gesture"],
    char: "\u{1f919}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  "+1": {
    keywords: [
      "thumbsup",
      "yes",
      "awesome",
      "good",
      "agree",
      "accept",
      "cool",
      "hand",
      "like",
    ],
    char: "\u{1f44d}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  "-1": {
    keywords: ["thumbsdown", "no", "dislike", "hand"],
    char: "\u{1f44e}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  facepunch: {
    keywords: ["angry", "violence", "fist", "hit", "attack", "hand"],
    char: "\u{1f44a}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  fist: {
    keywords: ["fingers", "hand", "grasp"],
    char: "\u270a",
    fitzpatrick_scale: !0,
    category: "people",
  },
  fist_left: {
    keywords: ["hand", "fistbump"],
    char: "\u{1f91b}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  fist_right: {
    keywords: ["hand", "fistbump"],
    char: "\u{1f91c}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  v: {
    keywords: ["fingers", "ohyeah", "hand", "peace", "victory", "two"],
    char: "\u270c",
    fitzpatrick_scale: !0,
    category: "people",
  },
  ok_hand: {
    keywords: ["fingers", "limbs", "perfect", "ok", "okay"],
    char: "\u{1f44c}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  raised_hand: {
    keywords: ["fingers", "stop", "highfive", "palm", "ban"],
    char: "\u270b",
    fitzpatrick_scale: !0,
    category: "people",
  },
  raised_back_of_hand: {
    keywords: ["fingers", "raised", "backhand"],
    char: "\u{1f91a}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  open_hands: {
    keywords: ["fingers", "butterfly", "hands", "open"],
    char: "\u{1f450}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  muscle: {
    keywords: ["arm", "flex", "hand", "summer", "strong", "biceps"],
    char: "\u{1f4aa}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  pray: {
    keywords: ["please", "hope", "wish", "namaste", "highfive"],
    char: "\u{1f64f}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  foot: {
    keywords: ["kick", "stomp"],
    char: "\u{1f9b6}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  leg: {
    keywords: ["kick", "limb"],
    char: "\u{1f9b5}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  handshake: {
    keywords: ["agreement", "shake"],
    char: "\u{1f91d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  point_up: {
    keywords: ["hand", "fingers", "direction", "up"],
    char: "\u261d",
    fitzpatrick_scale: !0,
    category: "people",
  },
  point_up_2: {
    keywords: ["fingers", "hand", "direction", "up"],
    char: "\u{1f446}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  point_down: {
    keywords: ["fingers", "hand", "direction", "down"],
    char: "\u{1f447}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  point_left: {
    keywords: ["direction", "fingers", "hand", "left"],
    char: "\u{1f448}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  point_right: {
    keywords: ["fingers", "hand", "direction", "right"],
    char: "\u{1f449}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  fu: {
    keywords: ["hand", "fingers", "rude", "middle", "flipping"],
    char: "\u{1f595}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  raised_hand_with_fingers_splayed: {
    keywords: ["hand", "fingers", "palm"],
    char: "\u{1f590}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  love_you: {
    keywords: ["hand", "fingers", "gesture"],
    char: "\u{1f91f}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  metal: {
    keywords: ["hand", "fingers", "evil_eye", "sign_of_horns", "rock_on"],
    char: "\u{1f918}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  crossed_fingers: {
    keywords: ["good", "lucky"],
    char: "\u{1f91e}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  vulcan_salute: {
    keywords: ["hand", "fingers", "spock", "star trek"],
    char: "\u{1f596}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  writing_hand: {
    keywords: ["lower_left_ballpoint_pen", "stationery", "write", "compose"],
    char: "\u270d",
    fitzpatrick_scale: !0,
    category: "people",
  },
  selfie: {
    keywords: ["camera", "phone"],
    char: "\u{1f933}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  nail_care: {
    keywords: ["beauty", "manicure", "finger", "fashion", "nail"],
    char: "\u{1f485}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  lips: {
    keywords: ["mouth", "kiss"],
    char: "\u{1f444}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  tooth: {
    keywords: ["teeth", "dentist"],
    char: "\u{1f9b7}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  tongue: {
    keywords: ["mouth", "playful"],
    char: "\u{1f445}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  ear: {
    keywords: ["face", "hear", "sound", "listen"],
    char: "\u{1f442}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  nose: {
    keywords: ["smell", "sniff"],
    char: "\u{1f443}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  eye: {
    keywords: ["face", "look", "see", "watch", "stare"],
    char: "\u{1f441}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  eyes: {
    keywords: ["look", "watch", "stalk", "peek", "see"],
    char: "\u{1f440}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  brain: {
    keywords: ["smart", "intelligent"],
    char: "\u{1f9e0}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  bust_in_silhouette: {
    keywords: ["user", "person", "human"],
    char: "\u{1f464}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  busts_in_silhouette: {
    keywords: ["user", "person", "human", "group", "team"],
    char: "\u{1f465}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  speaking_head: {
    keywords: ["user", "person", "human", "sing", "say", "talk"],
    char: "\u{1f5e3}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  baby: {
    keywords: ["child", "boy", "girl", "toddler"],
    char: "\u{1f476}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  child: {
    keywords: ["gender-neutral", "young"],
    char: "\u{1f9d2}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  boy: {
    keywords: ["man", "male", "guy", "teenager"],
    char: "\u{1f466}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  girl: {
    keywords: ["female", "woman", "teenager"],
    char: "\u{1f467}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  adult: {
    keywords: ["gender-neutral", "person"],
    char: "\u{1f9d1}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man: {
    keywords: [
      "mustache",
      "father",
      "dad",
      "guy",
      "classy",
      "sir",
      "moustache",
    ],
    char: "\u{1f468}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman: {
    keywords: ["female", "girls", "lady"],
    char: "\u{1f469}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  blonde_woman: {
    keywords: ["woman", "female", "girl", "blonde", "person"],
    char: "\u{1f471}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  blonde_man: {
    keywords: ["man", "male", "boy", "blonde", "guy", "person"],
    char: "\u{1f471}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  bearded_person: {
    keywords: ["person", "bewhiskered"],
    char: "\u{1f9d4}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  older_adult: {
    keywords: ["human", "elder", "senior", "gender-neutral"],
    char: "\u{1f9d3}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  older_man: {
    keywords: ["human", "male", "men", "old", "elder", "senior"],
    char: "\u{1f474}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  older_woman: {
    keywords: ["human", "female", "women", "lady", "old", "elder", "senior"],
    char: "\u{1f475}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_with_gua_pi_mao: {
    keywords: ["male", "boy", "chinese"],
    char: "\u{1f472}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_with_headscarf: {
    keywords: ["female", "hijab", "mantilla", "tichel"],
    char: "\u{1f9d5}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_with_turban: {
    keywords: ["female", "indian", "hinduism", "arabs", "woman"],
    char: "\u{1f473}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_with_turban: {
    keywords: ["male", "indian", "hinduism", "arabs"],
    char: "\u{1f473}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  policewoman: {
    keywords: [
      "woman",
      "police",
      "law",
      "legal",
      "enforcement",
      "arrest",
      "911",
      "female",
    ],
    char: "\u{1f46e}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  policeman: {
    keywords: ["man", "police", "law", "legal", "enforcement", "arrest", "911"],
    char: "\u{1f46e}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  construction_worker_woman: {
    keywords: [
      "female",
      "human",
      "wip",
      "build",
      "construction",
      "worker",
      "labor",
      "woman",
    ],
    char: "\u{1f477}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  construction_worker_man: {
    keywords: [
      "male",
      "human",
      "wip",
      "guy",
      "build",
      "construction",
      "worker",
      "labor",
    ],
    char: "\u{1f477}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  guardswoman: {
    keywords: ["uk", "gb", "british", "female", "royal", "woman"],
    char: "\u{1f482}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  guardsman: {
    keywords: ["uk", "gb", "british", "male", "guy", "royal"],
    char: "\u{1f482}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  female_detective: {
    keywords: ["human", "spy", "detective", "female", "woman"],
    char: "\u{1f575}\ufe0f\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  male_detective: {
    keywords: ["human", "spy", "detective"],
    char: "\u{1f575}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_health_worker: {
    keywords: ["doctor", "nurse", "therapist", "healthcare", "woman", "human"],
    char: "\u{1f469}\u200d\u2695\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_health_worker: {
    keywords: ["doctor", "nurse", "therapist", "healthcare", "man", "human"],
    char: "\u{1f468}\u200d\u2695\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_farmer: {
    keywords: ["rancher", "gardener", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f33e}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_farmer: {
    keywords: ["rancher", "gardener", "man", "human"],
    char: "\u{1f468}\u200d\u{1f33e}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_cook: {
    keywords: ["chef", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f373}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_cook: {
    keywords: ["chef", "man", "human"],
    char: "\u{1f468}\u200d\u{1f373}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_student: {
    keywords: ["graduate", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f393}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_student: {
    keywords: ["graduate", "man", "human"],
    char: "\u{1f468}\u200d\u{1f393}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_singer: {
    keywords: ["rockstar", "entertainer", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f3a4}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_singer: {
    keywords: ["rockstar", "entertainer", "man", "human"],
    char: "\u{1f468}\u200d\u{1f3a4}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_teacher: {
    keywords: ["instructor", "professor", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f3eb}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_teacher: {
    keywords: ["instructor", "professor", "man", "human"],
    char: "\u{1f468}\u200d\u{1f3eb}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_factory_worker: {
    keywords: ["assembly", "industrial", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f3ed}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_factory_worker: {
    keywords: ["assembly", "industrial", "man", "human"],
    char: "\u{1f468}\u200d\u{1f3ed}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_technologist: {
    keywords: [
      "coder",
      "developer",
      "engineer",
      "programmer",
      "software",
      "woman",
      "human",
      "laptop",
      "computer",
    ],
    char: "\u{1f469}\u200d\u{1f4bb}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_technologist: {
    keywords: [
      "coder",
      "developer",
      "engineer",
      "programmer",
      "software",
      "man",
      "human",
      "laptop",
      "computer",
    ],
    char: "\u{1f468}\u200d\u{1f4bb}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_office_worker: {
    keywords: ["business", "manager", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f4bc}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_office_worker: {
    keywords: ["business", "manager", "man", "human"],
    char: "\u{1f468}\u200d\u{1f4bc}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_mechanic: {
    keywords: ["plumber", "woman", "human", "wrench"],
    char: "\u{1f469}\u200d\u{1f527}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_mechanic: {
    keywords: ["plumber", "man", "human", "wrench"],
    char: "\u{1f468}\u200d\u{1f527}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_scientist: {
    keywords: [
      "biologist",
      "chemist",
      "engineer",
      "physicist",
      "woman",
      "human",
    ],
    char: "\u{1f469}\u200d\u{1f52c}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_scientist: {
    keywords: ["biologist", "chemist", "engineer", "physicist", "man", "human"],
    char: "\u{1f468}\u200d\u{1f52c}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_artist: {
    keywords: ["painter", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f3a8}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_artist: {
    keywords: ["painter", "man", "human"],
    char: "\u{1f468}\u200d\u{1f3a8}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_firefighter: {
    keywords: ["fireman", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f692}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_firefighter: {
    keywords: ["fireman", "man", "human"],
    char: "\u{1f468}\u200d\u{1f692}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_pilot: {
    keywords: ["aviator", "plane", "woman", "human"],
    char: "\u{1f469}\u200d\u2708\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_pilot: {
    keywords: ["aviator", "plane", "man", "human"],
    char: "\u{1f468}\u200d\u2708\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_astronaut: {
    keywords: ["space", "rocket", "woman", "human"],
    char: "\u{1f469}\u200d\u{1f680}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_astronaut: {
    keywords: ["space", "rocket", "man", "human"],
    char: "\u{1f468}\u200d\u{1f680}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_judge: {
    keywords: ["justice", "court", "woman", "human"],
    char: "\u{1f469}\u200d\u2696\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_judge: {
    keywords: ["justice", "court", "man", "human"],
    char: "\u{1f468}\u200d\u2696\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_superhero: {
    keywords: ["woman", "female", "good", "heroine", "superpowers"],
    char: "\u{1f9b8}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_superhero: {
    keywords: ["man", "male", "good", "hero", "superpowers"],
    char: "\u{1f9b8}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_supervillain: {
    keywords: [
      "woman",
      "female",
      "evil",
      "bad",
      "criminal",
      "heroine",
      "superpowers",
    ],
    char: "\u{1f9b9}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_supervillain: {
    keywords: ["man", "male", "evil", "bad", "criminal", "hero", "superpowers"],
    char: "\u{1f9b9}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  mrs_claus: {
    keywords: ["woman", "female", "xmas", "mother christmas"],
    char: "\u{1f936}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  santa: {
    keywords: ["festival", "man", "male", "xmas", "father christmas"],
    char: "\u{1f385}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  sorceress: {
    keywords: ["woman", "female", "mage", "witch"],
    char: "\u{1f9d9}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  wizard: {
    keywords: ["man", "male", "mage", "sorcerer"],
    char: "\u{1f9d9}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_elf: {
    keywords: ["woman", "female"],
    char: "\u{1f9dd}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_elf: {
    keywords: ["man", "male"],
    char: "\u{1f9dd}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_vampire: {
    keywords: ["woman", "female"],
    char: "\u{1f9db}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_vampire: {
    keywords: ["man", "male", "dracula"],
    char: "\u{1f9db}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_zombie: {
    keywords: ["woman", "female", "undead", "walking dead"],
    char: "\u{1f9df}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !1,
    category: "people",
  },
  man_zombie: {
    keywords: ["man", "male", "dracula", "undead", "walking dead"],
    char: "\u{1f9df}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !1,
    category: "people",
  },
  woman_genie: {
    keywords: ["woman", "female"],
    char: "\u{1f9de}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !1,
    category: "people",
  },
  man_genie: {
    keywords: ["man", "male"],
    char: "\u{1f9de}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !1,
    category: "people",
  },
  mermaid: {
    keywords: ["woman", "female", "merwoman", "ariel"],
    char: "\u{1f9dc}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  merman: {
    keywords: ["man", "male", "triton"],
    char: "\u{1f9dc}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_fairy: {
    keywords: ["woman", "female"],
    char: "\u{1f9da}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_fairy: {
    keywords: ["man", "male"],
    char: "\u{1f9da}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  angel: {
    keywords: ["heaven", "wings", "halo"],
    char: "\u{1f47c}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  pregnant_woman: {
    keywords: ["baby"],
    char: "\u{1f930}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  breastfeeding: {
    keywords: ["nursing", "baby"],
    char: "\u{1f931}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  princess: {
    keywords: ["girl", "woman", "female", "blond", "crown", "royal", "queen"],
    char: "\u{1f478}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  prince: {
    keywords: ["boy", "man", "male", "crown", "royal", "king"],
    char: "\u{1f934}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  bride_with_veil: {
    keywords: ["couple", "marriage", "wedding", "woman", "bride"],
    char: "\u{1f470}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_in_tuxedo: {
    keywords: ["couple", "marriage", "wedding", "groom"],
    char: "\u{1f935}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  running_woman: {
    keywords: ["woman", "walking", "exercise", "race", "running", "female"],
    char: "\u{1f3c3}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  running_man: {
    keywords: ["man", "walking", "exercise", "race", "running"],
    char: "\u{1f3c3}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  walking_woman: {
    keywords: ["human", "feet", "steps", "woman", "female"],
    char: "\u{1f6b6}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  walking_man: {
    keywords: ["human", "feet", "steps"],
    char: "\u{1f6b6}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  dancer: {
    keywords: ["female", "girl", "woman", "fun"],
    char: "\u{1f483}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_dancing: {
    keywords: ["male", "boy", "fun", "dancer"],
    char: "\u{1f57a}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  dancing_women: {
    keywords: ["female", "bunny", "women", "girls"],
    char: "\u{1f46f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  dancing_men: {
    keywords: ["male", "bunny", "men", "boys"],
    char: "\u{1f46f}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !1,
    category: "people",
  },
  couple: {
    keywords: [
      "pair",
      "people",
      "human",
      "love",
      "date",
      "dating",
      "like",
      "affection",
      "valentines",
      "marriage",
    ],
    char: "\u{1f46b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  two_men_holding_hands: {
    keywords: [
      "pair",
      "couple",
      "love",
      "like",
      "bromance",
      "friendship",
      "people",
      "human",
    ],
    char: "\u{1f46c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  two_women_holding_hands: {
    keywords: [
      "pair",
      "friendship",
      "couple",
      "love",
      "like",
      "female",
      "people",
      "human",
    ],
    char: "\u{1f46d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  bowing_woman: {
    keywords: ["woman", "female", "girl"],
    char: "\u{1f647}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  bowing_man: {
    keywords: ["man", "male", "boy"],
    char: "\u{1f647}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_facepalming: {
    keywords: ["man", "male", "boy", "disbelief"],
    char: "\u{1f926}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_facepalming: {
    keywords: ["woman", "female", "girl", "disbelief"],
    char: "\u{1f926}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_shrugging: {
    keywords: ["woman", "female", "girl", "confused", "indifferent", "doubt"],
    char: "\u{1f937}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_shrugging: {
    keywords: ["man", "male", "boy", "confused", "indifferent", "doubt"],
    char: "\u{1f937}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  tipping_hand_woman: {
    keywords: ["female", "girl", "woman", "human", "information"],
    char: "\u{1f481}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  tipping_hand_man: {
    keywords: ["male", "boy", "man", "human", "information"],
    char: "\u{1f481}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  no_good_woman: {
    keywords: ["female", "girl", "woman", "nope"],
    char: "\u{1f645}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  no_good_man: {
    keywords: ["male", "boy", "man", "nope"],
    char: "\u{1f645}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  ok_woman: {
    keywords: ["women", "girl", "female", "pink", "human", "woman"],
    char: "\u{1f646}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  ok_man: {
    keywords: ["men", "boy", "male", "blue", "human", "man"],
    char: "\u{1f646}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  raising_hand_woman: {
    keywords: ["female", "girl", "woman"],
    char: "\u{1f64b}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  raising_hand_man: {
    keywords: ["male", "boy", "man"],
    char: "\u{1f64b}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  pouting_woman: {
    keywords: ["female", "girl", "woman"],
    char: "\u{1f64e}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  pouting_man: {
    keywords: ["male", "boy", "man"],
    char: "\u{1f64e}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  frowning_woman: {
    keywords: [
      "female",
      "girl",
      "woman",
      "sad",
      "depressed",
      "discouraged",
      "unhappy",
    ],
    char: "\u{1f64d}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  frowning_man: {
    keywords: [
      "male",
      "boy",
      "man",
      "sad",
      "depressed",
      "discouraged",
      "unhappy",
    ],
    char: "\u{1f64d}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  haircut_woman: {
    keywords: ["female", "girl", "woman"],
    char: "\u{1f487}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  haircut_man: {
    keywords: ["male", "boy", "man"],
    char: "\u{1f487}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  massage_woman: {
    keywords: ["female", "girl", "woman", "head"],
    char: "\u{1f486}",
    fitzpatrick_scale: !0,
    category: "people",
  },
  massage_man: {
    keywords: ["male", "boy", "man", "head"],
    char: "\u{1f486}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  woman_in_steamy_room: {
    keywords: ["female", "woman", "spa", "steamroom", "sauna"],
    char: "\u{1f9d6}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  man_in_steamy_room: {
    keywords: ["male", "man", "spa", "steamroom", "sauna"],
    char: "\u{1f9d6}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "people",
  },
  couple_with_heart_woman_man: {
    keywords: [
      "pair",
      "love",
      "like",
      "affection",
      "human",
      "dating",
      "valentines",
      "marriage",
    ],
    char: "\u{1f491}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  couple_with_heart_woman_woman: {
    keywords: [
      "pair",
      "love",
      "like",
      "affection",
      "human",
      "dating",
      "valentines",
      "marriage",
    ],
    char: "\u{1f469}\u200d\u2764\ufe0f\u200d\u{1f469}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  couple_with_heart_man_man: {
    keywords: [
      "pair",
      "love",
      "like",
      "affection",
      "human",
      "dating",
      "valentines",
      "marriage",
    ],
    char: "\u{1f468}\u200d\u2764\ufe0f\u200d\u{1f468}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  couplekiss_man_woman: {
    keywords: ["pair", "valentines", "love", "like", "dating", "marriage"],
    char: "\u{1f48f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  couplekiss_woman_woman: {
    keywords: ["pair", "valentines", "love", "like", "dating", "marriage"],
    char: "\u{1f469}\u200d\u2764\ufe0f\u200d\u{1f48b}\u200d\u{1f469}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  couplekiss_man_man: {
    keywords: ["pair", "valentines", "love", "like", "dating", "marriage"],
    char: "\u{1f468}\u200d\u2764\ufe0f\u200d\u{1f48b}\u200d\u{1f468}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_woman_boy: {
    keywords: [
      "home",
      "parents",
      "child",
      "mom",
      "dad",
      "father",
      "mother",
      "people",
      "human",
    ],
    char: "\u{1f46a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_woman_girl: {
    keywords: ["home", "parents", "people", "human", "child"],
    char: "\u{1f468}\u200d\u{1f469}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_woman_girl_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f469}\u200d\u{1f467}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_woman_boy_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f469}\u200d\u{1f466}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_woman_girl_girl: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f469}\u200d\u{1f467}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_woman_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f469}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_woman_girl: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f469}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_woman_girl_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f469}\u200d\u{1f467}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_woman_boy_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f469}\u200d\u{1f466}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_woman_girl_girl: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f469}\u200d\u{1f467}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_man_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f468}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_man_girl: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f468}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_man_girl_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f468}\u200d\u{1f467}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_man_boy_boy: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f468}\u200d\u{1f466}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_man_girl_girl: {
    keywords: ["home", "parents", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f468}\u200d\u{1f467}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_boy: {
    keywords: ["home", "parent", "people", "human", "child"],
    char: "\u{1f469}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_girl: {
    keywords: ["home", "parent", "people", "human", "child"],
    char: "\u{1f469}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_girl_boy: {
    keywords: ["home", "parent", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f467}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_boy_boy: {
    keywords: ["home", "parent", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f466}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_woman_girl_girl: {
    keywords: ["home", "parent", "people", "human", "children"],
    char: "\u{1f469}\u200d\u{1f467}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_boy: {
    keywords: ["home", "parent", "people", "human", "child"],
    char: "\u{1f468}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_girl: {
    keywords: ["home", "parent", "people", "human", "child"],
    char: "\u{1f468}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_girl_boy: {
    keywords: ["home", "parent", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f467}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_boy_boy: {
    keywords: ["home", "parent", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f466}\u200d\u{1f466}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  family_man_girl_girl: {
    keywords: ["home", "parent", "people", "human", "children"],
    char: "\u{1f468}\u200d\u{1f467}\u200d\u{1f467}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  yarn: {
    keywords: ["ball", "crochet", "knit"],
    char: "\u{1f9f6}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  thread: {
    keywords: ["needle", "sewing", "spool", "string"],
    char: "\u{1f9f5}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  coat: {
    keywords: ["jacket"],
    char: "\u{1f9e5}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  labcoat: {
    keywords: ["doctor", "experiment", "scientist", "chemist"],
    char: "\u{1f97c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  womans_clothes: {
    keywords: ["fashion", "shopping_bags", "female"],
    char: "\u{1f45a}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  tshirt: {
    keywords: ["fashion", "cloth", "casual", "shirt", "tee"],
    char: "\u{1f455}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  jeans: {
    keywords: ["fashion", "shopping"],
    char: "\u{1f456}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  necktie: {
    keywords: ["shirt", "suitup", "formal", "fashion", "cloth", "business"],
    char: "\u{1f454}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  dress: {
    keywords: ["clothes", "fashion", "shopping"],
    char: "\u{1f457}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  bikini: {
    keywords: [
      "swimming",
      "female",
      "woman",
      "girl",
      "fashion",
      "beach",
      "summer",
    ],
    char: "\u{1f459}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kimono: {
    keywords: ["dress", "fashion", "women", "female", "japanese"],
    char: "\u{1f458}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  lipstick: {
    keywords: ["female", "girl", "fashion", "woman"],
    char: "\u{1f484}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  kiss: {
    keywords: ["face", "lips", "love", "like", "affection", "valentines"],
    char: "\u{1f48b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  footprints: {
    keywords: ["feet", "tracking", "walking", "beach"],
    char: "\u{1f463}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  flat_shoe: {
    keywords: ["ballet", "slip-on", "slipper"],
    char: "\u{1f97f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  high_heel: {
    keywords: ["fashion", "shoes", "female", "pumps", "stiletto"],
    char: "\u{1f460}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  sandal: {
    keywords: ["shoes", "fashion", "flip flops"],
    char: "\u{1f461}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  boot: {
    keywords: ["shoes", "fashion"],
    char: "\u{1f462}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  mans_shoe: {
    keywords: ["fashion", "male"],
    char: "\u{1f45e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  athletic_shoe: {
    keywords: ["shoes", "sports", "sneakers"],
    char: "\u{1f45f}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  hiking_boot: {
    keywords: ["backpacking", "camping", "hiking"],
    char: "\u{1f97e}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  socks: {
    keywords: ["stockings", "clothes"],
    char: "\u{1f9e6}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  gloves: {
    keywords: ["hands", "winter", "clothes"],
    char: "\u{1f9e4}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  scarf: {
    keywords: ["neck", "winter", "clothes"],
    char: "\u{1f9e3}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  womans_hat: {
    keywords: ["fashion", "accessories", "female", "lady", "spring"],
    char: "\u{1f452}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  tophat: {
    keywords: ["magic", "gentleman", "classy", "circus"],
    char: "\u{1f3a9}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  billed_hat: {
    keywords: ["cap", "baseball"],
    char: "\u{1f9e2}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  rescue_worker_helmet: {
    keywords: ["construction", "build"],
    char: "\u26d1",
    fitzpatrick_scale: !1,
    category: "people",
  },
  mortar_board: {
    keywords: [
      "school",
      "college",
      "degree",
      "university",
      "graduation",
      "cap",
      "hat",
      "legal",
      "learn",
      "education",
    ],
    char: "\u{1f393}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  crown: {
    keywords: ["king", "kod", "leader", "royalty", "lord"],
    char: "\u{1f451}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  school_satchel: {
    keywords: ["student", "education", "bag", "backpack"],
    char: "\u{1f392}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  luggage: {
    keywords: ["packing", "travel"],
    char: "\u{1f9f3}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  pouch: {
    keywords: ["bag", "accessories", "shopping"],
    char: "\u{1f45d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  purse: {
    keywords: ["fashion", "accessories", "money", "sales", "shopping"],
    char: "\u{1f45b}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  handbag: {
    keywords: ["fashion", "accessory", "accessories", "shopping"],
    char: "\u{1f45c}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  briefcase: {
    keywords: [
      "business",
      "documents",
      "work",
      "law",
      "legal",
      "job",
      "career",
    ],
    char: "\u{1f4bc}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  eyeglasses: {
    keywords: ["fashion", "accessories", "eyesight", "nerdy", "dork", "geek"],
    char: "\u{1f453}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  dark_sunglasses: {
    keywords: ["face", "cool", "accessories"],
    char: "\u{1f576}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  goggles: {
    keywords: ["eyes", "protection", "safety"],
    char: "\u{1f97d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  ring: {
    keywords: [
      "wedding",
      "propose",
      "marriage",
      "valentines",
      "diamond",
      "fashion",
      "jewelry",
      "gem",
      "engagement",
    ],
    char: "\u{1f48d}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  closed_umbrella: {
    keywords: ["weather", "rain", "drizzle"],
    char: "\u{1f302}",
    fitzpatrick_scale: !1,
    category: "people",
  },
  dog: {
    keywords: [
      "animal",
      "friend",
      "nature",
      "woof",
      "puppy",
      "pet",
      "faithful",
    ],
    char: "\u{1f436}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cat: {
    keywords: ["animal", "meow", "nature", "pet", "kitten"],
    char: "\u{1f431}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  mouse: {
    keywords: ["animal", "nature", "cheese_wedge", "rodent"],
    char: "\u{1f42d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hamster: {
    keywords: ["animal", "nature"],
    char: "\u{1f439}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  rabbit: {
    keywords: ["animal", "nature", "pet", "spring", "magic", "bunny"],
    char: "\u{1f430}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  fox_face: {
    keywords: ["animal", "nature", "face"],
    char: "\u{1f98a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  bear: {
    keywords: ["animal", "nature", "wild"],
    char: "\u{1f43b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  panda_face: {
    keywords: ["animal", "nature", "panda"],
    char: "\u{1f43c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  koala: {
    keywords: ["animal", "nature"],
    char: "\u{1f428}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  tiger: {
    keywords: ["animal", "cat", "danger", "wild", "nature", "roar"],
    char: "\u{1f42f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  lion: {
    keywords: ["animal", "nature"],
    char: "\u{1f981}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cow: {
    keywords: ["beef", "ox", "animal", "nature", "moo", "milk"],
    char: "\u{1f42e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  pig: {
    keywords: ["animal", "oink", "nature"],
    char: "\u{1f437}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  pig_nose: {
    keywords: ["animal", "oink"],
    char: "\u{1f43d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  frog: {
    keywords: ["animal", "nature", "croak", "toad"],
    char: "\u{1f438}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  squid: {
    keywords: ["animal", "nature", "ocean", "sea"],
    char: "\u{1f991}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  octopus: {
    keywords: ["animal", "creature", "ocean", "sea", "nature", "beach"],
    char: "\u{1f419}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  shrimp: {
    keywords: ["animal", "ocean", "nature", "seafood"],
    char: "\u{1f990}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  monkey_face: {
    keywords: ["animal", "nature", "circus"],
    char: "\u{1f435}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  gorilla: {
    keywords: ["animal", "nature", "circus"],
    char: "\u{1f98d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  see_no_evil: {
    keywords: ["monkey", "animal", "nature", "haha"],
    char: "\u{1f648}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hear_no_evil: {
    keywords: ["animal", "monkey", "nature"],
    char: "\u{1f649}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  speak_no_evil: {
    keywords: ["monkey", "animal", "nature", "omg"],
    char: "\u{1f64a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  monkey: {
    keywords: ["animal", "nature", "banana", "circus"],
    char: "\u{1f412}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  chicken: {
    keywords: ["animal", "cluck", "nature", "bird"],
    char: "\u{1f414}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  penguin: {
    keywords: ["animal", "nature"],
    char: "\u{1f427}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  bird: {
    keywords: ["animal", "nature", "fly", "tweet", "spring"],
    char: "\u{1f426}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  baby_chick: {
    keywords: ["animal", "chicken", "bird"],
    char: "\u{1f424}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hatching_chick: {
    keywords: ["animal", "chicken", "egg", "born", "baby", "bird"],
    char: "\u{1f423}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hatched_chick: {
    keywords: ["animal", "chicken", "baby", "bird"],
    char: "\u{1f425}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  duck: {
    keywords: ["animal", "nature", "bird", "mallard"],
    char: "\u{1f986}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  eagle: {
    keywords: ["animal", "nature", "bird"],
    char: "\u{1f985}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  owl: {
    keywords: ["animal", "nature", "bird", "hoot"],
    char: "\u{1f989}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  bat: {
    keywords: ["animal", "nature", "blind", "vampire"],
    char: "\u{1f987}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  wolf: {
    keywords: ["animal", "nature", "wild"],
    char: "\u{1f43a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  boar: {
    keywords: ["animal", "nature"],
    char: "\u{1f417}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  horse: {
    keywords: ["animal", "brown", "nature"],
    char: "\u{1f434}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  unicorn: {
    keywords: ["animal", "nature", "mystical"],
    char: "\u{1f984}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  honeybee: {
    keywords: ["animal", "insect", "nature", "bug", "spring", "honey"],
    char: "\u{1f41d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  bug: {
    keywords: ["animal", "insect", "nature", "worm"],
    char: "\u{1f41b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  butterfly: {
    keywords: ["animal", "insect", "nature", "caterpillar"],
    char: "\u{1f98b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  snail: {
    keywords: ["slow", "animal", "shell"],
    char: "\u{1f40c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  beetle: {
    keywords: ["animal", "insect", "nature", "ladybug"],
    char: "\u{1f41e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  ant: {
    keywords: ["animal", "insect", "nature", "bug"],
    char: "\u{1f41c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  grasshopper: {
    keywords: ["animal", "cricket", "chirp"],
    char: "\u{1f997}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  spider: {
    keywords: ["animal", "arachnid"],
    char: "\u{1f577}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  scorpion: {
    keywords: ["animal", "arachnid"],
    char: "\u{1f982}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  crab: {
    keywords: ["animal", "crustacean"],
    char: "\u{1f980}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  snake: {
    keywords: ["animal", "evil", "nature", "hiss", "python"],
    char: "\u{1f40d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  lizard: {
    keywords: ["animal", "nature", "reptile"],
    char: "\u{1f98e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  "t-rex": {
    keywords: ["animal", "nature", "dinosaur", "tyrannosaurus", "extinct"],
    char: "\u{1f996}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sauropod: {
    keywords: [
      "animal",
      "nature",
      "dinosaur",
      "brachiosaurus",
      "brontosaurus",
      "diplodocus",
      "extinct",
    ],
    char: "\u{1f995}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  turtle: {
    keywords: ["animal", "slow", "nature", "tortoise"],
    char: "\u{1f422}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  tropical_fish: {
    keywords: ["animal", "swim", "ocean", "beach", "nemo"],
    char: "\u{1f420}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  fish: {
    keywords: ["animal", "food", "nature"],
    char: "\u{1f41f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  blowfish: {
    keywords: ["animal", "nature", "food", "sea", "ocean"],
    char: "\u{1f421}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dolphin: {
    keywords: [
      "animal",
      "nature",
      "fish",
      "sea",
      "ocean",
      "flipper",
      "fins",
      "beach",
    ],
    char: "\u{1f42c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  shark: {
    keywords: [
      "animal",
      "nature",
      "fish",
      "sea",
      "ocean",
      "jaws",
      "fins",
      "beach",
    ],
    char: "\u{1f988}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  whale: {
    keywords: ["animal", "nature", "sea", "ocean"],
    char: "\u{1f433}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  whale2: {
    keywords: ["animal", "nature", "sea", "ocean"],
    char: "\u{1f40b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  crocodile: {
    keywords: ["animal", "nature", "reptile", "lizard", "alligator"],
    char: "\u{1f40a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  leopard: {
    keywords: ["animal", "nature"],
    char: "\u{1f406}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  zebra: {
    keywords: ["animal", "nature", "stripes", "safari"],
    char: "\u{1f993}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  tiger2: {
    keywords: ["animal", "nature", "roar"],
    char: "\u{1f405}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  water_buffalo: {
    keywords: ["animal", "nature", "ox", "cow"],
    char: "\u{1f403}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  ox: {
    keywords: ["animal", "cow", "beef"],
    char: "\u{1f402}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cow2: {
    keywords: ["beef", "ox", "animal", "nature", "moo", "milk"],
    char: "\u{1f404}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  deer: {
    keywords: ["animal", "nature", "horns", "venison"],
    char: "\u{1f98c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dromedary_camel: {
    keywords: ["animal", "hot", "desert", "hump"],
    char: "\u{1f42a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  camel: {
    keywords: ["animal", "nature", "hot", "desert", "hump"],
    char: "\u{1f42b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  giraffe: {
    keywords: ["animal", "nature", "spots", "safari"],
    char: "\u{1f992}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  elephant: {
    keywords: ["animal", "nature", "nose", "th", "circus"],
    char: "\u{1f418}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  rhinoceros: {
    keywords: ["animal", "nature", "horn"],
    char: "\u{1f98f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  goat: {
    keywords: ["animal", "nature"],
    char: "\u{1f410}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  ram: {
    keywords: ["animal", "sheep", "nature"],
    char: "\u{1f40f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sheep: {
    keywords: ["animal", "nature", "wool", "shipit"],
    char: "\u{1f411}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  racehorse: {
    keywords: ["animal", "gamble", "luck"],
    char: "\u{1f40e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  pig2: {
    keywords: ["animal", "nature"],
    char: "\u{1f416}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  rat: {
    keywords: ["animal", "mouse", "rodent"],
    char: "\u{1f400}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  mouse2: {
    keywords: ["animal", "nature", "rodent"],
    char: "\u{1f401}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  rooster: {
    keywords: ["animal", "nature", "chicken"],
    char: "\u{1f413}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  turkey: {
    keywords: ["animal", "bird"],
    char: "\u{1f983}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dove: {
    keywords: ["animal", "bird"],
    char: "\u{1f54a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dog2: {
    keywords: ["animal", "nature", "friend", "doge", "pet", "faithful"],
    char: "\u{1f415}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  poodle: {
    keywords: ["dog", "animal", "101", "nature", "pet"],
    char: "\u{1f429}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cat2: {
    keywords: ["animal", "meow", "pet", "cats"],
    char: "\u{1f408}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  rabbit2: {
    keywords: ["animal", "nature", "pet", "magic", "spring"],
    char: "\u{1f407}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  chipmunk: {
    keywords: ["animal", "nature", "rodent", "squirrel"],
    char: "\u{1f43f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hedgehog: {
    keywords: ["animal", "nature", "spiny"],
    char: "\u{1f994}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  raccoon: {
    keywords: ["animal", "nature"],
    char: "\u{1f99d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  llama: {
    keywords: ["animal", "nature", "alpaca"],
    char: "\u{1f999}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hippopotamus: {
    keywords: ["animal", "nature"],
    char: "\u{1f99b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  kangaroo: {
    keywords: ["animal", "nature", "australia", "joey", "hop", "marsupial"],
    char: "\u{1f998}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  badger: {
    keywords: ["animal", "nature", "honey"],
    char: "\u{1f9a1}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  swan: {
    keywords: ["animal", "nature", "bird"],
    char: "\u{1f9a2}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  peacock: {
    keywords: ["animal", "nature", "peahen", "bird"],
    char: "\u{1f99a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  parrot: {
    keywords: ["animal", "nature", "bird", "pirate", "talk"],
    char: "\u{1f99c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  lobster: {
    keywords: ["animal", "nature", "bisque", "claws", "seafood"],
    char: "\u{1f99e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  mosquito: {
    keywords: ["animal", "nature", "insect", "malaria"],
    char: "\u{1f99f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  paw_prints: {
    keywords: ["animal", "tracking", "footprints", "dog", "cat", "pet", "feet"],
    char: "\u{1f43e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dragon: {
    keywords: ["animal", "myth", "nature", "chinese", "green"],
    char: "\u{1f409}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dragon_face: {
    keywords: ["animal", "myth", "nature", "chinese", "green"],
    char: "\u{1f432}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cactus: {
    keywords: ["vegetable", "plant", "nature"],
    char: "\u{1f335}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  christmas_tree: {
    keywords: ["festival", "vacation", "december", "xmas", "celebration"],
    char: "\u{1f384}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  evergreen_tree: {
    keywords: ["plant", "nature"],
    char: "\u{1f332}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  deciduous_tree: {
    keywords: ["plant", "nature"],
    char: "\u{1f333}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  palm_tree: {
    keywords: [
      "plant",
      "vegetable",
      "nature",
      "summer",
      "beach",
      "mojito",
      "tropical",
    ],
    char: "\u{1f334}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  seedling: {
    keywords: ["plant", "nature", "grass", "lawn", "spring"],
    char: "\u{1f331}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  herb: {
    keywords: ["vegetable", "plant", "medicine", "weed", "grass", "lawn"],
    char: "\u{1f33f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  shamrock: {
    keywords: ["vegetable", "plant", "nature", "irish", "clover"],
    char: "\u2618",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  four_leaf_clover: {
    keywords: ["vegetable", "plant", "nature", "lucky", "irish"],
    char: "\u{1f340}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  bamboo: {
    keywords: ["plant", "nature", "vegetable", "panda", "pine_decoration"],
    char: "\u{1f38d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  tanabata_tree: {
    keywords: ["plant", "nature", "branch", "summer"],
    char: "\u{1f38b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  leaves: {
    keywords: [
      "nature",
      "plant",
      "tree",
      "vegetable",
      "grass",
      "lawn",
      "spring",
    ],
    char: "\u{1f343}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  fallen_leaf: {
    keywords: ["nature", "plant", "vegetable", "leaves"],
    char: "\u{1f342}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  maple_leaf: {
    keywords: ["nature", "plant", "vegetable", "ca", "fall"],
    char: "\u{1f341}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  ear_of_rice: {
    keywords: ["nature", "plant"],
    char: "\u{1f33e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  hibiscus: {
    keywords: ["plant", "vegetable", "flowers", "beach"],
    char: "\u{1f33a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sunflower: {
    keywords: ["nature", "plant", "fall"],
    char: "\u{1f33b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  rose: {
    keywords: ["flowers", "valentines", "love", "spring"],
    char: "\u{1f339}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  wilted_flower: {
    keywords: ["plant", "nature", "flower"],
    char: "\u{1f940}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  tulip: {
    keywords: ["flowers", "plant", "nature", "summer", "spring"],
    char: "\u{1f337}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  blossom: {
    keywords: ["nature", "flowers", "yellow"],
    char: "\u{1f33c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cherry_blossom: {
    keywords: ["nature", "plant", "spring", "flower"],
    char: "\u{1f338}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  bouquet: {
    keywords: ["flowers", "nature", "spring"],
    char: "\u{1f490}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  mushroom: {
    keywords: ["plant", "vegetable"],
    char: "\u{1f344}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  chestnut: {
    keywords: ["food", "squirrel"],
    char: "\u{1f330}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  jack_o_lantern: {
    keywords: ["halloween", "light", "pumpkin", "creepy", "fall"],
    char: "\u{1f383}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  shell: {
    keywords: ["nature", "sea", "beach"],
    char: "\u{1f41a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  spider_web: {
    keywords: ["animal", "insect", "arachnid", "silk"],
    char: "\u{1f578}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  earth_americas: {
    keywords: ["globe", "world", "USA", "international"],
    char: "\u{1f30e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  earth_africa: {
    keywords: ["globe", "world", "international"],
    char: "\u{1f30d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  earth_asia: {
    keywords: ["globe", "world", "east", "international"],
    char: "\u{1f30f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  full_moon: {
    keywords: [
      "nature",
      "yellow",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f315}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  waning_gibbous_moon: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
      "waxing_gibbous_moon",
    ],
    char: "\u{1f316}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  last_quarter_moon: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f317}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  waning_crescent_moon: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f318}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  new_moon: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f311}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  waxing_crescent_moon: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f312}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  first_quarter_moon: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f313}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  waxing_gibbous_moon: {
    keywords: [
      "nature",
      "night",
      "sky",
      "gray",
      "twilight",
      "planet",
      "space",
      "evening",
      "sleep",
    ],
    char: "\u{1f314}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  new_moon_with_face: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f31a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  full_moon_with_face: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f31d}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  first_quarter_moon_with_face: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f31b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  last_quarter_moon_with_face: {
    keywords: [
      "nature",
      "twilight",
      "planet",
      "space",
      "night",
      "evening",
      "sleep",
    ],
    char: "\u{1f31c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sun_with_face: {
    keywords: ["nature", "morning", "sky"],
    char: "\u{1f31e}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  crescent_moon: {
    keywords: ["night", "sleep", "sky", "evening", "magic"],
    char: "\u{1f319}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  star: {
    keywords: ["night", "yellow"],
    char: "\u2b50",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  star2: {
    keywords: ["night", "sparkle", "awesome", "good", "magic"],
    char: "\u{1f31f}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dizzy: {
    keywords: ["star", "sparkle", "shoot", "magic"],
    char: "\u{1f4ab}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sparkles: {
    keywords: ["stars", "shine", "shiny", "cool", "awesome", "good", "magic"],
    char: "\u2728",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  comet: {
    keywords: ["space"],
    char: "\u2604",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sunny: {
    keywords: ["weather", "nature", "brightness", "summer", "beach", "spring"],
    char: "\u2600\ufe0f",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sun_behind_small_cloud: {
    keywords: ["weather"],
    char: "\u{1f324}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  partly_sunny: {
    keywords: ["weather", "nature", "cloudy", "morning", "fall", "spring"],
    char: "\u26c5",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sun_behind_large_cloud: {
    keywords: ["weather"],
    char: "\u{1f325}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sun_behind_rain_cloud: {
    keywords: ["weather"],
    char: "\u{1f326}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cloud: {
    keywords: ["weather", "sky"],
    char: "\u2601\ufe0f",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cloud_with_rain: {
    keywords: ["weather"],
    char: "\u{1f327}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cloud_with_lightning_and_rain: {
    keywords: ["weather", "lightning"],
    char: "\u26c8",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cloud_with_lightning: {
    keywords: ["weather", "thunder"],
    char: "\u{1f329}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  zap: {
    keywords: ["thunder", "weather", "lightning bolt", "fast"],
    char: "\u26a1",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  fire: {
    keywords: ["hot", "cook", "flame"],
    char: "\u{1f525}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  boom: {
    keywords: ["bomb", "explode", "explosion", "collision", "blown"],
    char: "\u{1f4a5}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  snowflake: {
    keywords: ["winter", "season", "cold", "weather", "christmas", "xmas"],
    char: "\u2744\ufe0f",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  cloud_with_snow: {
    keywords: ["weather"],
    char: "\u{1f328}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  snowman: {
    keywords: [
      "winter",
      "season",
      "cold",
      "weather",
      "christmas",
      "xmas",
      "frozen",
      "without_snow",
    ],
    char: "\u26c4",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  snowman_with_snow: {
    keywords: [
      "winter",
      "season",
      "cold",
      "weather",
      "christmas",
      "xmas",
      "frozen",
    ],
    char: "\u2603",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  wind_face: {
    keywords: ["gust", "air"],
    char: "\u{1f32c}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  dash: {
    keywords: ["wind", "air", "fast", "shoo", "fart", "smoke", "puff"],
    char: "\u{1f4a8}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  tornado: {
    keywords: ["weather", "cyclone", "twister"],
    char: "\u{1f32a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  fog: {
    keywords: ["weather"],
    char: "\u{1f32b}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  open_umbrella: {
    keywords: ["weather", "spring"],
    char: "\u2602",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  umbrella: {
    keywords: ["rainy", "weather", "spring"],
    char: "\u2614",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  droplet: {
    keywords: ["water", "drip", "faucet", "spring"],
    char: "\u{1f4a7}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  sweat_drops: {
    keywords: ["water", "drip", "oops"],
    char: "\u{1f4a6}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  ocean: {
    keywords: ["sea", "water", "wave", "nature", "tsunami", "disaster"],
    char: "\u{1f30a}",
    fitzpatrick_scale: !1,
    category: "animals_and_nature",
  },
  green_apple: {
    keywords: ["fruit", "nature"],
    char: "\u{1f34f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  apple: {
    keywords: ["fruit", "mac", "school"],
    char: "\u{1f34e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  pear: {
    keywords: ["fruit", "nature", "food"],
    char: "\u{1f350}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  tangerine: {
    keywords: ["food", "fruit", "nature", "orange"],
    char: "\u{1f34a}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  lemon: {
    keywords: ["fruit", "nature"],
    char: "\u{1f34b}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  banana: {
    keywords: ["fruit", "food", "monkey"],
    char: "\u{1f34c}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  watermelon: {
    keywords: ["fruit", "food", "picnic", "summer"],
    char: "\u{1f349}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  grapes: {
    keywords: ["fruit", "food", "wine"],
    char: "\u{1f347}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  strawberry: {
    keywords: ["fruit", "food", "nature"],
    char: "\u{1f353}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  melon: {
    keywords: ["fruit", "nature", "food"],
    char: "\u{1f348}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cherries: {
    keywords: ["food", "fruit"],
    char: "\u{1f352}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  peach: {
    keywords: ["fruit", "nature", "food"],
    char: "\u{1f351}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  pineapple: {
    keywords: ["fruit", "nature", "food"],
    char: "\u{1f34d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  coconut: {
    keywords: ["fruit", "nature", "food", "palm"],
    char: "\u{1f965}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  kiwi_fruit: {
    keywords: ["fruit", "food"],
    char: "\u{1f95d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  mango: {
    keywords: ["fruit", "food", "tropical"],
    char: "\u{1f96d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  avocado: {
    keywords: ["fruit", "food"],
    char: "\u{1f951}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  broccoli: {
    keywords: ["fruit", "food", "vegetable"],
    char: "\u{1f966}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  tomato: {
    keywords: ["fruit", "vegetable", "nature", "food"],
    char: "\u{1f345}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  eggplant: {
    keywords: ["vegetable", "nature", "food", "aubergine"],
    char: "\u{1f346}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cucumber: {
    keywords: ["fruit", "food", "pickle"],
    char: "\u{1f952}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  carrot: {
    keywords: ["vegetable", "food", "orange"],
    char: "\u{1f955}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  hot_pepper: {
    keywords: ["food", "spicy", "chilli", "chili"],
    char: "\u{1f336}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  potato: {
    keywords: ["food", "tuber", "vegatable", "starch"],
    char: "\u{1f954}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  corn: {
    keywords: ["food", "vegetable", "plant"],
    char: "\u{1f33d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  leafy_greens: {
    keywords: [
      "food",
      "vegetable",
      "plant",
      "bok choy",
      "cabbage",
      "kale",
      "lettuce",
    ],
    char: "\u{1f96c}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  sweet_potato: {
    keywords: ["food", "nature"],
    char: "\u{1f360}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  peanuts: {
    keywords: ["food", "nut"],
    char: "\u{1f95c}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  honey_pot: {
    keywords: ["bees", "sweet", "kitchen"],
    char: "\u{1f36f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  croissant: {
    keywords: ["food", "bread", "french"],
    char: "\u{1f950}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  bread: {
    keywords: ["food", "wheat", "breakfast", "toast"],
    char: "\u{1f35e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  baguette_bread: {
    keywords: ["food", "bread", "french"],
    char: "\u{1f956}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  bagel: {
    keywords: ["food", "bread", "bakery", "schmear"],
    char: "\u{1f96f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  pretzel: {
    keywords: ["food", "bread", "twisted"],
    char: "\u{1f968}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cheese: {
    keywords: ["food", "chadder"],
    char: "\u{1f9c0}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  egg: {
    keywords: ["food", "chicken", "breakfast"],
    char: "\u{1f95a}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  bacon: {
    keywords: ["food", "breakfast", "pork", "pig", "meat"],
    char: "\u{1f953}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  steak: {
    keywords: ["food", "cow", "meat", "cut", "chop", "lambchop", "porkchop"],
    char: "\u{1f969}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  pancakes: {
    keywords: ["food", "breakfast", "flapjacks", "hotcakes"],
    char: "\u{1f95e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  poultry_leg: {
    keywords: ["food", "meat", "drumstick", "bird", "chicken", "turkey"],
    char: "\u{1f357}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  meat_on_bone: {
    keywords: ["good", "food", "drumstick"],
    char: "\u{1f356}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  bone: {
    keywords: ["skeleton"],
    char: "\u{1f9b4}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  fried_shrimp: {
    keywords: ["food", "animal", "appetizer", "summer"],
    char: "\u{1f364}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  fried_egg: {
    keywords: ["food", "breakfast", "kitchen", "egg"],
    char: "\u{1f373}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  hamburger: {
    keywords: [
      "meat",
      "fast food",
      "beef",
      "cheeseburger",
      "mcdonalds",
      "burger king",
    ],
    char: "\u{1f354}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  fries: {
    keywords: ["chips", "snack", "fast food"],
    char: "\u{1f35f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  stuffed_flatbread: {
    keywords: ["food", "flatbread", "stuffed", "gyro"],
    char: "\u{1f959}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  hotdog: {
    keywords: ["food", "frankfurter"],
    char: "\u{1f32d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  pizza: {
    keywords: ["food", "party"],
    char: "\u{1f355}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  sandwich: {
    keywords: ["food", "lunch", "bread"],
    char: "\u{1f96a}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  canned_food: {
    keywords: ["food", "soup"],
    char: "\u{1f96b}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  spaghetti: {
    keywords: ["food", "italian", "noodle"],
    char: "\u{1f35d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  taco: {
    keywords: ["food", "mexican"],
    char: "\u{1f32e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  burrito: {
    keywords: ["food", "mexican"],
    char: "\u{1f32f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  green_salad: {
    keywords: ["food", "healthy", "lettuce"],
    char: "\u{1f957}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  shallow_pan_of_food: {
    keywords: ["food", "cooking", "casserole", "paella"],
    char: "\u{1f958}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  ramen: {
    keywords: ["food", "japanese", "noodle", "chopsticks"],
    char: "\u{1f35c}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  stew: {
    keywords: ["food", "meat", "soup"],
    char: "\u{1f372}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  fish_cake: {
    keywords: [
      "food",
      "japan",
      "sea",
      "beach",
      "narutomaki",
      "pink",
      "swirl",
      "kamaboko",
      "surimi",
      "ramen",
    ],
    char: "\u{1f365}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  fortune_cookie: {
    keywords: ["food", "prophecy"],
    char: "\u{1f960}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  sushi: {
    keywords: ["food", "fish", "japanese", "rice"],
    char: "\u{1f363}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  bento: {
    keywords: ["food", "japanese", "box"],
    char: "\u{1f371}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  curry: {
    keywords: ["food", "spicy", "hot", "indian"],
    char: "\u{1f35b}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  rice_ball: {
    keywords: ["food", "japanese"],
    char: "\u{1f359}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  rice: {
    keywords: ["food", "china", "asian"],
    char: "\u{1f35a}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  rice_cracker: {
    keywords: ["food", "japanese"],
    char: "\u{1f358}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  oden: {
    keywords: ["food", "japanese"],
    char: "\u{1f362}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  dango: {
    keywords: ["food", "dessert", "sweet", "japanese", "barbecue", "meat"],
    char: "\u{1f361}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  shaved_ice: {
    keywords: ["hot", "dessert", "summer"],
    char: "\u{1f367}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  ice_cream: {
    keywords: ["food", "hot", "dessert"],
    char: "\u{1f368}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  icecream: {
    keywords: ["food", "hot", "dessert", "summer"],
    char: "\u{1f366}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  pie: {
    keywords: ["food", "dessert", "pastry"],
    char: "\u{1f967}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cake: {
    keywords: ["food", "dessert"],
    char: "\u{1f370}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cupcake: {
    keywords: ["food", "dessert", "bakery", "sweet"],
    char: "\u{1f9c1}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  moon_cake: {
    keywords: ["food", "autumn"],
    char: "\u{1f96e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  birthday: {
    keywords: ["food", "dessert", "cake"],
    char: "\u{1f382}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  custard: {
    keywords: ["dessert", "food"],
    char: "\u{1f36e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  candy: {
    keywords: ["snack", "dessert", "sweet", "lolly"],
    char: "\u{1f36c}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  lollipop: {
    keywords: ["food", "snack", "candy", "sweet"],
    char: "\u{1f36d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  chocolate_bar: {
    keywords: ["food", "snack", "dessert", "sweet"],
    char: "\u{1f36b}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  popcorn: {
    keywords: ["food", "movie theater", "films", "snack"],
    char: "\u{1f37f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  dumpling: {
    keywords: ["food", "empanada", "pierogi", "potsticker"],
    char: "\u{1f95f}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  doughnut: {
    keywords: ["food", "dessert", "snack", "sweet", "donut"],
    char: "\u{1f369}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cookie: {
    keywords: ["food", "snack", "oreo", "chocolate", "sweet", "dessert"],
    char: "\u{1f36a}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  milk_glass: {
    keywords: ["beverage", "drink", "cow"],
    char: "\u{1f95b}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  beer: {
    keywords: [
      "relax",
      "beverage",
      "drink",
      "drunk",
      "party",
      "pub",
      "summer",
      "alcohol",
      "booze",
    ],
    char: "\u{1f37a}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  beers: {
    keywords: [
      "relax",
      "beverage",
      "drink",
      "drunk",
      "party",
      "pub",
      "summer",
      "alcohol",
      "booze",
    ],
    char: "\u{1f37b}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  clinking_glasses: {
    keywords: [
      "beverage",
      "drink",
      "party",
      "alcohol",
      "celebrate",
      "cheers",
      "wine",
      "champagne",
      "toast",
    ],
    char: "\u{1f942}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  wine_glass: {
    keywords: ["drink", "beverage", "drunk", "alcohol", "booze"],
    char: "\u{1f377}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  tumbler_glass: {
    keywords: [
      "drink",
      "beverage",
      "drunk",
      "alcohol",
      "liquor",
      "booze",
      "bourbon",
      "scotch",
      "whisky",
      "glass",
      "shot",
    ],
    char: "\u{1f943}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cocktail: {
    keywords: ["drink", "drunk", "alcohol", "beverage", "booze", "mojito"],
    char: "\u{1f378}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  tropical_drink: {
    keywords: [
      "beverage",
      "cocktail",
      "summer",
      "beach",
      "alcohol",
      "booze",
      "mojito",
    ],
    char: "\u{1f379}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  champagne: {
    keywords: ["drink", "wine", "bottle", "celebration"],
    char: "\u{1f37e}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  sake: {
    keywords: [
      "wine",
      "drink",
      "drunk",
      "beverage",
      "japanese",
      "alcohol",
      "booze",
    ],
    char: "\u{1f376}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  tea: {
    keywords: ["drink", "bowl", "breakfast", "green", "british"],
    char: "\u{1f375}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  cup_with_straw: {
    keywords: ["drink", "soda"],
    char: "\u{1f964}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  coffee: {
    keywords: ["beverage", "caffeine", "latte", "espresso"],
    char: "\u2615",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  baby_bottle: {
    keywords: ["food", "container", "milk"],
    char: "\u{1f37c}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  salt: {
    keywords: ["condiment", "shaker"],
    char: "\u{1f9c2}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  spoon: {
    keywords: ["cutlery", "kitchen", "tableware"],
    char: "\u{1f944}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  fork_and_knife: {
    keywords: ["cutlery", "kitchen"],
    char: "\u{1f374}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  plate_with_cutlery: {
    keywords: ["food", "eat", "meal", "lunch", "dinner", "restaurant"],
    char: "\u{1f37d}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  bowl_with_spoon: {
    keywords: ["food", "breakfast", "cereal", "oatmeal", "porridge"],
    char: "\u{1f963}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  takeout_box: {
    keywords: ["food", "leftovers"],
    char: "\u{1f961}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  chopsticks: {
    keywords: ["food"],
    char: "\u{1f962}",
    fitzpatrick_scale: !1,
    category: "food_and_drink",
  },
  soccer: {
    keywords: ["sports", "football"],
    char: "\u26bd",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  basketball: {
    keywords: ["sports", "balls", "NBA"],
    char: "\u{1f3c0}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  football: {
    keywords: ["sports", "balls", "NFL"],
    char: "\u{1f3c8}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  baseball: {
    keywords: ["sports", "balls"],
    char: "\u26be",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  softball: {
    keywords: ["sports", "balls"],
    char: "\u{1f94e}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  tennis: {
    keywords: ["sports", "balls", "green"],
    char: "\u{1f3be}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  volleyball: {
    keywords: ["sports", "balls"],
    char: "\u{1f3d0}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  rugby_football: {
    keywords: ["sports", "team"],
    char: "\u{1f3c9}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  flying_disc: {
    keywords: ["sports", "frisbee", "ultimate"],
    char: "\u{1f94f}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  "8ball": {
    keywords: ["pool", "hobby", "game", "luck", "magic"],
    char: "\u{1f3b1}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  golf: {
    keywords: ["sports", "business", "flag", "hole", "summer"],
    char: "\u26f3",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  golfing_woman: {
    keywords: ["sports", "business", "woman", "female"],
    char: "\u{1f3cc}\ufe0f\u200d\u2640\ufe0f",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  golfing_man: {
    keywords: ["sports", "business"],
    char: "\u{1f3cc}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  ping_pong: {
    keywords: ["sports", "pingpong"],
    char: "\u{1f3d3}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  badminton: {
    keywords: ["sports"],
    char: "\u{1f3f8}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  goal_net: {
    keywords: ["sports"],
    char: "\u{1f945}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  ice_hockey: {
    keywords: ["sports"],
    char: "\u{1f3d2}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  field_hockey: {
    keywords: ["sports"],
    char: "\u{1f3d1}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  lacrosse: {
    keywords: ["sports", "ball", "stick"],
    char: "\u{1f94d}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  cricket: {
    keywords: ["sports"],
    char: "\u{1f3cf}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  ski: {
    keywords: ["sports", "winter", "cold", "snow"],
    char: "\u{1f3bf}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  skier: {
    keywords: ["sports", "winter", "snow"],
    char: "\u26f7",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  snowboarder: {
    keywords: ["sports", "winter"],
    char: "\u{1f3c2}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  person_fencing: {
    keywords: ["sports", "fencing", "sword"],
    char: "\u{1f93a}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  women_wrestling: {
    keywords: ["sports", "wrestlers"],
    char: "\u{1f93c}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  men_wrestling: {
    keywords: ["sports", "wrestlers"],
    char: "\u{1f93c}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  woman_cartwheeling: {
    keywords: ["gymnastics"],
    char: "\u{1f938}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  man_cartwheeling: {
    keywords: ["gymnastics"],
    char: "\u{1f938}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  woman_playing_handball: {
    keywords: ["sports"],
    char: "\u{1f93e}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  man_playing_handball: {
    keywords: ["sports"],
    char: "\u{1f93e}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  ice_skate: {
    keywords: ["sports"],
    char: "\u26f8",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  curling_stone: {
    keywords: ["sports"],
    char: "\u{1f94c}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  skateboard: {
    keywords: ["board"],
    char: "\u{1f6f9}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  sled: {
    keywords: ["sleigh", "luge", "toboggan"],
    char: "\u{1f6f7}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  bow_and_arrow: {
    keywords: ["sports"],
    char: "\u{1f3f9}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  fishing_pole_and_fish: {
    keywords: ["food", "hobby", "summer"],
    char: "\u{1f3a3}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  boxing_glove: {
    keywords: ["sports", "fighting"],
    char: "\u{1f94a}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  martial_arts_uniform: {
    keywords: ["judo", "karate", "taekwondo"],
    char: "\u{1f94b}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  rowing_woman: {
    keywords: ["sports", "hobby", "water", "ship", "woman", "female"],
    char: "\u{1f6a3}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  rowing_man: {
    keywords: ["sports", "hobby", "water", "ship"],
    char: "\u{1f6a3}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  climbing_woman: {
    keywords: ["sports", "hobby", "woman", "female", "rock"],
    char: "\u{1f9d7}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  climbing_man: {
    keywords: ["sports", "hobby", "man", "male", "rock"],
    char: "\u{1f9d7}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  swimming_woman: {
    keywords: [
      "sports",
      "exercise",
      "human",
      "athlete",
      "water",
      "summer",
      "woman",
      "female",
    ],
    char: "\u{1f3ca}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  swimming_man: {
    keywords: ["sports", "exercise", "human", "athlete", "water", "summer"],
    char: "\u{1f3ca}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  woman_playing_water_polo: {
    keywords: ["sports", "pool"],
    char: "\u{1f93d}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  man_playing_water_polo: {
    keywords: ["sports", "pool"],
    char: "\u{1f93d}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  woman_in_lotus_position: {
    keywords: [
      "woman",
      "female",
      "meditation",
      "yoga",
      "serenity",
      "zen",
      "mindfulness",
    ],
    char: "\u{1f9d8}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  man_in_lotus_position: {
    keywords: [
      "man",
      "male",
      "meditation",
      "yoga",
      "serenity",
      "zen",
      "mindfulness",
    ],
    char: "\u{1f9d8}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  surfing_woman: {
    keywords: ["sports", "ocean", "sea", "summer", "beach", "woman", "female"],
    char: "\u{1f3c4}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  surfing_man: {
    keywords: ["sports", "ocean", "sea", "summer", "beach"],
    char: "\u{1f3c4}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  bath: {
    keywords: ["clean", "shower", "bathroom"],
    char: "\u{1f6c0}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  basketball_woman: {
    keywords: ["sports", "human", "woman", "female"],
    char: "\u26f9\ufe0f\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  basketball_man: {
    keywords: ["sports", "human"],
    char: "\u26f9",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  weight_lifting_woman: {
    keywords: ["sports", "training", "exercise", "woman", "female"],
    char: "\u{1f3cb}\ufe0f\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  weight_lifting_man: {
    keywords: ["sports", "training", "exercise"],
    char: "\u{1f3cb}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  biking_woman: {
    keywords: ["sports", "bike", "exercise", "hipster", "woman", "female"],
    char: "\u{1f6b4}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  biking_man: {
    keywords: ["sports", "bike", "exercise", "hipster"],
    char: "\u{1f6b4}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  mountain_biking_woman: {
    keywords: [
      "transportation",
      "sports",
      "human",
      "race",
      "bike",
      "woman",
      "female",
    ],
    char: "\u{1f6b5}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  mountain_biking_man: {
    keywords: ["transportation", "sports", "human", "race", "bike"],
    char: "\u{1f6b5}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  horse_racing: {
    keywords: ["animal", "betting", "competition", "gambling", "luck"],
    char: "\u{1f3c7}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  business_suit_levitating: {
    keywords: ["suit", "business", "levitate", "hover", "jump"],
    char: "\u{1f574}",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  trophy: {
    keywords: ["win", "award", "contest", "place", "ftw", "ceremony"],
    char: "\u{1f3c6}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  running_shirt_with_sash: {
    keywords: ["play", "pageant"],
    char: "\u{1f3bd}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  medal_sports: {
    keywords: ["award", "winning"],
    char: "\u{1f3c5}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  medal_military: {
    keywords: ["award", "winning", "army"],
    char: "\u{1f396}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  "1st_place_medal": {
    keywords: ["award", "winning", "first"],
    char: "\u{1f947}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  "2nd_place_medal": {
    keywords: ["award", "second"],
    char: "\u{1f948}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  "3rd_place_medal": {
    keywords: ["award", "third"],
    char: "\u{1f949}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  reminder_ribbon: {
    keywords: ["sports", "cause", "support", "awareness"],
    char: "\u{1f397}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  rosette: {
    keywords: ["flower", "decoration", "military"],
    char: "\u{1f3f5}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  ticket: {
    keywords: ["event", "concert", "pass"],
    char: "\u{1f3ab}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  tickets: {
    keywords: ["sports", "concert", "entrance"],
    char: "\u{1f39f}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  performing_arts: {
    keywords: ["acting", "theater", "drama"],
    char: "\u{1f3ad}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  art: {
    keywords: ["design", "paint", "draw", "colors"],
    char: "\u{1f3a8}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  circus_tent: {
    keywords: ["festival", "carnival", "party"],
    char: "\u{1f3aa}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  woman_juggling: {
    keywords: ["juggle", "balance", "skill", "multitask"],
    char: "\u{1f939}\u200d\u2640\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  man_juggling: {
    keywords: ["juggle", "balance", "skill", "multitask"],
    char: "\u{1f939}\u200d\u2642\ufe0f",
    fitzpatrick_scale: !0,
    category: "activity",
  },
  microphone: {
    keywords: ["sound", "music", "PA", "sing", "talkshow"],
    char: "\u{1f3a4}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  headphones: {
    keywords: ["music", "score", "gadgets"],
    char: "\u{1f3a7}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  musical_score: {
    keywords: ["treble", "clef", "compose"],
    char: "\u{1f3bc}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  musical_keyboard: {
    keywords: ["piano", "instrument", "compose"],
    char: "\u{1f3b9}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  drum: {
    keywords: ["music", "instrument", "drumsticks", "snare"],
    char: "\u{1f941}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  saxophone: {
    keywords: ["music", "instrument", "jazz", "blues"],
    char: "\u{1f3b7}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  trumpet: {
    keywords: ["music", "brass"],
    char: "\u{1f3ba}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  guitar: {
    keywords: ["music", "instrument"],
    char: "\u{1f3b8}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  violin: {
    keywords: ["music", "instrument", "orchestra", "symphony"],
    char: "\u{1f3bb}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  clapper: {
    keywords: ["movie", "film", "record"],
    char: "\u{1f3ac}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  video_game: {
    keywords: ["play", "console", "PS4", "controller"],
    char: "\u{1f3ae}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  space_invader: {
    keywords: ["game", "arcade", "play"],
    char: "\u{1f47e}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  dart: {
    keywords: ["game", "play", "bar", "target", "bullseye"],
    char: "\u{1f3af}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  game_die: {
    keywords: ["dice", "random", "tabletop", "play", "luck"],
    char: "\u{1f3b2}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  chess_pawn: {
    keywords: ["expendable"],
    char: "\u265f",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  slot_machine: {
    keywords: ["bet", "gamble", "vegas", "fruit machine", "luck", "casino"],
    char: "\u{1f3b0}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  jigsaw: {
    keywords: ["interlocking", "puzzle", "piece"],
    char: "\u{1f9e9}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  bowling: {
    keywords: ["sports", "fun", "play"],
    char: "\u{1f3b3}",
    fitzpatrick_scale: !1,
    category: "activity",
  },
  red_car: {
    keywords: ["red", "transportation", "vehicle"],
    char: "\u{1f697}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  taxi: {
    keywords: ["uber", "vehicle", "cars", "transportation"],
    char: "\u{1f695}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  blue_car: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f699}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  bus: {
    keywords: ["car", "vehicle", "transportation"],
    char: "\u{1f68c}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  trolleybus: {
    keywords: ["bart", "transportation", "vehicle"],
    char: "\u{1f68e}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  racing_car: {
    keywords: ["sports", "race", "fast", "formula", "f1"],
    char: "\u{1f3ce}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  police_car: {
    keywords: [
      "vehicle",
      "cars",
      "transportation",
      "law",
      "legal",
      "enforcement",
    ],
    char: "\u{1f693}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  ambulance: {
    keywords: ["health", "911", "hospital"],
    char: "\u{1f691}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  fire_engine: {
    keywords: ["transportation", "cars", "vehicle"],
    char: "\u{1f692}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  minibus: {
    keywords: ["vehicle", "car", "transportation"],
    char: "\u{1f690}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  truck: {
    keywords: ["cars", "transportation"],
    char: "\u{1f69a}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  articulated_lorry: {
    keywords: ["vehicle", "cars", "transportation", "express"],
    char: "\u{1f69b}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  tractor: {
    keywords: ["vehicle", "car", "farming", "agriculture"],
    char: "\u{1f69c}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  kick_scooter: {
    keywords: ["vehicle", "kick", "razor"],
    char: "\u{1f6f4}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  motorcycle: {
    keywords: ["race", "sports", "fast"],
    char: "\u{1f3cd}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  bike: {
    keywords: ["sports", "bicycle", "exercise", "hipster"],
    char: "\u{1f6b2}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  motor_scooter: {
    keywords: ["vehicle", "vespa", "sasha"],
    char: "\u{1f6f5}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  rotating_light: {
    keywords: [
      "police",
      "ambulance",
      "911",
      "emergency",
      "alert",
      "error",
      "pinged",
      "law",
      "legal",
    ],
    char: "\u{1f6a8}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  oncoming_police_car: {
    keywords: ["vehicle", "law", "legal", "enforcement", "911"],
    char: "\u{1f694}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  oncoming_bus: {
    keywords: ["vehicle", "transportation"],
    char: "\u{1f68d}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  oncoming_automobile: {
    keywords: ["car", "vehicle", "transportation"],
    char: "\u{1f698}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  oncoming_taxi: {
    keywords: ["vehicle", "cars", "uber"],
    char: "\u{1f696}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  aerial_tramway: {
    keywords: ["transportation", "vehicle", "ski"],
    char: "\u{1f6a1}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  mountain_cableway: {
    keywords: ["transportation", "vehicle", "ski"],
    char: "\u{1f6a0}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  suspension_railway: {
    keywords: ["vehicle", "transportation"],
    char: "\u{1f69f}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  railway_car: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f683}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  train: {
    keywords: ["transportation", "vehicle", "carriage", "public", "travel"],
    char: "\u{1f68b}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  monorail: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f69d}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  bullettrain_side: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f684}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  bullettrain_front: {
    keywords: [
      "transportation",
      "vehicle",
      "speed",
      "fast",
      "public",
      "travel",
    ],
    char: "\u{1f685}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  light_rail: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f688}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  mountain_railway: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f69e}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  steam_locomotive: {
    keywords: ["transportation", "vehicle", "train"],
    char: "\u{1f682}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  train2: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f686}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  metro: {
    keywords: ["transportation", "blue-square", "mrt", "underground", "tube"],
    char: "\u{1f687}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  tram: {
    keywords: ["transportation", "vehicle"],
    char: "\u{1f68a}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  station: {
    keywords: ["transportation", "vehicle", "public"],
    char: "\u{1f689}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  flying_saucer: {
    keywords: ["transportation", "vehicle", "ufo"],
    char: "\u{1f6f8}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  helicopter: {
    keywords: ["transportation", "vehicle", "fly"],
    char: "\u{1f681}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  small_airplane: {
    keywords: ["flight", "transportation", "fly", "vehicle"],
    char: "\u{1f6e9}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  airplane: {
    keywords: ["vehicle", "transportation", "flight", "fly"],
    char: "\u2708\ufe0f",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  flight_departure: {
    keywords: ["airport", "flight", "landing"],
    char: "\u{1f6eb}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  flight_arrival: {
    keywords: ["airport", "flight", "boarding"],
    char: "\u{1f6ec}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  sailboat: {
    keywords: ["ship", "summer", "transportation", "water", "sailing"],
    char: "\u26f5",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  motor_boat: {
    keywords: ["ship"],
    char: "\u{1f6e5}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  speedboat: {
    keywords: ["ship", "transportation", "vehicle", "summer"],
    char: "\u{1f6a4}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  ferry: {
    keywords: ["boat", "ship", "yacht"],
    char: "\u26f4",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  passenger_ship: {
    keywords: ["yacht", "cruise", "ferry"],
    char: "\u{1f6f3}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  rocket: {
    keywords: [
      "launch",
      "ship",
      "staffmode",
      "NASA",
      "outer space",
      "outer_space",
      "fly",
    ],
    char: "\u{1f680}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  artificial_satellite: {
    keywords: ["communication", "gps", "orbit", "spaceflight", "NASA", "ISS"],
    char: "\u{1f6f0}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  seat: {
    keywords: ["sit", "airplane", "transport", "bus", "flight", "fly"],
    char: "\u{1f4ba}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  canoe: {
    keywords: ["boat", "paddle", "water", "ship"],
    char: "\u{1f6f6}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  anchor: {
    keywords: ["ship", "ferry", "sea", "boat"],
    char: "\u2693",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  construction: {
    keywords: ["wip", "progress", "caution", "warning"],
    char: "\u{1f6a7}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  fuelpump: {
    keywords: ["gas station", "petroleum"],
    char: "\u26fd",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  busstop: {
    keywords: ["transportation", "wait"],
    char: "\u{1f68f}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  vertical_traffic_light: {
    keywords: ["transportation", "driving"],
    char: "\u{1f6a6}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  traffic_light: {
    keywords: ["transportation", "signal"],
    char: "\u{1f6a5}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  checkered_flag: {
    keywords: ["contest", "finishline", "race", "gokart"],
    char: "\u{1f3c1}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  ship: {
    keywords: ["transportation", "titanic", "deploy"],
    char: "\u{1f6a2}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  ferris_wheel: {
    keywords: ["photo", "carnival", "londoneye"],
    char: "\u{1f3a1}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  roller_coaster: {
    keywords: ["carnival", "playground", "photo", "fun"],
    char: "\u{1f3a2}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  carousel_horse: {
    keywords: ["photo", "carnival"],
    char: "\u{1f3a0}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  building_construction: {
    keywords: ["wip", "working", "progress"],
    char: "\u{1f3d7}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  foggy: {
    keywords: ["photo", "mountain"],
    char: "\u{1f301}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  tokyo_tower: {
    keywords: ["photo", "japanese"],
    char: "\u{1f5fc}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  factory: {
    keywords: ["building", "industry", "pollution", "smoke"],
    char: "\u{1f3ed}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  fountain: {
    keywords: ["photo", "summer", "water", "fresh"],
    char: "\u26f2",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  rice_scene: {
    keywords: ["photo", "japan", "asia", "tsukimi"],
    char: "\u{1f391}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  mountain: {
    keywords: ["photo", "nature", "environment"],
    char: "\u26f0",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  mountain_snow: {
    keywords: ["photo", "nature", "environment", "winter", "cold"],
    char: "\u{1f3d4}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  mount_fuji: {
    keywords: ["photo", "mountain", "nature", "japanese"],
    char: "\u{1f5fb}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  volcano: {
    keywords: ["photo", "nature", "disaster"],
    char: "\u{1f30b}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  japan: {
    keywords: ["nation", "country", "japanese", "asia"],
    char: "\u{1f5fe}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  camping: {
    keywords: ["photo", "outdoors", "tent"],
    char: "\u{1f3d5}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  tent: {
    keywords: ["photo", "camping", "outdoors"],
    char: "\u26fa",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  national_park: {
    keywords: ["photo", "environment", "nature"],
    char: "\u{1f3de}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  motorway: {
    keywords: ["road", "cupertino", "interstate", "highway"],
    char: "\u{1f6e3}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  railway_track: {
    keywords: ["train", "transportation"],
    char: "\u{1f6e4}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  sunrise: {
    keywords: ["morning", "view", "vacation", "photo"],
    char: "\u{1f305}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  sunrise_over_mountains: {
    keywords: ["view", "vacation", "photo"],
    char: "\u{1f304}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  desert: {
    keywords: ["photo", "warm", "saharah"],
    char: "\u{1f3dc}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  beach_umbrella: {
    keywords: ["weather", "summer", "sunny", "sand", "mojito"],
    char: "\u{1f3d6}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  desert_island: {
    keywords: ["photo", "tropical", "mojito"],
    char: "\u{1f3dd}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  city_sunrise: {
    keywords: ["photo", "good morning", "dawn"],
    char: "\u{1f307}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  city_sunset: {
    keywords: ["photo", "evening", "sky", "buildings"],
    char: "\u{1f306}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  cityscape: {
    keywords: ["photo", "night life", "urban"],
    char: "\u{1f3d9}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  night_with_stars: {
    keywords: ["evening", "city", "downtown"],
    char: "\u{1f303}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  bridge_at_night: {
    keywords: ["photo", "sanfrancisco"],
    char: "\u{1f309}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  milky_way: {
    keywords: ["photo", "space", "stars"],
    char: "\u{1f30c}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  stars: {
    keywords: ["night", "photo"],
    char: "\u{1f320}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  sparkler: {
    keywords: ["stars", "night", "shine"],
    char: "\u{1f387}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  fireworks: {
    keywords: ["photo", "festival", "carnival", "congratulations"],
    char: "\u{1f386}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  rainbow: {
    keywords: ["nature", "happy", "unicorn_face", "photo", "sky", "spring"],
    char: "\u{1f308}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  houses: {
    keywords: ["buildings", "photo"],
    char: "\u{1f3d8}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  european_castle: {
    keywords: ["building", "royalty", "history"],
    char: "\u{1f3f0}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  japanese_castle: {
    keywords: ["photo", "building"],
    char: "\u{1f3ef}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  stadium: {
    keywords: ["photo", "place", "sports", "concert", "venue"],
    char: "\u{1f3df}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  statue_of_liberty: {
    keywords: ["american", "newyork"],
    char: "\u{1f5fd}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  house: {
    keywords: ["building", "home"],
    char: "\u{1f3e0}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  house_with_garden: {
    keywords: ["home", "plant", "nature"],
    char: "\u{1f3e1}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  derelict_house: {
    keywords: ["abandon", "evict", "broken", "building"],
    char: "\u{1f3da}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  office: {
    keywords: ["building", "bureau", "work"],
    char: "\u{1f3e2}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  department_store: {
    keywords: ["building", "shopping", "mall"],
    char: "\u{1f3ec}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  office: {
    keywords: ["building", "envelope", "communication"],
    char: "\u{1f3e3}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  european_office: {
    keywords: ["building", "email"],
    char: "\u{1f3e4}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  hospital: {
    keywords: ["building", "health", "surgery", "doctor"],
    char: "\u{1f3e5}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  bank: {
    keywords: ["building", "money", "sales", "cash", "business", "enterprise"],
    char: "\u{1f3e6}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  hotel: {
    keywords: ["building", "accomodation", "checkin"],
    char: "\u{1f3e8}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  convenience_store: {
    keywords: ["building", "shopping", "groceries"],
    char: "\u{1f3ea}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  school: {
    keywords: ["building", "student", "education", "learn", "teach"],
    char: "\u{1f3eb}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  love_hotel: {
    keywords: ["like", "affection", "dating"],
    char: "\u{1f3e9}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  wedding: {
    keywords: [
      "love",
      "like",
      "affection",
      "couple",
      "marriage",
      "bride",
      "groom",
    ],
    char: "\u{1f492}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  classical_building: {
    keywords: ["art", "culture", "history"],
    char: "\u{1f3db}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  church: {
    keywords: ["building", "religion", "christ"],
    char: "\u26ea",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  mosque: {
    keywords: ["islam", "worship", "minaret"],
    char: "\u{1f54c}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  synagogue: {
    keywords: ["judaism", "worship", "temple", "jewish"],
    char: "\u{1f54d}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  kaaba: {
    keywords: ["mecca", "mosque", "islam"],
    char: "\u{1f54b}",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  shinto_shrine: {
    keywords: ["temple", "japan", "kyoto"],
    char: "\u26e9",
    fitzpatrick_scale: !1,
    category: "travel_and_places",
  },
  watch: {
    keywords: ["time", "accessories"],
    char: "\u231a",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  iphone: {
    keywords: ["technology", "apple", "gadgets", "dial"],
    char: "\u{1f4f1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  calling: {
    keywords: ["iphone", "incoming"],
    char: "\u{1f4f2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  computer: {
    keywords: ["technology", "laptop", "screen", "display", "monitor"],
    char: "\u{1f4bb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  keyboard: {
    keywords: ["technology", "computer", "type", "input", "text"],
    char: "\u2328",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  desktop_computer: {
    keywords: ["technology", "computing", "screen"],
    char: "\u{1f5a5}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  printer: {
    keywords: ["paper", "ink"],
    char: "\u{1f5a8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  computer_mouse: {
    keywords: ["click"],
    char: "\u{1f5b1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  trackball: {
    keywords: ["technology", "trackpad"],
    char: "\u{1f5b2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  joystick: {
    keywords: ["game", "play"],
    char: "\u{1f579}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  clamp: {
    keywords: ["tool"],
    char: "\u{1f5dc}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  minidisc: {
    keywords: ["technology", "record", "data", "disk", "90s"],
    char: "\u{1f4bd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  floppy_disk: {
    keywords: ["oldschool", "technology", "save", "90s", "80s"],
    char: "\u{1f4be}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  cd: {
    keywords: ["technology", "dvd", "disk", "disc", "90s"],
    char: "\u{1f4bf}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  dvd: {
    keywords: ["cd", "disk", "disc"],
    char: "\u{1f4c0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  vhs: {
    keywords: ["record", "video", "oldschool", "90s", "80s"],
    char: "\u{1f4fc}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  camera: {
    keywords: ["gadgets", "photography"],
    char: "\u{1f4f7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  camera_flash: {
    keywords: ["photography", "gadgets"],
    char: "\u{1f4f8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  video_camera: {
    keywords: ["film", "record"],
    char: "\u{1f4f9}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  movie_camera: {
    keywords: ["film", "record"],
    char: "\u{1f3a5}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  film_projector: {
    keywords: ["video", "tape", "record", "movie"],
    char: "\u{1f4fd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  film_strip: {
    keywords: ["movie"],
    char: "\u{1f39e}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  telephone_receiver: {
    keywords: ["technology", "communication", "dial"],
    char: "\u{1f4de}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  phone: {
    keywords: ["technology", "communication", "dial", "telephone"],
    char: "\u260e\ufe0f",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pager: {
    keywords: ["bbcall", "oldschool", "90s"],
    char: "\u{1f4df}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  fax: {
    keywords: ["communication", "technology"],
    char: "\u{1f4e0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  tv: {
    keywords: ["technology", "program", "oldschool", "show", "television"],
    char: "\u{1f4fa}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  radio: {
    keywords: ["communication", "music", "podcast", "program"],
    char: "\u{1f4fb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  studio_microphone: {
    keywords: ["sing", "recording", "artist", "talkshow"],
    char: "\u{1f399}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  level_slider: {
    keywords: ["scale"],
    char: "\u{1f39a}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  control_knobs: {
    keywords: ["dial"],
    char: "\u{1f39b}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  compass: {
    keywords: ["magnetic", "navigation", "orienteering"],
    char: "\u{1f9ed}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  stopwatch: {
    keywords: ["time", "deadline"],
    char: "\u23f1",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  timer_clock: {
    keywords: ["alarm"],
    char: "\u23f2",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  alarm_clock: {
    keywords: ["time", "wake"],
    char: "\u23f0",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mantelpiece_clock: {
    keywords: ["time"],
    char: "\u{1f570}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hourglass_flowing_sand: {
    keywords: ["oldschool", "time", "countdown"],
    char: "\u23f3",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hourglass: {
    keywords: ["time", "clock", "oldschool", "limit", "exam", "quiz", "test"],
    char: "\u231b",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  satellite: {
    keywords: ["communication", "future", "radio", "space"],
    char: "\u{1f4e1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  battery: {
    keywords: ["power", "energy", "sustain"],
    char: "\u{1f50b}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  electric_plug: {
    keywords: ["charger", "power"],
    char: "\u{1f50c}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bulb: {
    keywords: ["light", "electricity", "idea"],
    char: "\u{1f4a1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  flashlight: {
    keywords: ["dark", "camping", "sight", "night"],
    char: "\u{1f526}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  candle: {
    keywords: ["fire", "wax"],
    char: "\u{1f56f}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  fire_extinguisher: {
    keywords: ["quench"],
    char: "\u{1f9ef}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  wastebasket: {
    keywords: ["bin", "trash", "rubbish", "garbage", "toss"],
    char: "\u{1f5d1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  oil_drum: {
    keywords: ["barrell"],
    char: "\u{1f6e2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  money_with_wings: {
    keywords: ["dollar", "bills", "payment", "sale"],
    char: "\u{1f4b8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  dollar: {
    keywords: ["money", "sales", "bill", "currency"],
    char: "\u{1f4b5}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  yen: {
    keywords: ["money", "sales", "japanese", "dollar", "currency"],
    char: "\u{1f4b4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  euro: {
    keywords: ["money", "sales", "dollar", "currency"],
    char: "\u{1f4b6}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pound: {
    keywords: [
      "british",
      "sterling",
      "money",
      "sales",
      "bills",
      "uk",
      "england",
      "currency",
    ],
    char: "\u{1f4b7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  moneybag: {
    keywords: ["dollar", "payment", "coins", "sale"],
    char: "\u{1f4b0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  credit_card: {
    keywords: ["money", "sales", "dollar", "bill", "payment", "shopping"],
    char: "\u{1f4b3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  gem: {
    keywords: ["blue", "ruby", "diamond", "jewelry"],
    char: "\u{1f48e}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  balance_scale: {
    keywords: ["law", "fairness", "weight"],
    char: "\u2696",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  toolbox: {
    keywords: ["tools", "diy", "fix", "maintainer", "mechanic"],
    char: "\u{1f9f0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  wrench: {
    keywords: ["tools", "diy", "ikea", "fix", "maintainer"],
    char: "\u{1f527}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hammer: {
    keywords: ["tools", "build", "create"],
    char: "\u{1f528}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hammer_and_pick: {
    keywords: ["tools", "build", "create"],
    char: "\u2692",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hammer_and_wrench: {
    keywords: ["tools", "build", "create"],
    char: "\u{1f6e0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pick: {
    keywords: ["tools", "dig"],
    char: "\u26cf",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  nut_and_bolt: {
    keywords: ["handy", "tools", "fix"],
    char: "\u{1f529}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  gear: {
    keywords: ["cog"],
    char: "\u2699",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  brick: {
    keywords: ["bricks"],
    char: "\u{1f9f1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  chains: {
    keywords: ["lock", "arrest"],
    char: "\u26d3",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  magnet: {
    keywords: ["attraction", "magnetic"],
    char: "\u{1f9f2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  gun: {
    keywords: ["violence", "weapon", "pistol", "revolver"],
    char: "\u{1f52b}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bomb: {
    keywords: ["boom", "explode", "explosion", "terrorism"],
    char: "\u{1f4a3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  firecracker: {
    keywords: ["dynamite", "boom", "explode", "explosion", "explosive"],
    char: "\u{1f9e8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hocho: {
    keywords: ["knife", "blade", "cutlery", "kitchen", "weapon"],
    char: "\u{1f52a}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  dagger: {
    keywords: ["weapon"],
    char: "\u{1f5e1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  crossed_swords: {
    keywords: ["weapon"],
    char: "\u2694",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  shield: {
    keywords: ["protection", "security"],
    char: "\u{1f6e1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  smoking: {
    keywords: ["kills", "tobacco", "cigarette", "joint", "smoke"],
    char: "\u{1f6ac}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  skull_and_crossbones: {
    keywords: [
      "poison",
      "danger",
      "deadly",
      "scary",
      "death",
      "pirate",
      "evil",
    ],
    char: "\u2620",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  coffin: {
    keywords: [
      "vampire",
      "dead",
      "die",
      "death",
      "rip",
      "graveyard",
      "cemetery",
      "casket",
      "funeral",
      "box",
    ],
    char: "\u26b0",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  funeral_urn: {
    keywords: ["dead", "die", "death", "rip", "ashes"],
    char: "\u26b1",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  amphora: {
    keywords: ["vase", "jar"],
    char: "\u{1f3fa}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  crystal_ball: {
    keywords: ["disco", "party", "magic", "circus", "fortune_teller"],
    char: "\u{1f52e}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  prayer_beads: {
    keywords: ["dhikr", "religious"],
    char: "\u{1f4ff}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  nazar_amulet: {
    keywords: ["bead", "charm"],
    char: "\u{1f9ff}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  barber: {
    keywords: ["hair", "salon", "style"],
    char: "\u{1f488}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  alembic: {
    keywords: ["distilling", "science", "experiment", "chemistry"],
    char: "\u2697",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  telescope: {
    keywords: ["stars", "space", "zoom", "science", "astronomy"],
    char: "\u{1f52d}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  microscope: {
    keywords: ["laboratory", "experiment", "zoomin", "science", "study"],
    char: "\u{1f52c}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  hole: {
    keywords: ["embarrassing"],
    char: "\u{1f573}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pill: {
    keywords: ["health", "medicine", "doctor", "pharmacy", "drug"],
    char: "\u{1f48a}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  syringe: {
    keywords: [
      "health",
      "hospital",
      "drugs",
      "blood",
      "medicine",
      "needle",
      "doctor",
      "nurse",
    ],
    char: "\u{1f489}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  dna: {
    keywords: ["biologist", "genetics", "life"],
    char: "\u{1f9ec}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  microbe: {
    keywords: ["amoeba", "bacteria", "germs"],
    char: "\u{1f9a0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  petri_dish: {
    keywords: ["bacteria", "biology", "culture", "lab"],
    char: "\u{1f9eb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  test_tube: {
    keywords: ["chemistry", "experiment", "lab", "science"],
    char: "\u{1f9ea}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  thermometer: {
    keywords: ["weather", "temperature", "hot", "cold"],
    char: "\u{1f321}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  broom: {
    keywords: ["cleaning", "sweeping", "witch"],
    char: "\u{1f9f9}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  basket: {
    keywords: ["laundry"],
    char: "\u{1f9fa}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  toilet_paper: {
    keywords: ["roll"],
    char: "\u{1f9fb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  label: {
    keywords: ["sale", "tag"],
    char: "\u{1f3f7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bookmark: {
    keywords: ["favorite", "label", "save"],
    char: "\u{1f516}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  toilet: {
    keywords: ["restroom", "wc", "washroom", "bathroom", "potty"],
    char: "\u{1f6bd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  shower: {
    keywords: ["clean", "water", "bathroom"],
    char: "\u{1f6bf}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bathtub: {
    keywords: ["clean", "shower", "bathroom"],
    char: "\u{1f6c1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  soap: {
    keywords: ["bar", "bathing", "cleaning", "lather"],
    char: "\u{1f9fc}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  sponge: {
    keywords: ["absorbing", "cleaning", "porous"],
    char: "\u{1f9fd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  lotion_bottle: {
    keywords: ["moisturizer", "sunscreen"],
    char: "\u{1f9f4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  key: {
    keywords: ["lock", "door", "password"],
    char: "\u{1f511}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  old_key: {
    keywords: ["lock", "door", "password"],
    char: "\u{1f5dd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  couch_and_lamp: {
    keywords: ["read", "chill"],
    char: "\u{1f6cb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  sleeping_bed: {
    keywords: ["bed", "rest"],
    char: "\u{1f6cc}",
    fitzpatrick_scale: !0,
    category: "objects",
  },
  bed: {
    keywords: ["sleep", "rest"],
    char: "\u{1f6cf}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  door: {
    keywords: ["house", "entry", "exit"],
    char: "\u{1f6aa}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bellhop_bell: {
    keywords: ["service"],
    char: "\u{1f6ce}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  teddy_bear: {
    keywords: ["plush", "stuffed"],
    char: "\u{1f9f8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  framed_picture: {
    keywords: ["photography"],
    char: "\u{1f5bc}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  world_map: {
    keywords: ["location", "direction"],
    char: "\u{1f5fa}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  parasol_on_ground: {
    keywords: ["weather", "summer"],
    char: "\u26f1",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  moyai: {
    keywords: ["rock", "easter island", "moai"],
    char: "\u{1f5ff}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  shopping: {
    keywords: ["mall", "buy", "purchase"],
    char: "\u{1f6cd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  shopping_cart: {
    keywords: ["trolley"],
    char: "\u{1f6d2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  balloon: {
    keywords: ["party", "celebration", "birthday", "circus"],
    char: "\u{1f388}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  flags: {
    keywords: ["fish", "japanese", "koinobori", "carp", "banner"],
    char: "\u{1f38f}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  ribbon: {
    keywords: ["decoration", "pink", "girl", "bowtie"],
    char: "\u{1f380}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  gift: {
    keywords: ["present", "birthday", "christmas", "xmas"],
    char: "\u{1f381}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  confetti_ball: {
    keywords: ["festival", "party", "birthday", "circus"],
    char: "\u{1f38a}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  tada: {
    keywords: [
      "party",
      "congratulations",
      "birthday",
      "magic",
      "circus",
      "celebration",
    ],
    char: "\u{1f389}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  dolls: {
    keywords: ["japanese", "toy", "kimono"],
    char: "\u{1f38e}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  wind_chime: {
    keywords: ["nature", "ding", "spring", "bell"],
    char: "\u{1f390}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  crossed_flags: {
    keywords: ["japanese", "nation", "country", "border"],
    char: "\u{1f38c}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  izakaya_lantern: {
    keywords: ["light", "paper", "halloween", "spooky"],
    char: "\u{1f3ee}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  red_envelope: {
    keywords: ["gift"],
    char: "\u{1f9e7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  email: {
    keywords: ["letter", "postal", "inbox", "communication"],
    char: "\u2709\ufe0f",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  envelope_with_arrow: {
    keywords: ["email", "communication"],
    char: "\u{1f4e9}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  incoming_envelope: {
    keywords: ["email", "inbox"],
    char: "\u{1f4e8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  "e-mail": {
    keywords: ["communication", "inbox"],
    char: "\u{1f4e7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  love_letter: {
    keywords: ["email", "like", "affection", "envelope", "valentines"],
    char: "\u{1f48c}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  postbox: {
    keywords: ["email", "letter", "envelope"],
    char: "\u{1f4ee}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mailbox_closed: {
    keywords: ["email", "communication", "inbox"],
    char: "\u{1f4ea}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mailbox: {
    keywords: ["email", "inbox", "communication"],
    char: "\u{1f4eb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mailbox_with_mail: {
    keywords: ["email", "inbox", "communication"],
    char: "\u{1f4ec}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mailbox_with_no_mail: {
    keywords: ["email", "inbox"],
    char: "\u{1f4ed}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  package: {
    keywords: ["mail", "gift", "cardboard", "box", "moving"],
    char: "\u{1f4e6}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  postal_horn: {
    keywords: ["instrument", "music"],
    char: "\u{1f4ef}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  inbox_tray: {
    keywords: ["email", "documents"],
    char: "\u{1f4e5}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  outbox_tray: {
    keywords: ["inbox", "email"],
    char: "\u{1f4e4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  scroll: {
    keywords: ["documents", "ancient", "history", "paper"],
    char: "\u{1f4dc}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  page_with_curl: {
    keywords: ["documents", "office", "paper"],
    char: "\u{1f4c3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bookmark_tabs: {
    keywords: ["favorite", "save", "order", "tidy"],
    char: "\u{1f4d1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  receipt: {
    keywords: ["accounting", "expenses"],
    char: "\u{1f9fe}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  bar_chart: {
    keywords: ["graph", "presentation", "stats"],
    char: "\u{1f4ca}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  chart_with_upwards_trend: {
    keywords: [
      "graph",
      "presentation",
      "stats",
      "recovery",
      "business",
      "economics",
      "money",
      "sales",
      "good",
      "success",
    ],
    char: "\u{1f4c8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  chart_with_downwards_trend: {
    keywords: [
      "graph",
      "presentation",
      "stats",
      "recession",
      "business",
      "economics",
      "money",
      "sales",
      "bad",
      "failure",
    ],
    char: "\u{1f4c9}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  page_facing_up: {
    keywords: ["documents", "office", "paper", "information"],
    char: "\u{1f4c4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  date: {
    keywords: ["calendar", "schedule"],
    char: "\u{1f4c5}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  calendar: {
    keywords: ["schedule", "date", "planning"],
    char: "\u{1f4c6}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  spiral_calendar: {
    keywords: ["date", "schedule", "planning"],
    char: "\u{1f5d3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  card_index: {
    keywords: ["business", "stationery"],
    char: "\u{1f4c7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  card_file_box: {
    keywords: ["business", "stationery"],
    char: "\u{1f5c3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  ballot_box: {
    keywords: ["election", "vote"],
    char: "\u{1f5f3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  file_cabinet: {
    keywords: ["filing", "organizing"],
    char: "\u{1f5c4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  clipboard: {
    keywords: ["stationery", "documents"],
    char: "\u{1f4cb}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  spiral_notepad: {
    keywords: ["memo", "stationery"],
    char: "\u{1f5d2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  file_folder: {
    keywords: ["documents", "business", "office"],
    char: "\u{1f4c1}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  open_file_folder: {
    keywords: ["documents", "load"],
    char: "\u{1f4c2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  card_index_dividers: {
    keywords: ["organizing", "business", "stationery"],
    char: "\u{1f5c2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  newspaper_roll: {
    keywords: ["press", "headline"],
    char: "\u{1f5de}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  newspaper: {
    keywords: ["press", "headline"],
    char: "\u{1f4f0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  notebook: {
    keywords: ["stationery", "record", "notes", "paper", "study"],
    char: "\u{1f4d3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  closed_book: {
    keywords: ["read", "library", "knowledge", "textbook", "learn"],
    char: "\u{1f4d5}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  green_book: {
    keywords: ["read", "library", "knowledge", "study"],
    char: "\u{1f4d7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  blue_book: {
    keywords: ["read", "library", "knowledge", "learn", "study"],
    char: "\u{1f4d8}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  orange_book: {
    keywords: ["read", "library", "knowledge", "textbook", "study"],
    char: "\u{1f4d9}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  notebook_with_decorative_cover: {
    keywords: ["classroom", "notes", "record", "paper", "study"],
    char: "\u{1f4d4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  ledger: {
    keywords: ["notes", "paper"],
    char: "\u{1f4d2}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  books: {
    keywords: ["literature", "library", "study"],
    char: "\u{1f4da}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  open_book: {
    keywords: [
      "book",
      "read",
      "library",
      "knowledge",
      "literature",
      "learn",
      "study",
    ],
    char: "\u{1f4d6}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  safety_pin: {
    keywords: ["diaper"],
    char: "\u{1f9f7}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  link: {
    keywords: ["rings", "url"],
    char: "\u{1f517}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  paperclip: {
    keywords: ["documents", "stationery"],
    char: "\u{1f4ce}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  paperclips: {
    keywords: ["documents", "stationery"],
    char: "\u{1f587}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  scissors: {
    keywords: ["stationery", "cut"],
    char: "\u2702\ufe0f",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  triangular_ruler: {
    keywords: ["stationery", "math", "architect", "sketch"],
    char: "\u{1f4d0}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  straight_ruler: {
    keywords: [
      "stationery",
      "calculate",
      "length",
      "math",
      "school",
      "drawing",
      "architect",
      "sketch",
    ],
    char: "\u{1f4cf}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  abacus: {
    keywords: ["calculation"],
    char: "\u{1f9ee}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pushpin: {
    keywords: ["stationery", "mark", "here"],
    char: "\u{1f4cc}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  round_pushpin: {
    keywords: ["stationery", "location", "map", "here"],
    char: "\u{1f4cd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  triangular_flag_on_post: {
    keywords: ["mark", "milestone", "place"],
    char: "\u{1f6a9}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  white_flag: {
    keywords: ["losing", "loser", "lost", "surrender", "give up", "fail"],
    char: "\u{1f3f3}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  black_flag: {
    keywords: ["pirate"],
    char: "\u{1f3f4}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  rainbow_flag: {
    keywords: [
      "flag",
      "rainbow",
      "pride",
      "gay",
      "lgbt",
      "glbt",
      "queer",
      "homosexual",
      "lesbian",
      "bisexual",
      "transgender",
    ],
    char: "\u{1f3f3}\ufe0f\u200d\u{1f308}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  closed_lock_with_key: {
    keywords: ["security", "privacy"],
    char: "\u{1f510}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  lock: {
    keywords: ["security", "password", "padlock"],
    char: "\u{1f512}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  unlock: {
    keywords: ["privacy", "security"],
    char: "\u{1f513}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  lock_with_ink_pen: {
    keywords: ["security", "secret"],
    char: "\u{1f50f}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pen: {
    keywords: ["stationery", "writing", "write"],
    char: "\u{1f58a}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  fountain_pen: {
    keywords: ["stationery", "writing", "write"],
    char: "\u{1f58b}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  black_nib: {
    keywords: ["pen", "stationery", "writing", "write"],
    char: "\u2712\ufe0f",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  memo: {
    keywords: [
      "write",
      "documents",
      "stationery",
      "pencil",
      "paper",
      "writing",
      "legal",
      "exam",
      "quiz",
      "test",
      "study",
      "compose",
    ],
    char: "\u{1f4dd}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  pencil2: {
    keywords: ["stationery", "write", "paper", "writing", "school", "study"],
    char: "\u270f\ufe0f",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  crayon: {
    keywords: ["drawing", "creativity"],
    char: "\u{1f58d}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  paintbrush: {
    keywords: ["drawing", "creativity", "art"],
    char: "\u{1f58c}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mag: {
    keywords: ["search", "zoom", "find", "detective"],
    char: "\u{1f50d}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  mag_right: {
    keywords: ["search", "zoom", "find", "detective"],
    char: "\u{1f50e}",
    fitzpatrick_scale: !1,
    category: "objects",
  },
  heart: {
    keywords: ["love", "like", "valentines"],
    char: "\u2764\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  orange_heart: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f9e1}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  yellow_heart: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f49b}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  green_heart: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f49a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  blue_heart: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f499}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  purple_heart: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f49c}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_heart: {
    keywords: ["evil"],
    char: "\u{1f5a4}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  broken_heart: {
    keywords: ["sad", "sorry", "break", "heart", "heartbreak"],
    char: "\u{1f494}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_heart_exclamation: {
    keywords: ["decoration", "love"],
    char: "\u2763",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  two_hearts: {
    keywords: ["love", "like", "affection", "valentines", "heart"],
    char: "\u{1f495}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  revolving_hearts: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f49e}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heartbeat: {
    keywords: ["love", "like", "affection", "valentines", "pink", "heart"],
    char: "\u{1f493}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heartpulse: {
    keywords: ["like", "love", "affection", "valentines", "pink"],
    char: "\u{1f497}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  sparkling_heart: {
    keywords: ["love", "like", "affection", "valentines"],
    char: "\u{1f496}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  cupid: {
    keywords: ["love", "like", "heart", "affection", "valentines"],
    char: "\u{1f498}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  gift_heart: {
    keywords: ["love", "valentines"],
    char: "\u{1f49d}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heart_decoration: {
    keywords: ["purple-square", "love", "like"],
    char: "\u{1f49f}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  peace_symbol: {
    keywords: ["hippie"],
    char: "\u262e",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  latin_cross: {
    keywords: ["christianity"],
    char: "\u271d",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  star_and_crescent: {
    keywords: ["islam"],
    char: "\u262a",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  om: {
    keywords: ["hinduism", "buddhism", "sikhism", "jainism"],
    char: "\u{1f549}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  wheel_of_dharma: {
    keywords: ["hinduism", "buddhism", "sikhism", "jainism"],
    char: "\u2638",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  star_of_david: {
    keywords: ["judaism"],
    char: "\u2721",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  six_pointed_star: {
    keywords: ["purple-square", "religion", "jewish", "hexagram"],
    char: "\u{1f52f}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  menorah: {
    keywords: ["hanukkah", "candles", "jewish"],
    char: "\u{1f54e}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  yin_yang: {
    keywords: ["balance"],
    char: "\u262f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  orthodox_cross: {
    keywords: ["suppedaneum", "religion"],
    char: "\u2626",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  place_of_worship: {
    keywords: ["religion", "church", "temple", "prayer"],
    char: "\u{1f6d0}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  ophiuchus: {
    keywords: ["sign", "purple-square", "constellation", "astrology"],
    char: "\u26ce",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  aries: {
    keywords: ["sign", "purple-square", "zodiac", "astrology"],
    char: "\u2648",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  taurus: {
    keywords: ["purple-square", "sign", "zodiac", "astrology"],
    char: "\u2649",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  gemini: {
    keywords: ["sign", "zodiac", "purple-square", "astrology"],
    char: "\u264a",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  cancer: {
    keywords: ["sign", "zodiac", "purple-square", "astrology"],
    char: "\u264b",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  leo: {
    keywords: ["sign", "purple-square", "zodiac", "astrology"],
    char: "\u264c",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  virgo: {
    keywords: ["sign", "zodiac", "purple-square", "astrology"],
    char: "\u264d",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  libra: {
    keywords: ["sign", "purple-square", "zodiac", "astrology"],
    char: "\u264e",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  scorpius: {
    keywords: ["sign", "zodiac", "purple-square", "astrology", "scorpio"],
    char: "\u264f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  sagittarius: {
    keywords: ["sign", "zodiac", "purple-square", "astrology"],
    char: "\u2650",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  capricorn: {
    keywords: ["sign", "zodiac", "purple-square", "astrology"],
    char: "\u2651",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  aquarius: {
    keywords: ["sign", "purple-square", "zodiac", "astrology"],
    char: "\u2652",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  pisces: {
    keywords: ["purple-square", "sign", "zodiac", "astrology"],
    char: "\u2653",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  id: {
    keywords: ["purple-square", "words"],
    char: "\u{1f194}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  atom_symbol: {
    keywords: ["science", "physics", "chemistry"],
    char: "\u269b",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u7a7a: {
    keywords: ["kanji", "japanese", "chinese", "empty", "sky", "blue-square"],
    char: "\u{1f233}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u5272: {
    keywords: ["cut", "divide", "chinese", "kanji", "pink-square"],
    char: "\u{1f239}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  radioactive: {
    keywords: ["nuclear", "danger"],
    char: "\u2622",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  biohazard: {
    keywords: ["danger"],
    char: "\u2623",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  mobile_phone_off: {
    keywords: ["mute", "orange-square", "silence", "quiet"],
    char: "\u{1f4f4}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  vibration_mode: {
    keywords: ["orange-square", "phone"],
    char: "\u{1f4f3}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u6709: {
    keywords: ["orange-square", "chinese", "have", "kanji"],
    char: "\u{1f236}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u7121: {
    keywords: ["nothing", "chinese", "kanji", "japanese", "orange-square"],
    char: "\u{1f21a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u7533: {
    keywords: ["chinese", "japanese", "kanji", "orange-square"],
    char: "\u{1f238}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u55b6: {
    keywords: ["japanese", "opening hours", "orange-square"],
    char: "\u{1f23a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u6708: {
    keywords: [
      "chinese",
      "month",
      "moon",
      "japanese",
      "orange-square",
      "kanji",
    ],
    char: "\u{1f237}\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  eight_pointed_black_star: {
    keywords: ["orange-square", "shape", "polygon"],
    char: "\u2734\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  vs: {
    keywords: ["words", "orange-square"],
    char: "\u{1f19a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  accept: {
    keywords: [
      "ok",
      "good",
      "chinese",
      "kanji",
      "agree",
      "yes",
      "orange-circle",
    ],
    char: "\u{1f251}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_flower: {
    keywords: ["japanese", "spring"],
    char: "\u{1f4ae}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  ideograph_advantage: {
    keywords: ["chinese", "kanji", "obtain", "get", "circle"],
    char: "\u{1f250}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  secret: {
    keywords: ["privacy", "chinese", "sshh", "kanji", "red-circle"],
    char: "\u3299\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  congratulations: {
    keywords: ["chinese", "kanji", "japanese", "red-circle"],
    char: "\u3297\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u5408: {
    keywords: ["japanese", "chinese", "join", "kanji", "red-square"],
    char: "\u{1f234}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u6e80: {
    keywords: ["full", "chinese", "japanese", "red-square", "kanji"],
    char: "\u{1f235}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u7981: {
    keywords: [
      "kanji",
      "japanese",
      "chinese",
      "forbidden",
      "limit",
      "restricted",
      "red-square",
    ],
    char: "\u{1f232}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  a: {
    keywords: ["red-square", "alphabet", "letter"],
    char: "\u{1f170}\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  b: {
    keywords: ["red-square", "alphabet", "letter"],
    char: "\u{1f171}\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  ab: {
    keywords: ["red-square", "alphabet"],
    char: "\u{1f18e}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  cl: {
    keywords: ["alphabet", "words", "red-square"],
    char: "\u{1f191}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  o2: {
    keywords: ["alphabet", "red-square", "letter"],
    char: "\u{1f17e}\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  sos: {
    keywords: ["help", "red-square", "words", "emergency", "911"],
    char: "\u{1f198}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_entry: {
    keywords: [
      "limit",
      "security",
      "privacy",
      "bad",
      "denied",
      "stop",
      "circle",
    ],
    char: "\u26d4",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  name_badge: {
    keywords: ["fire", "forbid"],
    char: "\u{1f4db}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_entry_sign: {
    keywords: ["forbid", "stop", "limit", "denied", "disallow", "circle"],
    char: "\u{1f6ab}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  x: {
    keywords: ["no", "delete", "remove", "cancel", "red"],
    char: "\u274c",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  o: {
    keywords: ["circle", "round"],
    char: "\u2b55",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  stop_sign: {
    keywords: ["stop"],
    char: "\u{1f6d1}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  anger: {
    keywords: ["angry", "mad"],
    char: "\u{1f4a2}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  hotsprings: {
    keywords: ["bath", "warm", "relax"],
    char: "\u2668\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_pedestrians: {
    keywords: ["rules", "crossing", "walking", "circle"],
    char: "\u{1f6b7}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  do_not_litter: {
    keywords: ["trash", "bin", "garbage", "circle"],
    char: "\u{1f6af}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_bicycles: {
    keywords: ["cyclist", "prohibited", "circle"],
    char: "\u{1f6b3}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  "non-potable_water": {
    keywords: ["drink", "faucet", "tap", "circle"],
    char: "\u{1f6b1}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  underage: {
    keywords: ["18", "drink", "pub", "night", "minor", "circle"],
    char: "\u{1f51e}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_mobile_phones: {
    keywords: ["iphone", "mute", "circle"],
    char: "\u{1f4f5}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  exclamation: {
    keywords: [
      "heavy_exclamation_mark",
      "danger",
      "surprise",
      "punctuation",
      "wow",
      "warning",
    ],
    char: "\u2757",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  grey_exclamation: {
    keywords: ["surprise", "punctuation", "gray", "wow", "warning"],
    char: "\u2755",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  question: {
    keywords: ["doubt", "confused"],
    char: "\u2753",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  grey_question: {
    keywords: ["doubts", "gray", "huh", "confused"],
    char: "\u2754",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  bangbang: {
    keywords: ["exclamation", "surprise"],
    char: "\u203c\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  interrobang: {
    keywords: ["wat", "punctuation", "surprise"],
    char: "\u2049\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  100: {
    keywords: [
      "score",
      "perfect",
      "numbers",
      "century",
      "exam",
      "quiz",
      "test",
      "pass",
      "hundred",
    ],
    char: "\u{1f4af}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  low_brightness: {
    keywords: ["sun", "afternoon", "warm", "summer"],
    char: "\u{1f505}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  high_brightness: {
    keywords: ["sun", "light"],
    char: "\u{1f506}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  trident: {
    keywords: ["weapon", "spear"],
    char: "\u{1f531}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  fleur_de_lis: {
    keywords: ["decorative", "scout"],
    char: "\u269c",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  part_alternation_mark: {
    keywords: [
      "graph",
      "presentation",
      "stats",
      "business",
      "economics",
      "bad",
    ],
    char: "\u303d\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  warning: {
    keywords: ["exclamation", "wip", "alert", "error", "problem", "issue"],
    char: "\u26a0\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  children_crossing: {
    keywords: [
      "school",
      "warning",
      "danger",
      "sign",
      "driving",
      "yellow-diamond",
    ],
    char: "\u{1f6b8}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  beginner: {
    keywords: ["badge", "shield"],
    char: "\u{1f530}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  recycle: {
    keywords: ["arrow", "environment", "garbage", "trash"],
    char: "\u267b\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  u6307: {
    keywords: ["chinese", "point", "green-square", "kanji"],
    char: "\u{1f22f}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  chart: {
    keywords: ["green-square", "graph", "presentation", "stats"],
    char: "\u{1f4b9}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  sparkle: {
    keywords: ["stars", "green-square", "awesome", "good", "fireworks"],
    char: "\u2747\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  eight_spoked_asterisk: {
    keywords: ["star", "sparkle", "green-square"],
    char: "\u2733\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  negative_squared_cross_mark: {
    keywords: ["x", "green-square", "no", "deny"],
    char: "\u274e",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_check_mark: {
    keywords: [
      "green-square",
      "ok",
      "agree",
      "vote",
      "election",
      "answer",
      "tick",
    ],
    char: "\u2705",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  diamond_shape_with_a_dot_inside: {
    keywords: ["jewel", "blue", "gem", "crystal", "fancy"],
    char: "\u{1f4a0}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  cyclone: {
    keywords: [
      "weather",
      "swirl",
      "blue",
      "cloud",
      "vortex",
      "spiral",
      "whirlpool",
      "spin",
      "tornado",
      "hurricane",
      "typhoon",
    ],
    char: "\u{1f300}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  loop: {
    keywords: ["tape", "cassette"],
    char: "\u27bf",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  globe_with_meridians: {
    keywords: [
      "earth",
      "international",
      "world",
      "internet",
      "interweb",
      "i18n",
    ],
    char: "\u{1f310}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  m: {
    keywords: ["alphabet", "blue-circle", "letter"],
    char: "\u24c2\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  atm: {
    keywords: ["money", "sales", "cash", "blue-square", "payment", "bank"],
    char: "\u{1f3e7}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  sa: {
    keywords: ["japanese", "blue-square", "katakana"],
    char: "\u{1f202}\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  passport_control: {
    keywords: ["custom", "blue-square"],
    char: "\u{1f6c2}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  customs: {
    keywords: ["passport", "border", "blue-square"],
    char: "\u{1f6c3}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  baggage_claim: {
    keywords: ["blue-square", "airport", "transport"],
    char: "\u{1f6c4}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  left_luggage: {
    keywords: ["blue-square", "travel"],
    char: "\u{1f6c5}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  wheelchair: {
    keywords: ["blue-square", "disabled", "a11y", "accessibility"],
    char: "\u267f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_smoking: {
    keywords: ["cigarette", "blue-square", "smell", "smoke"],
    char: "\u{1f6ad}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  wc: {
    keywords: ["toilet", "restroom", "blue-square"],
    char: "\u{1f6be}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  parking: {
    keywords: ["cars", "blue-square", "alphabet", "letter"],
    char: "\u{1f17f}\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  potable_water: {
    keywords: ["blue-square", "liquid", "restroom", "cleaning", "faucet"],
    char: "\u{1f6b0}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  mens: {
    keywords: ["toilet", "restroom", "wc", "blue-square", "gender", "male"],
    char: "\u{1f6b9}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  womens: {
    keywords: [
      "purple-square",
      "woman",
      "female",
      "toilet",
      "loo",
      "restroom",
      "gender",
    ],
    char: "\u{1f6ba}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  baby_symbol: {
    keywords: ["orange-square", "child"],
    char: "\u{1f6bc}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  restroom: {
    keywords: ["blue-square", "toilet", "refresh", "wc", "gender"],
    char: "\u{1f6bb}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  put_litter_in_its_place: {
    keywords: ["blue-square", "sign", "human", "info"],
    char: "\u{1f6ae}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  cinema: {
    keywords: [
      "blue-square",
      "record",
      "film",
      "movie",
      "curtain",
      "stage",
      "theater",
    ],
    char: "\u{1f3a6}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  signal_strength: {
    keywords: [
      "blue-square",
      "reception",
      "phone",
      "internet",
      "connection",
      "wifi",
      "bluetooth",
      "bars",
    ],
    char: "\u{1f4f6}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  koko: {
    keywords: ["blue-square", "here", "katakana", "japanese", "destination"],
    char: "\u{1f201}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  ng: {
    keywords: ["blue-square", "words", "shape", "icon"],
    char: "\u{1f196}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  ok: {
    keywords: ["good", "agree", "yes", "blue-square"],
    char: "\u{1f197}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  up: {
    keywords: ["blue-square", "above", "high"],
    char: "\u{1f199}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  cool: {
    keywords: ["words", "blue-square"],
    char: "\u{1f192}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  new: {
    keywords: ["blue-square", "words", "start"],
    char: "\u{1f195}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  free: {
    keywords: ["blue-square", "words"],
    char: "\u{1f193}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  zero: {
    keywords: ["0", "numbers", "blue-square", "null"],
    char: "0\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  one: {
    keywords: ["blue-square", "numbers", "1"],
    char: "1\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  two: {
    keywords: ["numbers", "2", "prime", "blue-square"],
    char: "2\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  three: {
    keywords: ["3", "numbers", "prime", "blue-square"],
    char: "3\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  four: {
    keywords: ["4", "numbers", "blue-square"],
    char: "4\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  five: {
    keywords: ["5", "numbers", "blue-square", "prime"],
    char: "5\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  six: {
    keywords: ["6", "numbers", "blue-square"],
    char: "6\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  seven: {
    keywords: ["7", "numbers", "blue-square", "prime"],
    char: "7\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  eight: {
    keywords: ["8", "blue-square", "numbers"],
    char: "8\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  nine: {
    keywords: ["blue-square", "numbers", "9"],
    char: "9\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  keycap_ten: {
    keywords: ["numbers", "10", "blue-square"],
    char: "\u{1f51f}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  asterisk: {
    keywords: ["star", "keycap"],
    char: "*\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  1234: {
    keywords: ["numbers", "blue-square"],
    char: "\u{1f522}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  eject_button: {
    keywords: ["blue-square"],
    char: "\u23cf\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_forward: {
    keywords: ["blue-square", "right", "direction", "play"],
    char: "\u25b6\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  pause_button: {
    keywords: ["pause", "blue-square"],
    char: "\u23f8",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  next_track_button: {
    keywords: ["forward", "next", "blue-square"],
    char: "\u23ed",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  stop_button: {
    keywords: ["blue-square"],
    char: "\u23f9",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  record_button: {
    keywords: ["blue-square"],
    char: "\u23fa",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  play_or_pause_button: {
    keywords: ["blue-square", "play", "pause"],
    char: "\u23ef",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  previous_track_button: {
    keywords: ["backward"],
    char: "\u23ee",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  fast_forward: {
    keywords: ["blue-square", "play", "speed", "continue"],
    char: "\u23e9",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  rewind: {
    keywords: ["play", "blue-square"],
    char: "\u23ea",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  twisted_rightwards_arrows: {
    keywords: ["blue-square", "shuffle", "music", "random"],
    char: "\u{1f500}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  repeat: {
    keywords: ["loop", "record"],
    char: "\u{1f501}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  repeat_one: {
    keywords: ["blue-square", "loop"],
    char: "\u{1f502}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_backward: {
    keywords: ["blue-square", "left", "direction"],
    char: "\u25c0\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_up_small: {
    keywords: [
      "blue-square",
      "triangle",
      "direction",
      "point",
      "forward",
      "top",
    ],
    char: "\u{1f53c}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_down_small: {
    keywords: ["blue-square", "direction", "bottom"],
    char: "\u{1f53d}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_double_up: {
    keywords: ["blue-square", "direction", "top"],
    char: "\u23eb",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_double_down: {
    keywords: ["blue-square", "direction", "bottom"],
    char: "\u23ec",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_right: {
    keywords: ["blue-square", "next"],
    char: "\u27a1\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_left: {
    keywords: ["blue-square", "previous", "back"],
    char: "\u2b05\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_up: {
    keywords: ["blue-square", "continue", "top", "direction"],
    char: "\u2b06\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_down: {
    keywords: ["blue-square", "direction", "bottom"],
    char: "\u2b07\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_upper_right: {
    keywords: ["blue-square", "point", "direction", "diagonal", "northeast"],
    char: "\u2197\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_lower_right: {
    keywords: ["blue-square", "direction", "diagonal", "southeast"],
    char: "\u2198\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_lower_left: {
    keywords: ["blue-square", "direction", "diagonal", "southwest"],
    char: "\u2199\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_upper_left: {
    keywords: ["blue-square", "point", "direction", "diagonal", "northwest"],
    char: "\u2196\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_up_down: {
    keywords: ["blue-square", "direction", "way", "vertical"],
    char: "\u2195\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  left_right_arrow: {
    keywords: ["shape", "direction", "horizontal", "sideways"],
    char: "\u2194\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrows_counterclockwise: {
    keywords: ["blue-square", "sync", "cycle"],
    char: "\u{1f504}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_right_hook: {
    keywords: ["blue-square", "return", "rotate", "direction"],
    char: "\u21aa\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  leftwards_arrow_with_hook: {
    keywords: ["back", "return", "blue-square", "undo", "enter"],
    char: "\u21a9\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_heading_up: {
    keywords: ["blue-square", "direction", "top"],
    char: "\u2934\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrow_heading_down: {
    keywords: ["blue-square", "direction", "bottom"],
    char: "\u2935\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  hash: {
    keywords: ["symbol", "blue-square", "twitter"],
    char: "#\ufe0f\u20e3",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  information_source: {
    keywords: ["blue-square", "alphabet", "letter"],
    char: "\u2139\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  abc: {
    keywords: ["blue-square", "alphabet"],
    char: "\u{1f524}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  abcd: {
    keywords: ["blue-square", "alphabet"],
    char: "\u{1f521}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  capital_abcd: {
    keywords: ["alphabet", "words", "blue-square"],
    char: "\u{1f520}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  symbols: {
    keywords: [
      "blue-square",
      "music",
      "note",
      "ampersand",
      "percent",
      "glyphs",
      "characters",
    ],
    char: "\u{1f523}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  musical_note: {
    keywords: ["score", "tone", "sound"],
    char: "\u{1f3b5}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  notes: {
    keywords: ["music", "score"],
    char: "\u{1f3b6}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  wavy_dash: {
    keywords: ["draw", "line", "moustache", "mustache", "squiggle", "scribble"],
    char: "\u3030\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  curly_loop: {
    keywords: ["scribble", "draw", "shape", "squiggle"],
    char: "\u27b0",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_check_mark: {
    keywords: ["ok", "nike", "answer", "yes", "tick"],
    char: "\u2714\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  arrows_clockwise: {
    keywords: ["sync", "cycle", "round", "repeat"],
    char: "\u{1f503}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_plus_sign: {
    keywords: ["math", "calculation", "addition", "more", "increase"],
    char: "\u2795",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_minus_sign: {
    keywords: ["math", "calculation", "subtract", "less"],
    char: "\u2796",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_division_sign: {
    keywords: ["divide", "math", "calculation"],
    char: "\u2797",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_multiplication_x: {
    keywords: ["math", "calculation"],
    char: "\u2716\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  infinity: {
    keywords: ["forever"],
    char: "\u267e",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  heavy_dollar_sign: {
    keywords: ["money", "sales", "payment", "currency", "buck"],
    char: "\u{1f4b2}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  currency_exchange: {
    keywords: ["money", "sales", "dollar", "travel"],
    char: "\u{1f4b1}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  copyright: {
    keywords: ["ip", "license", "circle", "law", "legal"],
    char: "\xa9\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  registered: {
    keywords: ["alphabet", "circle"],
    char: "\xae\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  tm: {
    keywords: ["trademark", "brand", "law", "legal"],
    char: "\u2122\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  end: {
    keywords: ["words", "arrow"],
    char: "\u{1f51a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  back: {
    keywords: ["arrow", "words", "return"],
    char: "\u{1f519}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  on: {
    keywords: ["arrow", "words"],
    char: "\u{1f51b}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  top: {
    keywords: ["words", "blue-square"],
    char: "\u{1f51d}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  soon: {
    keywords: ["arrow", "words"],
    char: "\u{1f51c}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  ballot_box_with_check: {
    keywords: [
      "ok",
      "agree",
      "confirm",
      "black-square",
      "vote",
      "election",
      "yes",
      "tick",
    ],
    char: "\u2611\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  radio_button: {
    keywords: ["input", "old", "music", "circle"],
    char: "\u{1f518}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_circle: {
    keywords: ["shape", "round"],
    char: "\u26aa",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_circle: {
    keywords: ["shape", "button", "round"],
    char: "\u26ab",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  red_circle: {
    keywords: ["shape", "error", "danger"],
    char: "\u{1f534}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  large_blue_circle: {
    keywords: ["shape", "icon", "button"],
    char: "\u{1f535}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  small_orange_diamond: {
    keywords: ["shape", "jewel", "gem"],
    char: "\u{1f538}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  small_blue_diamond: {
    keywords: ["shape", "jewel", "gem"],
    char: "\u{1f539}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  large_orange_diamond: {
    keywords: ["shape", "jewel", "gem"],
    char: "\u{1f536}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  large_blue_diamond: {
    keywords: ["shape", "jewel", "gem"],
    char: "\u{1f537}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  small_red_triangle: {
    keywords: ["shape", "direction", "up", "top"],
    char: "\u{1f53a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_small_square: {
    keywords: ["shape", "icon"],
    char: "\u25aa\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_small_square: {
    keywords: ["shape", "icon"],
    char: "\u25ab\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_large_square: {
    keywords: ["shape", "icon", "button"],
    char: "\u2b1b",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_large_square: {
    keywords: ["shape", "icon", "stone", "button"],
    char: "\u2b1c",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  small_red_triangle_down: {
    keywords: ["shape", "direction", "bottom"],
    char: "\u{1f53b}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_medium_square: {
    keywords: ["shape", "button", "icon"],
    char: "\u25fc\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_medium_square: {
    keywords: ["shape", "stone", "icon"],
    char: "\u25fb\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_medium_small_square: {
    keywords: ["icon", "shape", "button"],
    char: "\u25fe",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_medium_small_square: {
    keywords: ["shape", "stone", "icon", "button"],
    char: "\u25fd",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_square_button: {
    keywords: ["shape", "input", "frame"],
    char: "\u{1f532}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  white_square_button: {
    keywords: ["shape", "input"],
    char: "\u{1f533}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  speaker: {
    keywords: ["sound", "volume", "silence", "broadcast"],
    char: "\u{1f508}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  sound: {
    keywords: ["volume", "speaker", "broadcast"],
    char: "\u{1f509}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  loud_sound: {
    keywords: ["volume", "noise", "noisy", "speaker", "broadcast"],
    char: "\u{1f50a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  mute: {
    keywords: ["sound", "volume", "silence", "quiet"],
    char: "\u{1f507}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  mega: {
    keywords: ["sound", "speaker", "volume"],
    char: "\u{1f4e3}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  loudspeaker: {
    keywords: ["volume", "sound"],
    char: "\u{1f4e2}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  bell: {
    keywords: ["sound", "notification", "christmas", "xmas", "chime"],
    char: "\u{1f514}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  no_bell: {
    keywords: ["sound", "volume", "mute", "quiet", "silent"],
    char: "\u{1f515}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  black_joker: {
    keywords: ["poker", "cards", "game", "play", "magic"],
    char: "\u{1f0cf}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  mahjong: {
    keywords: ["game", "play", "chinese", "kanji"],
    char: "\u{1f004}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  spades: {
    keywords: ["poker", "cards", "suits", "magic"],
    char: "\u2660\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clubs: {
    keywords: ["poker", "cards", "magic", "suits"],
    char: "\u2663\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  hearts: {
    keywords: ["poker", "cards", "magic", "suits"],
    char: "\u2665\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  diamonds: {
    keywords: ["poker", "cards", "magic", "suits"],
    char: "\u2666\ufe0f",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  flower_playing_cards: {
    keywords: ["game", "sunset", "red"],
    char: "\u{1f3b4}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  thought_balloon: {
    keywords: ["bubble", "cloud", "speech", "thinking", "dream"],
    char: "\u{1f4ad}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  right_anger_bubble: {
    keywords: ["caption", "speech", "thinking", "mad"],
    char: "\u{1f5ef}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  speech_balloon: {
    keywords: ["bubble", "words", "message", "talk", "chatting"],
    char: "\u{1f4ac}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  left_speech_bubble: {
    keywords: ["words", "message", "talk", "chatting"],
    char: "\u{1f5e8}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock1: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f550}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock2: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f551}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock3: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f552}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock4: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f553}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock5: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f554}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock6: {
    keywords: ["time", "late", "early", "schedule", "dawn", "dusk"],
    char: "\u{1f555}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock7: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f556}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock8: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f557}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock9: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f558}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock10: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f559}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock11: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f55a}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock12: {
    keywords: [
      "time",
      "noon",
      "midnight",
      "midday",
      "late",
      "early",
      "schedule",
    ],
    char: "\u{1f55b}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock130: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f55c}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock230: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f55d}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock330: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f55e}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock430: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f55f}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock530: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f560}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock630: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f561}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock730: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f562}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock830: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f563}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock930: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f564}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock1030: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f565}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock1130: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f566}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  clock1230: {
    keywords: ["time", "late", "early", "schedule"],
    char: "\u{1f567}",
    fitzpatrick_scale: !1,
    category: "symbols",
  },
  afghanistan: {
    keywords: ["af", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  aland_islands: {
    keywords: ["\xc5land", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1fd}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  albania: {
    keywords: ["al", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  algeria: {
    keywords: ["dz", "flag", "nation", "country", "banner"],
    char: "\u{1f1e9}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  american_samoa: {
    keywords: ["american", "ws", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  andorra: {
    keywords: ["ad", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  angola: {
    keywords: ["ao", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  anguilla: {
    keywords: ["ai", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  antarctica: {
    keywords: ["aq", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  antigua_barbuda: {
    keywords: ["antigua", "barbuda", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  argentina: {
    keywords: ["ar", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  armenia: {
    keywords: ["am", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  aruba: {
    keywords: ["aw", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  australia: {
    keywords: ["au", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  austria: {
    keywords: ["at", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  azerbaijan: {
    keywords: ["az", "flag", "nation", "country", "banner"],
    char: "\u{1f1e6}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bahamas: {
    keywords: ["bs", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bahrain: {
    keywords: ["bh", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bangladesh: {
    keywords: ["bd", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  barbados: {
    keywords: ["bb", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1e7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  belarus: {
    keywords: ["by", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  belgium: {
    keywords: ["be", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  belize: {
    keywords: ["bz", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  benin: {
    keywords: ["bj", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1ef}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bermuda: {
    keywords: ["bm", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bhutan: {
    keywords: ["bt", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bolivia: {
    keywords: ["bo", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  caribbean_netherlands: {
    keywords: ["bonaire", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bosnia_herzegovina: {
    keywords: ["bosnia", "herzegovina", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  botswana: {
    keywords: ["bw", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  brazil: {
    keywords: ["br", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  british_indian_ocean_territory: {
    keywords: [
      "british",
      "indian",
      "ocean",
      "territory",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1ee}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  british_virgin_islands: {
    keywords: [
      "british",
      "virgin",
      "islands",
      "bvi",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1fb}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  brunei: {
    keywords: ["bn", "darussalam", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  bulgaria: {
    keywords: ["bg", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  burkina_faso: {
    keywords: ["burkina", "faso", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  burundi: {
    keywords: ["bi", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cape_verde: {
    keywords: ["cabo", "verde", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1fb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cambodia: {
    keywords: ["kh", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cameroon: {
    keywords: ["cm", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  canada: {
    keywords: ["ca", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  canary_islands: {
    keywords: ["canary", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cayman_islands: {
    keywords: ["cayman", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  central_african_republic: {
    keywords: [
      "central",
      "african",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1e8}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  chad: {
    keywords: ["td", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  chile: {
    keywords: ["flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cn: {
    keywords: [
      "china",
      "chinese",
      "prc",
      "flag",
      "country",
      "nation",
      "banner",
    ],
    char: "\u{1f1e8}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  christmas_island: {
    keywords: ["christmas", "island", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1fd}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cocos_islands: {
    keywords: [
      "cocos",
      "keeling",
      "islands",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1e8}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  colombia: {
    keywords: ["co", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  comoros: {
    keywords: ["km", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  congo_brazzaville: {
    keywords: ["congo", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  congo_kinshasa: {
    keywords: [
      "congo",
      "democratic",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1e8}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cook_islands: {
    keywords: ["cook", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  costa_rica: {
    keywords: ["costa", "rica", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  croatia: {
    keywords: ["hr", "flag", "nation", "country", "banner"],
    char: "\u{1f1ed}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cuba: {
    keywords: ["cu", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  curacao: {
    keywords: ["cura\xe7ao", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cyprus: {
    keywords: ["cy", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  czech_republic: {
    keywords: ["cz", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  denmark: {
    keywords: ["dk", "flag", "nation", "country", "banner"],
    char: "\u{1f1e9}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  djibouti: {
    keywords: ["dj", "flag", "nation", "country", "banner"],
    char: "\u{1f1e9}\u{1f1ef}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  dominica: {
    keywords: ["dm", "flag", "nation", "country", "banner"],
    char: "\u{1f1e9}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  dominican_republic: {
    keywords: ["dominican", "republic", "flag", "nation", "country", "banner"],
    char: "\u{1f1e9}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  ecuador: {
    keywords: ["ec", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  egypt: {
    keywords: ["eg", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  el_salvador: {
    keywords: ["el", "salvador", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1fb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  equatorial_guinea: {
    keywords: ["equatorial", "gn", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  eritrea: {
    keywords: ["er", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  estonia: {
    keywords: ["ee", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  ethiopia: {
    keywords: ["et", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  eu: {
    keywords: ["european", "union", "flag", "banner"],
    char: "\u{1f1ea}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  falkland_islands: {
    keywords: [
      "falkland",
      "islands",
      "malvinas",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1eb}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  faroe_islands: {
    keywords: ["faroe", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1eb}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  fiji: {
    keywords: ["fj", "flag", "nation", "country", "banner"],
    char: "\u{1f1eb}\u{1f1ef}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  finland: {
    keywords: ["fi", "flag", "nation", "country", "banner"],
    char: "\u{1f1eb}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  fr: {
    keywords: ["banner", "flag", "nation", "france", "french", "country"],
    char: "\u{1f1eb}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  french_guiana: {
    keywords: ["french", "guiana", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  french_polynesia: {
    keywords: ["french", "polynesia", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  french_southern_territories: {
    keywords: [
      "french",
      "southern",
      "territories",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f9}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  gabon: {
    keywords: ["ga", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  gambia: {
    keywords: ["gm", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  georgia: {
    keywords: ["ge", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  de: {
    keywords: ["german", "nation", "flag", "country", "banner"],
    char: "\u{1f1e9}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  ghana: {
    keywords: ["gh", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  gibraltar: {
    keywords: ["gi", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  greece: {
    keywords: ["gr", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  greenland: {
    keywords: ["gl", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  grenada: {
    keywords: ["gd", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guadeloupe: {
    keywords: ["gp", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f5}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guam: {
    keywords: ["gu", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guatemala: {
    keywords: ["gt", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guernsey: {
    keywords: ["gg", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guinea: {
    keywords: ["gn", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guinea_bissau: {
    keywords: ["gw", "bissau", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  guyana: {
    keywords: ["gy", "flag", "nation", "country", "banner"],
    char: "\u{1f1ec}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  haiti: {
    keywords: ["ht", "flag", "nation", "country", "banner"],
    char: "\u{1f1ed}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  honduras: {
    keywords: ["hn", "flag", "nation", "country", "banner"],
    char: "\u{1f1ed}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  hong_kong: {
    keywords: ["hong", "kong", "flag", "nation", "country", "banner"],
    char: "\u{1f1ed}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  hungary: {
    keywords: ["hu", "flag", "nation", "country", "banner"],
    char: "\u{1f1ed}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  iceland: {
    keywords: ["is", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  india: {
    keywords: ["in", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  indonesia: {
    keywords: ["flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  iran: {
    keywords: [
      "iran,",
      "islamic",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1ee}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  iraq: {
    keywords: ["iq", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1f6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  ireland: {
    keywords: ["ie", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  isle_of_man: {
    keywords: ["isle", "man", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  israel: {
    keywords: ["il", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  it: {
    keywords: ["italy", "flag", "nation", "country", "banner"],
    char: "\u{1f1ee}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  cote_divoire: {
    keywords: ["ivory", "coast", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  jamaica: {
    keywords: ["jm", "flag", "nation", "country", "banner"],
    char: "\u{1f1ef}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  jp: {
    keywords: ["japanese", "nation", "flag", "country", "banner"],
    char: "\u{1f1ef}\u{1f1f5}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  jersey: {
    keywords: ["je", "flag", "nation", "country", "banner"],
    char: "\u{1f1ef}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  jordan: {
    keywords: ["jo", "flag", "nation", "country", "banner"],
    char: "\u{1f1ef}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kazakhstan: {
    keywords: ["kz", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kenya: {
    keywords: ["ke", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kiribati: {
    keywords: ["ki", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kosovo: {
    keywords: ["xk", "flag", "nation", "country", "banner"],
    char: "\u{1f1fd}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kuwait: {
    keywords: ["kw", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kyrgyzstan: {
    keywords: ["kg", "flag", "nation", "country", "banner"],
    char: "\u{1f1f0}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  laos: {
    keywords: [
      "lao",
      "democratic",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f1}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  latvia: {
    keywords: ["lv", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1fb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  lebanon: {
    keywords: ["lb", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1e7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  lesotho: {
    keywords: ["ls", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  liberia: {
    keywords: ["lr", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  libya: {
    keywords: ["ly", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  liechtenstein: {
    keywords: ["li", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  lithuania: {
    keywords: ["lt", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  luxembourg: {
    keywords: ["lu", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  macau: {
    keywords: ["macao", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  macedonia: {
    keywords: ["macedonia,", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  madagascar: {
    keywords: ["mg", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  malawi: {
    keywords: ["mw", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  malaysia: {
    keywords: ["my", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  maldives: {
    keywords: ["mv", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1fb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mali: {
    keywords: ["ml", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  malta: {
    keywords: ["mt", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  marshall_islands: {
    keywords: ["marshall", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  martinique: {
    keywords: ["mq", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mauritania: {
    keywords: ["mr", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mauritius: {
    keywords: ["mu", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mayotte: {
    keywords: ["yt", "flag", "nation", "country", "banner"],
    char: "\u{1f1fe}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mexico: {
    keywords: ["mx", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1fd}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  micronesia: {
    keywords: [
      "micronesia,",
      "federated",
      "states",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1eb}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  moldova: {
    keywords: ["moldova,", "republic", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  monaco: {
    keywords: ["mc", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mongolia: {
    keywords: ["mn", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  montenegro: {
    keywords: ["me", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  montserrat: {
    keywords: ["ms", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  morocco: {
    keywords: ["ma", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  mozambique: {
    keywords: ["mz", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  myanmar: {
    keywords: ["mm", "flag", "nation", "country", "banner"],
    char: "\u{1f1f2}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  namibia: {
    keywords: ["na", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  nauru: {
    keywords: ["nr", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  nepal: {
    keywords: ["np", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1f5}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  netherlands: {
    keywords: ["nl", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  new_caledonia: {
    keywords: ["new", "caledonia", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  new_zealand: {
    keywords: ["new", "zealand", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  nicaragua: {
    keywords: ["ni", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  niger: {
    keywords: ["ne", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  nigeria: {
    keywords: ["flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  niue: {
    keywords: ["nu", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  norfolk_island: {
    keywords: ["norfolk", "island", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  northern_mariana_islands: {
    keywords: [
      "northern",
      "mariana",
      "islands",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f2}\u{1f1f5}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  north_korea: {
    keywords: ["north", "korea", "nation", "flag", "country", "banner"],
    char: "\u{1f1f0}\u{1f1f5}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  norway: {
    keywords: ["no", "flag", "nation", "country", "banner"],
    char: "\u{1f1f3}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  oman: {
    keywords: ["om_symbol", "flag", "nation", "country", "banner"],
    char: "\u{1f1f4}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  pakistan: {
    keywords: ["pk", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  palau: {
    keywords: ["pw", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  palestinian_territories: {
    keywords: [
      "palestine",
      "palestinian",
      "territories",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f5}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  panama: {
    keywords: ["pa", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  papua_new_guinea: {
    keywords: ["papua", "new", "guinea", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  paraguay: {
    keywords: ["py", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  peru: {
    keywords: ["pe", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  philippines: {
    keywords: ["ph", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  pitcairn_islands: {
    keywords: ["pitcairn", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  poland: {
    keywords: ["pl", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  portugal: {
    keywords: ["pt", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  puerto_rico: {
    keywords: ["puerto", "rico", "flag", "nation", "country", "banner"],
    char: "\u{1f1f5}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  qatar: {
    keywords: ["qa", "flag", "nation", "country", "banner"],
    char: "\u{1f1f6}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  reunion: {
    keywords: ["r\xe9union", "flag", "nation", "country", "banner"],
    char: "\u{1f1f7}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  romania: {
    keywords: ["ro", "flag", "nation", "country", "banner"],
    char: "\u{1f1f7}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  ru: {
    keywords: ["russian", "federation", "flag", "nation", "country", "banner"],
    char: "\u{1f1f7}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  rwanda: {
    keywords: ["rw", "flag", "nation", "country", "banner"],
    char: "\u{1f1f7}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  st_barthelemy: {
    keywords: ["saint", "barth\xe9lemy", "flag", "nation", "country", "banner"],
    char: "\u{1f1e7}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  st_helena: {
    keywords: [
      "saint",
      "helena",
      "ascension",
      "tristan",
      "cunha",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f8}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  st_kitts_nevis: {
    keywords: [
      "saint",
      "kitts",
      "nevis",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f0}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  st_lucia: {
    keywords: ["saint", "lucia", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  st_pierre_miquelon: {
    keywords: [
      "saint",
      "pierre",
      "miquelon",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f5}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  st_vincent_grenadines: {
    keywords: [
      "saint",
      "vincent",
      "grenadines",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1fb}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  samoa: {
    keywords: ["ws", "flag", "nation", "country", "banner"],
    char: "\u{1f1fc}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  san_marino: {
    keywords: ["san", "marino", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  sao_tome_principe: {
    keywords: [
      "sao",
      "tome",
      "principe",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f8}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  saudi_arabia: {
    keywords: ["flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  senegal: {
    keywords: ["sn", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  serbia: {
    keywords: ["rs", "flag", "nation", "country", "banner"],
    char: "\u{1f1f7}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  seychelles: {
    keywords: ["sc", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  sierra_leone: {
    keywords: ["sierra", "leone", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  singapore: {
    keywords: ["sg", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  sint_maarten: {
    keywords: [
      "sint",
      "maarten",
      "dutch",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f8}\u{1f1fd}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  slovakia: {
    keywords: ["sk", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  slovenia: {
    keywords: ["si", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  solomon_islands: {
    keywords: ["solomon", "islands", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1e7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  somalia: {
    keywords: ["so", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  south_africa: {
    keywords: ["south", "africa", "flag", "nation", "country", "banner"],
    char: "\u{1f1ff}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  south_georgia_south_sandwich_islands: {
    keywords: [
      "south",
      "georgia",
      "sandwich",
      "islands",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1ec}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  kr: {
    keywords: ["south", "korea", "nation", "flag", "country", "banner"],
    char: "\u{1f1f0}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  south_sudan: {
    keywords: ["south", "sd", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  es: {
    keywords: ["spain", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  sri_lanka: {
    keywords: ["sri", "lanka", "flag", "nation", "country", "banner"],
    char: "\u{1f1f1}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  sudan: {
    keywords: ["sd", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1e9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  suriname: {
    keywords: ["sr", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  swaziland: {
    keywords: ["sz", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  sweden: {
    keywords: ["se", "flag", "nation", "country", "banner"],
    char: "\u{1f1f8}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  switzerland: {
    keywords: ["ch", "flag", "nation", "country", "banner"],
    char: "\u{1f1e8}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  syria: {
    keywords: [
      "syrian",
      "arab",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f8}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  taiwan: {
    keywords: ["tw", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tajikistan: {
    keywords: ["tj", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1ef}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tanzania: {
    keywords: [
      "tanzania,",
      "united",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f9}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  thailand: {
    keywords: ["th", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  timor_leste: {
    keywords: ["timor", "leste", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f1}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  togo: {
    keywords: ["tg", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tokelau: {
    keywords: ["tk", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f0}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tonga: {
    keywords: ["to", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f4}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  trinidad_tobago: {
    keywords: ["trinidad", "tobago", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f9}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tunisia: {
    keywords: ["tn", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tr: {
    keywords: ["turkey", "flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  turkmenistan: {
    keywords: ["flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  turks_caicos_islands: {
    keywords: [
      "turks",
      "caicos",
      "islands",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1f9}\u{1f1e8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  tuvalu: {
    keywords: ["flag", "nation", "country", "banner"],
    char: "\u{1f1f9}\u{1f1fb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  uganda: {
    keywords: ["ug", "flag", "nation", "country", "banner"],
    char: "\u{1f1fa}\u{1f1ec}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  ukraine: {
    keywords: ["ua", "flag", "nation", "country", "banner"],
    char: "\u{1f1fa}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  united_arab_emirates: {
    keywords: [
      "united",
      "arab",
      "emirates",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1e6}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  uk: {
    keywords: [
      "united",
      "kingdom",
      "great",
      "britain",
      "northern",
      "ireland",
      "flag",
      "nation",
      "country",
      "banner",
      "british",
      "UK",
      "english",
      "england",
      "union jack",
    ],
    char: "\u{1f1ec}\u{1f1e7}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  england: {
    keywords: ["flag", "english"],
    char: "\u{1f3f4}\u{e0067}\u{e0062}\u{e0065}\u{e006e}\u{e0067}\u{e007f}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  scotland: {
    keywords: ["flag", "scottish"],
    char: "\u{1f3f4}\u{e0067}\u{e0062}\u{e0073}\u{e0063}\u{e0074}\u{e007f}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  wales: {
    keywords: ["flag", "welsh"],
    char: "\u{1f3f4}\u{e0067}\u{e0062}\u{e0077}\u{e006c}\u{e0073}\u{e007f}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  us: {
    keywords: [
      "united",
      "states",
      "america",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1fa}\u{1f1f8}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  us_virgin_islands: {
    keywords: [
      "virgin",
      "islands",
      "us",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1fb}\u{1f1ee}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  uruguay: {
    keywords: ["uy", "flag", "nation", "country", "banner"],
    char: "\u{1f1fa}\u{1f1fe}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  uzbekistan: {
    keywords: ["uz", "flag", "nation", "country", "banner"],
    char: "\u{1f1fa}\u{1f1ff}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  vanuatu: {
    keywords: ["vu", "flag", "nation", "country", "banner"],
    char: "\u{1f1fb}\u{1f1fa}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  vatican_city: {
    keywords: ["vatican", "city", "flag", "nation", "country", "banner"],
    char: "\u{1f1fb}\u{1f1e6}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  venezuela: {
    keywords: [
      "ve",
      "bolivarian",
      "republic",
      "flag",
      "nation",
      "country",
      "banner",
    ],
    char: "\u{1f1fb}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  vietnam: {
    keywords: ["viet", "nam", "flag", "nation", "country", "banner"],
    char: "\u{1f1fb}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  wallis_futuna: {
    keywords: ["wallis", "futuna", "flag", "nation", "country", "banner"],
    char: "\u{1f1fc}\u{1f1eb}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  western_sahara: {
    keywords: ["western", "sahara", "flag", "nation", "country", "banner"],
    char: "\u{1f1ea}\u{1f1ed}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  yemen: {
    keywords: ["ye", "flag", "nation", "country", "banner"],
    char: "\u{1f1fe}\u{1f1ea}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  zambia: {
    keywords: ["zm", "flag", "nation", "country", "banner"],
    char: "\u{1f1ff}\u{1f1f2}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  zimbabwe: {
    keywords: ["zw", "flag", "nation", "country", "banner"],
    char: "\u{1f1ff}\u{1f1fc}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  united_nations: {
    keywords: ["un", "flag", "banner"],
    char: "\u{1f1fa}\u{1f1f3}",
    fitzpatrick_scale: !1,
    category: "flags",
  },
  pirate_flag: {
    keywords: ["skull", "crossbones", "flag", "banner"],
    char: "\u{1f3f4}\u200d\u2620\ufe0f",
    fitzpatrick_scale: !1,
    category: "flags",
  },
});
