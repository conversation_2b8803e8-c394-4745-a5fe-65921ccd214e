<template>
  <BaseContent scrollable>
    <div class="row q-pa-md">
      <q-card class="col">
        <q-card-section>
          <div class="text-h6">
            {{ t("admin.Add") + t("admin.Information") }}
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form ref="recordDetailForm">
            <div class="row q-my-md">
              <q-file
                v-model="fileList"
                class="col"
                name="img_files"
                filled
                multiple
                use-chips
                label="上传资料附件，可以一次上传多个"
              />
            </div>
            <div v-if="fileData.length" class="row">
              <q-img
                v-for="item in fileData"
                :src="item.url"
                spinner-color="white"
                style="height: 140px; max-width: 150px"
                class="q-ma-md"
              />
            </div>

            <div class="row q-my-md">
              <q-input
                v-model="recordDetail.title"
                class="col q-mx-sm"
                :label="t('admin.Title')"
              />
              <q-input
                v-model.number="recordDetail.weight"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.Sort')"
              />
            </div>
            <div class="row q-my-md">
              <q-input
                v-model="recordDetail.category_name"
                :label="
                  t('admin.Select') + t('admin.Category') + t('admin.Name')
                "
                :input-style="{
                  fontSize: ' 16px',
                  lineHeight: '28px',
                  fontWeight: '400',
                }"
                readonly
                class="col q-mx-sm"
              >
                <template #append>
                  <q-btn
                    dense
                    color="primary"
                    :label="t('content.Select')"
                    @click="showSelectDialog"
                  />
                </template>
              </q-input>
              <q-field
                dense
                class="col q-mx-sm"
                :label="t('admin.Category') + t('admin.FixTop')"
                stack-label
              >
                <template #control>
                  <q-option-group
                    v-model="recordDetail.fix_top"
                    :options="statusDict"
                    color="primary"
                    inline
                  />
                </template>
              </q-field>
            </div>
            <div class="row q-my-md">
              <q-input
                v-model="recordDetail.seo_kw"
                class="col q-mx-sm"
                :label="t('admin.Seo') + t('admin.Keyword')"
              />
            </div>
            <div class="row q-my-md">
              <q-input
                v-model="recordDetail.slogan"
                class="col q-mx-sm"
                :label="t('admin.Slogan') + t('admin.Description')"
              />
            </div>
            <div class="row">
              <Tinymce
                :value="recordDetail.content"
                class="col"
                @getContent="getContent"
              />
            </div>
          </q-form>
        </q-card-section>

        <q-separator />

        <q-card-actions align="left">
          <q-btn
            :label="t('admin.Save')"
            color="primary"
            @click="handleCreate"
          />
          <q-btn
            v-close-popup
            :label="t('admin.Cancel')"
            color="negative"
            @click="closeTab"
          />
        </q-card-actions>
      </q-card>
    </div>
    <SelectCategory ref="selectItemDialog" @handleSelectItem="handleSelect" />
  </BaseContent>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import Tinymce from "src/components/Editor/TinyMce.vue";
import SelectCategory from "src/pages/faq/modules/SelectFaqDialog.vue";
import { useTagViewStore } from "src/stores/tagView";
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

const tagViewStore = useTagViewStore();
const route = useRoute();
const { t } = useI18n();
const url = {
  create: "/api/info/faq",
  edit: "/api/info/faq",
  upload: "/api/info/faqAttachment",
};
const imgExt = [
  "bmp",
  "jpg",
  "jpeg",
  "png",
  "tif",
  "gif",
  "svg",
  "webp",
  "avif",
  "apng",
];
const statusDict = [
  { label: "目录置顶", value: true },
  { label: "不置顶", value: false },
];
const recordDetailForm = ref();
const selectItemDialog = ref(null);
const showSelectDialog = () => {
  selectItemDialog.value.show("FAQ");
};
const handleSelect = (item) => {
  console.log(item);
  recordDetail.category_id = item.id;
  recordDetail.category_name = item.name;
};

const fileList = ref();
const fileData = computed(() => {
  const res = reactive([]);
  if (fileList.value) {
    fileList.value.forEach((item) => {
      if (imgExt.includes(item.type.split("/")[1])) {
        const tmp = {
          name: item.name,
          url: URL.createObjectURL(item),
        };
        res.push(tmp);
      } else {
        const tmp = {
          name: item.name,
          url: "img/fileImg.svg",
        };
        res.push(tmp);
      }
      // res.push(tmp)
    });
  }
  return res;
});

const recordDetail = reactive({
  weight: 1,
  fix_top: false,
  category_type: "FAQ",
});

const handleCreate = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.create) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await postAction(url.create, recordDetail);
    if (res.code === 200) {
      console.log(res);
      if (fileList.value.length > 0) {
        fileList.value.forEach((item) => {
          const uploadData = new FormData();
          uploadData.append("file", item);
          uploadData.append("parent_id", res.data.id);
          postAction(url.upload, uploadData);
        });
      }
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  recordDetail.content = v;
};

const closeTab = () => {
  tagViewStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
