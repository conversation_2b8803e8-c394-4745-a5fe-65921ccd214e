<template>
  <q-dialog v-model="recordDetailVisible" position="right">
    <div style="width: 60vw">
      <!-- 仿百度地图官网 -->
      <BMap
        ref="map"
        width="640px"
        height="720px"
        ak="0qcW0PsvXVRqNaaw2K4jhDWANKsS7xF6"
        enable-scroll-wheel-zoom
        :center="location.point || undefined"
        :map-type="mapType"
        @initd="get"
      >
        <BZoom :offset="{ x: 22, y: 40 }" />
        <BCityList :offset="{ x: 20, y: 20 }" />
        <BScale anchor="BMAP_ANCHOR_BOTTOM_RIGHT" />
        <BNavigation3d anchor="BMAP_ANCHOR_BOTTOM_RIGHT" :offset="{ x: 10, y: 140 }" />
        <template v-if="!isLoading">
          <BMarker :position="location.point" />
          <BCircle
            stroke-style="solid"
            stroke-color="#0099ff"
            :stroke-opacity="0.8"
            fill-color="#0099ff"
            :fill-opacity="0.5"
            :center="location.point"
            :radius="location.accuracy"
          />
        </template>
        <BControl
          anchor="BMAP_ANCHOR_TOP_RIGHT"
          :offset="{ x: 20, y: 20 }"
          class="custom-control"
        >
          <select
            id="" v-model="mapType"
            class="select"
            name=""
          >
            <option value="BMAP_NORMAL_MAP">普通地图</option>
            <option value="BMAP_SATELLITE_MAP">卫星地图</option>
            <option value="BMAP_EARTH_MAP">地球模式</option>
          </select>
        </BControl>
        <BControl
          anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
          :offset="{ x: 22, y: 103 }"
          class="custom-control location-btn"
          @click="get"
        >
          <span v-if="isLoading" class="location-text">定位中..</span>
          <LocationIcon v-if="!isLoading" />
        </BControl>
      </BMap>
    </div>
  </q-dialog>
</template>

<script setup>
import { ref } from 'vue'
import {
  BMap,
  BZoom,
  BScale,
  BCityList,
  BNavigation3d,
  BControl,
  BCircle,
  BMarker,
  useBrowserLocation
} from 'vue3-baidu-map-gl'

const recordDetailVisible = ref(false)
const loading = ref(false)
const show = () => {
  loading.value = true
  recordDetailVisible.value = true
  loading.value = false
}

const map = ref()
const { get, location, isLoading, isError, status } = useBrowserLocation(null, () => {
  map.value.resetCenter()
})
const mapType = ref('BMAP_NORMAL_MAP')

defineExpose({
  show,
})
</script>

<style scoped>

</style>
