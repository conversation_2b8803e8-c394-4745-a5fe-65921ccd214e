<template>
  <q-dialog v-model="recordDetailVisible">
    <q-card style="width: 800px; max-width: 80vw;">
      <q-img :src="src" width="100%" />
    </q-card>
  </q-dialog>
</template>

<script setup>
import { toRefs, ref } from 'vue'
const props = defineProps({
  src: {
    type: String,
    required: false,
    default: '',
  },
})
const { src } = toRefs(props)
const recordDetailVisible = ref(false)
const show = () => {
  recordDetailVisible.value = true
}

defineExpose({
  show
})
</script>
