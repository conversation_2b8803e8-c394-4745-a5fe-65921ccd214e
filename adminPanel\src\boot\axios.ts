import { boot } from "quasar/wrappers";
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { Notify, Dialog } from "quasar";
import { useUserStore } from "src/stores/user";
import { responseData } from "src/types";

type ApiResponse<T = any> = AxiosResponse<responseData<T>>;

interface ApiError<T = any> {
  response?: {
    status: number;
    data: responseData<T>;
    request: {
      responseURL: string;
    };
  };
  message?: string;
  config?: AxiosRequestConfig;
  isAxiosError: boolean;
}

interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)

const api = axios.create({
  baseURL: process.env.API,
  timeout: 50000,
  withCredentials: false,
  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
});

export default boot(({ app, router, store }) => {
  const userStore = useUserStore();
  // 请求拦截
  api.interceptors.request.use(
    (config) => {
      const token = userStore.GetToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      Notify.create({
        type: "negative",
        message: error.message || "Request Error",
      });
      return Promise.reject(error);
    }
  );
  // 响应拦截
  api.interceptors.response.use(
    (response: ApiResponse<any>) => {
      // Handle token refresh logic
      if (response.headers["refresh-token"] && response.data.data?.token) {
        const refreshResponse = response.data.data as RefreshTokenResponse;
        userStore.SetToken(response.headers["refresh-token"]);
        Notify.create({
          type: "positive",
          message: "Token Refresh Success",
        });
        return api(response.config);
      }
      if (response.headers["content-download"] && response.status === 200) {
        return response;
      }
      const responseData = response.data;
      const { status } = response;
      if (
        status === 504 ||
        status === 401 ||
        status === 514 ||
        status === 402
      ) {
        userStore.HandleLogout();
        router.push({ name: "Login" });
      }
      if (status === 200) {
        return response;
      } else {
        switch (status) {
          case 0:
            if (responseData.data && responseData.data.reload) {
              Dialog.create({
                title: "Authentication Failed",
                message: response.data.msg || "Please ReLogin",
                persistent: true,
                ok: {
                  push: true,
                  color: "negative",
                  label: "ReLogin",
                },
              }).onOk(() => {
                userStore.HandleLogout();
                // store.dispatch('user/HandleLogout')
                router.push({ name: "Login" });
              });
            } else {
              Notify.create({
                type: "negative",
                message: response.data.msg || "Operation Failed",
              });
              return response;
            }
          // eslint-disable-next-line no-fallthrough
          default:
            return response;
        }
      }
    },
    (error: ApiError<any>) => {
      if (!error.response) {
        return Promise.reject({
          ...error,
          message: error.message || "Network Error",
          isAxiosError: true,
        });
      }

      switch (error.response.status) {
        case 500:
          Dialog.create({
            title: "Error",
            message: "Data Exception, Please Return",
            persistent: true,
            ok: {
              push: true,
              color: "negative",
              label: "Confirm",
            },
          }).onOk(() => {
            // userStore.HandleLogout()
            // store.dispatch('user/HandleLogout')
            router.push({ name: "index" });
          });
          return Promise.reject(error);
        case 400:
          console.log("服务器数据错误 [" + error.response.data.code + "]");
          Notify.create({
            type: "negative",
            message: error.response.data.msg,
          });
          return Promise.resolve(error);
        case 401:
          Notify.create({
            type: "negative",
            message: "未获得相应的权限" + error.response.request.responseURL,
          });
          userStore.HandleLogout();
          return Promise.reject(error);
        case 404:
          console.log(
            "请求地址不存在 [" + error.response.request.responseURL + "]"
          );
          Notify.create({
            type: "negative",
            message:
              "Request Address NotFound" +
              " " +
              error.response.request.responseURL,
          });
          return Promise.reject(error);
        case 514:
          userStore.HandleLogout();
          return Promise.reject(error);
      }
      // 500的情况
      // if (error + '' === 'Error: Request failed with status code 500') {
      //   Dialog.create({
      //     title: $t('Error'),
      //     message: $t('Data') + $t('Exception') + ',' + $t('Please') + $t('ReLogin'),
      //     persistent: true,
      //     ok: {
      //       push: true,
      //       color: 'negative',
      //       label: $t('Logout'),
      //     },
      //   }).onOk(() => {
      //     // userStore.HandleLogout()
      //     // store.dispatch('user/HandleLogout')
      //     router.push({ name: 'dashboard' })
      //   })
      // }
      // 超时
      // if (error + '' === 'Error: timeout of 50000ms exceeded') {
      //   Notify.create({
      //     type: 'negative',
      //     message: $t('Operation') + $t('Timeout'),
      //   })
      // }
      // 网络错误情况，比如后台没有对应的接口
      // if (error + '' === 'Error: Network Error') {
      //   router.push({ name: 'notFound' })
      // }
      // else if (error.response && error.response.status === 404) {
      //   console.log('请求地址不存在 [' + error.response.request.responseURL + ']')
      //   Notify.create({
      //     type: 'negative',
      //     message: $t('Request') + $t('Address') + $t('NotFound') + ' ' + error.response.request.responseURL,
      //   })
      // }
      return Promise.reject(error);
    }
  );
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
});

export { api };
