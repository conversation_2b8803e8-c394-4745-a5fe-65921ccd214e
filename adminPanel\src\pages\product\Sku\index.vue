<template>
  <div class="row q-gutter-md">
    <div class="col">
      <q-table
        v-model:pagination="pagination"
        row-key="id"
        class="sku-table-stick"
        separator="cell"
        :rows="tableData"
        :columns="columns"
        :rows-per-page-options="pageOptions"
        :loading="loading"
        @request="onRequest"
      >
        <template #body-cell-name="props">
          <q-td
            :props="props"
            :auto-width="false"
            style="max-width: 480px; white-space: pre-line"
          >
            <span v-if="props.row.name"> {{ props.row.name }}</span>
            <div v-else>
              <div class="no-translate">未有翻译信息</div>
              ({{ props.row.parent.name }})
            </div>
          </q-td>
        </template>

        <template #body-cell-model="props">
          <q-td
            :props="props"
            :auto-width="false"
            style="max-width: 480px; white-space: pre-line"
          >
            <span v-if="props.row.model"> {{ props.row.model }}</span>
            <div v-else>
              <div class="no-translate">未有翻译信息</div>
              ({{ props.row.parent.model }})
            </div>
          </q-td>
        </template>

        <template #body-cell-size="props">
          <q-td
            :props="props"
            :auto-width="false"
            style="max-width: 480px; white-space: pre-line"
          >
            <span v-if="props.row.size"> {{ props.row.size }}</span>
            <div v-else>
              <div class="no-translate">未有翻译信息</div>
              ({{ props.row.parent.size }})
            </div>
          </q-td>
        </template>

        <template #body-cell-package="props">
          <q-td
            :props="props"
            :auto-width="false"
            style="max-width: 480px; white-space: pre-line"
          >
            <span v-if="props.row.package"> {{ props.row.package }}</span>
            <div v-else>
              <div class="no-translate">未有翻译信息</div>
              ({{ props.row.parent.package }})
            </div>
          </q-td>
        </template>

        <template #body-cell-specification="props">
          <q-td
            :props="props"
            :auto-width="false"
            style="max-width: 480px; white-space: pre-line"
          >
            <span v-if="props.row.specification">
              {{ props.row.specification }}</span
            >
            <div v-else>
              <div class="no-translate">未有翻译信息</div>
              ({{ props.row.parent.specification }})
            </div>
          </q-td>
        </template>

        <template #body-cell-preview="props">
          <q-td :props="props">
            <ImageView
              v-if="props.row?.preview !== ''"
              :src="props.row.preview"
              :thumb-src="props.row.preview"
              :preview="true"
            />
            <div v-else>暂无图片</div>
          </q-td>
        </template>

        <template #body-cell-is_active="props">
          <q-td :props="props">
            <q-chip
              v-if="props.row.is_active"
              color="positive"
              class="text-white"
              >启用</q-chip
            >
            <q-chip v-else>停用</q-chip>
          </q-td>
        </template>

        <template #body-cell-actions="props">
          <q-td :props="props">
            <div class="q-gutter-xs">
              <q-btn-group>
                <q-btn
                  color="primary"
                  :label="t('admin.Edit')"
                  size="xs"
                  @click="showEditForm(props.row)"
                />
                <q-btn
                  v-if="props.row.is_active && props.row.code === 'default'"
                  color="warning"
                  :label="t('admin.PutOff')"
                  size="xs"
                  @click="handleDetailStatus(props.row)"
                />
                <q-btn
                  v-if="!props.row.is_active && props.row.code === 'default'"
                  color="positive"
                  :label="t('admin.PutOn')"
                  size="xs"
                  @click="handleDetailStatus(props.row)"
                />
                <q-btn
                  v-if="props.row.code === 'default'"
                  color="negative"
                  :label="t('admin.Delete')"
                  size="xs"
                  @click="handleDelete(props.row)"
                />
              </q-btn-group>
            </div>
          </q-td>
        </template>
      </q-table>
      <RecordDetail
        ref="recordDetailDialog"
        :parent-id="parentId"
        :code="code"
        @handleFinish="handleFinish"
      />
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { putAction } from "src/api/manage";
import ImageView from "src/components/ImgPreview/index.vue";
import useTableData from "src/composables/useTableData";
import RecordDetail from "src/pages/product/Sku/modules/recordDetail.vue";
import { computed, toRefs } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  parentId: {
    type: Number,
    required: true,
  },
  code: {
    type: String,
    required: false,
    default: "default",
  },
});
const { parentId, code } = toRefs(props);

const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/business/productSku/lang",
  create: "/api/business/productSku",
  edit: "/api/business/productSku",
  delete: "/api/business/productSku",
};
const columns = computed(() => {
  return [
    {
      name: "name",
      required: true,
      align: "center",
      label: t("admin.Name"),
      field: "name",
    },
    {
      name: "preview",
      align: "center",
      label: t("admin.Cover"),
      field: "preview",
    },
    {
      name: "model",
      align: "center",
      label: t("admin.Model"),
      field: "model",
    },
    { name: "size", align: "center", label: t("admin.Size"), field: "size" },
    {
      name: "specification",
      align: "center",
      label: t("admin.Specification"),
      field: "specification",
    },
    {
      name: "package",
      align: "center",
      label: t("admin.Package"),
      field: "package",
    },
    {
      name: "is_active",
      align: "center",
      label: t("admin.Status"),
      field: "is_active",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  showAddForm,
  showEditForm,
  getTableData,
  handleFinish,
  handleDelete,
} = useTableData(url);

const freshData = async () => {
  pagination.value.page_size = 9999;
  pagination.value.order = "updated_at";
  queryParams.value = {
    parent_id: parentId.value,
    code: code.value,
  };
  await getTableData();
};

const handleDetailStatus = async (item) => {
  $q.dialog({
    title: t("admin.Confirm"),
    message: t("admin.Confirm") + t("admin.Change") + t("admin.Status") + "?",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    item.is_active = !item.is_active;
    console.log(item, "更新");
    const { code, message } = await putAction(url.edit, item);
    if (code === 200) {
      $q.notify({
        type: "positive",
        message: message,
      });
    }
    freshData();
  });
};
defineExpose({
  freshData,
  showAddForm,
});
</script>

<style lang="scss" scoped>
.no-translate {
  font-size: 16px;
  color: #e67e23;
  font-weight: 600;
}
</style>
