<template>
  <q-dialog v-model="recordDetailVisible" full-height position="right">
    <q-card style="width: 800px; max-width: 80vw; height: 100%">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Menu") }} :

          {{ recordDetail.value.title }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input
              v-model="recordDetail.value.icon"
              class="col q-mx-sm"
              :label="t('admin.Icon')"
              clearable
            >
              <template #before>
                <q-icon
                  :name="recordDetail.value.icon"
                  size="35px"
                  class="q-mr-sm"
                />
              </template>
              <template #prepend>
                <q-icon name="insert_emoticon" class="cursor-pointer">
                  <q-popup-proxy v-model="iconData.showIconPicker">
                    <q-input
                      v-model="iconData.filter"
                      label="Filter"
                      outlined
                      clearable
                      dense
                      class="q-ma-md"
                    />
                    <QIconPicker
                      v-model="recordDetail.value.icon"
                      v-model:model-pagination="iconData.pagination"
                      :icons="iconData.icons"
                      :filter="iconData.filter"
                      tooltips
                      style="
                        height: 300px;
                        width: 300px;
                        background-color: white;
                      "
                    />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
            <q-input
              v-model.number="recordDetail.value.order"
              class="col q-mx-sm"
              type="number"
              :rules="[(val) => val >= 1 || t('admin.SortRule')]"
              :label="t('admin.Sort')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.title"
              class="col q-mx-sm"
              :label="t('admin.Menu') + t('admin.Name')"
              :rules="[
                (val) => (val && val.length > 0) || t('admin.NeetInput'),
              ]"
              hint="大写字母开头"
            />
            <q-input
              v-model="recordDetail.value.name"
              class="col q-mx-sm"
              :label="t('admin.Menu') + t('admin.Code')"
              :rules="[
                (val) => (val && val.length > 0) || t('admin.NeetInput'),
              ]"
              hint="小写字母开头，路由跳转依据"
            />
            <q-input
              v-model="recordDetail.value.parent"
              class="col q-mx-sm"
              :disable="isParent ? false : true"
              :label="t('admin.Parent') + t('admin.Code')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.path"
              class="col q-mx-sm"
              label="URL"
            />
            <q-input
              v-model="recordDetail.value.redirect"
              class="col q-mx-sm"
              :label="t('admin.Redirect')"
            />
            <q-input
              v-model="recordDetail.value.component"
              class="col q-mx-sm"
              :label="t('admin.Component')"
            />
          </div>

          <div class="row">
            <q-field class="col q-mx-sm" :label="t('admin.IsLink')" stack-label>
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.is_link"
                  :options="dict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
            <q-field
              class="col q-mx-sm"
              :label="t('admin.KeepAlive')"
              stack-label
            >
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.keep_alive"
                  :options="dict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
            <q-field class="col q-mx-sm" :label="t('admin.Hidden')" stack-label>
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.hidden"
                  :options="dict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
            <q-field class="col q-mx-sm" :label="t('admin.Active')" stack-label>
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.active"
                  :options="dict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
          </div>
          <q-input
            v-model="recordDetail.value.memo"
            type="textarea"
            :label="t('admin.Memo')"
          />
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleAddOrEdit"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
defineOptions({ name: "UserDialog" });
import { QIconPicker } from "@quasar/quasar-ui-qiconpicker";
import materialIcons from "@quasar/quasar-ui-qiconpicker/src/components/icon-set/material-icons";
import { useQuasar } from "quasar";
import useRecordDetail from "src/composables/useRecordDetail";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/menu",
  create: "/api/menu",
  edit: "/api/menu",
};
const dict = [
  { label: "是", value: "yes" },
  { label: "否", value: "no" },
];
const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
  handleQueryById,
  handleAddOrEdit,
} = useRecordDetail(url, emit);

const isParent = ref(false);
const parentName = ref("");

const show = (row) => {
  recordDetail.value = {};
  loading.value = true;
  recordDetailVisible.value = true;
  if (row && row.id) {
    handleQueryById([row.id]);
  } else {
    recordDetail.value = {};
    recordDetail.value.parent = parentName.value;
    recordDetailVisible.value = true;
    loading.value = false;
  }
};

defineExpose({
  show,
  formType,
  isParent,
  parentName,
  recordDetail,
});
const iconData = ref({
  filter: "",
  icons: materialIcons.icons,
  showIconPicker: false,
  pagination: {
    itemsPerPage: 35,
    page: 0,
  },
});
</script>
