<template>
  <q-card class="col">
    <q-card-section>
      <div class="row">
        <div class="text-h6 col">
          {{ $t("admin.Product") + $t("admin.Attributes") + $t("admin.List") }}
        </div>
        <div class="col-auto">
          <q-btn
            color="primary"
            size="sm"
            :label="t('admin.Add') + t('admin.Product') + t('admin.Attributes')"
            @click="showAddForm"
          />
        </div>
      </div>
    </q-card-section>
    <q-card-section>
      <q-table
        v-if="tableData"
        row-key="id"
        grid
        :hide-pagination="true"
        :rows-per-page-options="pageOptions"
        :rows="tableData"
        :loading="loading"
        @request="onRequest"
      >
        <template #item="props">
          <div class="q-pa-xs col-xs-6 col-sm-4 col-md-4">
            <q-card>
              <q-card-section>
                <div class="row">
                  <div class="col-auto attr-label">
                    {{ $t("admin.AttributeName") }}
                  </div>
                  <div class="col">
                    <div v-if="language === 'default'">
                      {{ props.row.name }}
                    </div>
                    <div v-else>
                      <div
                        v-if="props.row.name === ''"
                        class="text-bold text-amber"
                      >
                        未有翻译
                      </div>
                      <div v-else>{{ props.row.name }}</div>
                      <div class="attr-label">
                        {{ props.row.parent.name }}(默认值)
                      </div>
                    </div>
                  </div>
                  <div class="col-auto">
                    <q-btn-group push>
                      <q-btn
                        size="xs"
                        color="primary"
                        icon="edit"
                        @click="showEditForm(props.row)"
                      />
                      <q-btn
                        size="xs"
                        color="negative"
                        icon="delete"
                        @click="handleDelete(props.row)"
                      />
                    </q-btn-group>
                  </div>
                </div>
                <div class="row">
                  <div class="col-auto attr-label">
                    {{ $t("admin.Attributes") + $t("admin.Value") }}
                  </div>
                  <div class="col">
                    <div v-if="language === 'default'">
                      {{ props.row.value }}
                    </div>
                    <div v-else>
                      <div
                        v-if="props.row.value === ''"
                        class="text-bold text-amber"
                      >
                        未有翻译
                      </div>
                      <div v-else>{{ props.row.value }}</div>
                      <div class="">{{ props.row.parent.value }}(默认值)</div>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </template>
      </q-table>
      <span v-else> <q-icon name="warning" />{{ $t("admin.NotFound") }}</span>
    </q-card-section>
    <AttributeDetail
      v-if="parentId"
      ref="recordDetailDialog"
      :parent-id="parentId"
      :language="language"
      @handleFinish="handleAttributes"
    />
  </q-card>
</template>

<script setup>
import { useQuasar } from "quasar";
import { postAction, putAction } from "src/api/manage";
import useTableData from "src/composables/useTableData";
import AttributeDetail from "src/pages/product/modules/attributeDetail.vue";
import { toRefs } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  parentId: {
    type: Number,
    required: true,
  },
  language: {
    type: String,
    required: false,
    default: "default",
  },
});
const { parentId, language } = toRefs(props);

const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/business/productAttributes/lang",
  create: "/api/business/productAttributes",
  edit: "/api/business/productAttributes",
  delete: "/api/business/productAttributes",
};
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  showAddForm,
  showEditForm,
  getTableData,
  handleDelete,
} = useTableData(url);

const freshData = async (locale) => {
  pagination.value.page_size = 9999;
  pagination.value.order = "sort";
  queryParams.value = {
    parent_id: parentId.value,
    code: language.value,
  };
  if (locale) {
    queryParams.value.code = locale;
  }
  await getTableData();
};

const handleAttributes = async (item, idx) => {
  if (item.id) {
    if (url === undefined || !url.edit) {
      $q.notify({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await putAction(url.edit, item);
    if (res.code === 200) {
      $q.notify({
        type: "positive",
        message: res.message,
      });
    }
  } else {
    if (!url.create) {
      $q.notify({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await postAction(url.create, item);
    if (res.code === 200) {
      $q.notify({
        type: "positive",
        message: res.message,
      });
    }
  }
  await getTableData();
};

defineExpose({
  freshData,
  showAddForm,
});
</script>

<style scoped lang="scss">
.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
