<template>
  <BaseContent scrollable>
    <div class="q-ma-md">
      <div class="row">
        <div class="col-6 q-mr-sm">
          <q-card v-if="itemDetail?.id">
            <q-card-section>
              <div class="row q-my-sm">
                <div class="text-h6 col" style="margin-bottom: 10px">
                  {{ t("admin.Faq") + t("admin.Title") }}:
                  {{ itemDetail.title }}
                  <q-chip
                    v-if="itemDetail.fix_top"
                    square
                    outline
                    color="positive"
                    label="目录置顶"
                    size="sm"
                    class="q-mx-md"
                  />
                </div>
                <div class="col-auto">
                  <q-btn
                    color="primary"
                    size="sm"
                    :label="t('admin.Edit')"
                    @click="editFaqItem"
                  />
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">信息目录</div>
                <div class="col-auto">
                  {{ itemDetail.category_name }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">信息索引</div>
                <div class="col-auto">
                  {{ itemDetail.slogan }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">SEO关键词</div>
                <div class="col-auto">
                  {{ itemDetail.seo_kw }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">创建时间</div>
                <div class="col-auto">
                  {{ showDateTime(itemDetail.created_at) }}
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">更新时间</div>
                <div class="col-auto">
                  {{ showDateTime(itemDetail.updated_at) }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
        <div class="col q-mr-sm">
          <q-card v-if="itemDetail?.id" class="card-size">
            <q-card-section>
              <div class="row q-my-sm">
                <div class="text-h6 col">
                  {{ t("admin.Faq") + t("admin.Attachment") }}
                </div>
                <div class="col-auto">
                  <q-btn
                    color="primary"
                    size="sm"
                    :label="t('admin.Upload') + t('admin.Attachment')"
                    @click="showUploader"
                  />
                </div>
              </div>
              <div v-if="itemDetail?.attachment?.length" class="col">
                <div
                  v-for="item in itemDetail.attachment"
                  :key="item.id"
                  class="row"
                >
                  <div
                    class="col-8 filename"
                    @click="handleFileDownload(item.link)"
                  >
                    <span class="filename">{{ item.filename }}</span>
                  </div>
                  <div class="col q-mb-xs text-right">
                    <q-btn
                      color="primary"
                      :label="t('admin.View')"
                      size="sm"
                      @click="handleFileDownload(item.link)"
                    />
                    <q-btn
                      color="negative"
                      :label="t('admin.Delete')"
                      size="sm"
                      @click="handleFileDelete(item)"
                    />
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <div class="row q-pa-md">
        <div class="col">
          <div class="text-h6">多语言信息</div>
          <q-separator />
          <q-tabs
            v-model="tab"
            dense
            align="left"
            active-color="primary"
            indicator-color="primary"
            :breakpoint="0"
          >
            <q-tab name="default" :label="$t('themeSetting.Default')" />
            <q-tab
              v-for="item in langList"
              :name="item.code"
              :label="item.name"
            />
          </q-tabs>
        </div>
      </div>
      <q-tab-panels v-if="itemDetail?.id" v-model="tab" animated class="col">
        <q-tab-panel
          v-for="item in itemDetail.lang"
          :key="item.id"
          :name="item.code"
        >
          <q-btn color="primary" label="编辑内容" @click="editFaqLang(item)" />
          <q-card class="q-my-md">
            <q-card-section>
              <div class="row q-my-md">
                <div class="col-2 row-title">Faq标题</div>
                <div class="col-auto">
                  <span v-if="item.title">{{ item.title }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">Faq索引</div>
                <div class="col-auto">
                  <span v-if="item.slogan">{{ item.slogan }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
              <div class="row q-my-md">
                <div class="col-2 row-title">Faq SEO关键词</div>
                <div class="col-auto">
                  <span v-if="item.seo_kw">{{ item.seo_kw }}</span>
                  <span v-else>未设置</span>
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <div class="text-h6">
                {{ t("admin.Faq") + t("admin.Content") }}
              </div>
            </q-card-section>
            <q-card-section>
              <div v-if="item?.content" v-html="item.content" />
              <span v-else>
                <q-icon name="warning" />{{ t("admin.NotFound") }}</span
              >
            </q-card-section>
          </q-card>
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <SelectUpload
      v-if="itemDetail?.id"
      ref="uploadImage"
      :url="url.attachment"
      :data-type="'file'"
      :multiple="true"
      :parent-id="itemDetail.id"
      @finishUpload="handleDetail(route.query.id)"
    />
  </BaseContent>
</template>

<script setup>
import { Dialog, Notify } from "quasar";
import { deleteAction, getAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import SelectUpload from "src/components/SelectUpload/index.vue";
import { useSettingStore } from "src/stores/settings";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

const langList = computed(() => {
  return useSettingStore().GetLanguageList();
});
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const url = {
  item: "/api/info/faq",
  attachmentList: "/api/info/faqAttachment/list",
  attachment: "/api/info/faqAttachment",
};
const tab = ref("default");
const itemDetail = ref();

onMounted(async () => {
  if (route.query.id) {
    await handleDetail(route.query.id);
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

const handleDetail = async (id) => {
  const { code, data } = await getAction(url.item, { id: id });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const uploadImage = ref();
const showUploader = () => {
  uploadImage.value.show();
};

const editFaqItem = (item) => {
  router.push({ name: "FaqEdit", query: { id: itemDetail.value.id } });
};

const editFaqLang = (item) => {
  router.push({
    name: "FaqEditLang",
    query: { id: item.id },
    state: { data: JSON.stringify(item) },
  });
};

const handleFileDelete = async (item) => {
  Dialog.create({
    title: t("admin.Confirm"),
    message: t("admin.Confirm") + t("admin.Delete") + "?",
    persistent: false,
    ok: {
      push: true,
      color: "negative",
      label: t("admin.Confirm"),
    },
    cancel: {
      push: true,
      color: "primary",
      label: t("admin.Cancel"),
    },
  }).onOk(async () => {
    const { code, message } = await deleteAction(url.attachment, {
      id: item.id,
    });
    if (code === 200) {
      Notify.create({
        type: "positive",
        message: message,
        position: "top-right",
      });
      await handleDetail(route.query.id);
    }
  });
};

const handleFileDownload = async (item) => {
  if (item.substring(0, 4) === "http") {
    // 非登录上传，头像为链接
    return window.open(item, "_blank");
  } else if (item.substring(0, 11) === "sys-upload:") {
    // 非登录用户，头像为上传
    return window.open(process.env.API + item.substring(11), "_blank");
  } else {
    return window.open(item, "_blank");
  }
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.card-size {
  height: 100%;
}

.pic-size {
  width: 100%;
  height: 100%;
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}

:deep(img) {
  max-width: 100%;
}

.filename {
  overflow: hidden;
}
</style>
