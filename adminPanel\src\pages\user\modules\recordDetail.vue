<template>
  <q-dialog v-model="recordDetailVisible" full-height position="right">
    <q-card style="width: 800px; max-width: 80vw; height: 100%">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ $t('admin.User') }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input v-model="recordDetail.value.username" class="col q-mx-sm" :label="$t('admin.Username')" lazy-rules
              :rules="[val => val && val.length > 0 || $t('router.NeedInput')]"
              :disable="recordDetail.value.username === 'admin'" />
            <q-input v-model="recordDetail.value.realname" class="col q-mx-sm" :label="$t('admin.RealName')" lazy-rules
              :rules="[val => val && val.length > 0 || $t('router.NeedInput')]" />
            <q-input v-model="recordDetail.value.password" class="col q-mx-sm" :label="$t('admin.Password')" lazy-rules
              hint="如果密码不输入，则默认为123456" />
          </div>

          <div class="row">
            <q-field dense class="col q-mx-sm" :label="$t('admin.Admin')" stack-label>
              <template #control>
                <q-option-group v-model="recordDetail.value.is_admin" :options="dictAdmin" color="primary" inline
                  :disable="recordDetail.value.username === 'admin'" />
              </template>
            </q-field>
            <q-field dense class="col q-mx-sm" :label="$t('admin.Status')" stack-label>
              <template #control>
                <q-option-group v-model="recordDetail.value.is_active" :options="dict" color="primary" inline
                  :disable="recordDetail.value.username === 'admin'" />
              </template>
            </q-field>
          </div>
          <div v-if="recordDetail.value.group" class="row">
            <div class="col-4">所在家庭组</div>
            <div class="col-8">{{ recordDetail.value.group.name }}</div>
          </div>
          <div class="row">
            <q-input v-model="recordDetail.value.memo" type="textarea" :label="$t('admin.Memo')" class="col q-mx-sm" />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn :label="$t('admin.Save')" color="primary" @click="handleAddOrEdit" />
        <q-btn v-close-popup :label="$t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
defineOptions({ name: 'UserDialog' })
import { useQuasar } from 'quasar'
import useRecordDetail from 'src/composables/useRecordDetail'
import { useI18n } from 'vue-i18n'

const $q = useQuasar()
const { t } = useI18n()
const emit = defineEmits(['handleFinish'])
const url = {
  list: '/api/user/list',
  create: '/api/user',
  edit: '/api/user',
  item: '/api/user',
}
const dict = [{ 'label': '启用', 'value': true }, { 'label': '停用', 'value': false }]
const dictAdmin = [{ 'label': '是', 'value': true }, { 'label': '否', 'value': false }]
const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  show,
  recordDetailForm,
  handleAddOrEdit
} = useRecordDetail(url, emit)

defineExpose({
  show,
  formType
})
</script>
