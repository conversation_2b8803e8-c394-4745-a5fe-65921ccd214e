<template>
  <q-select v-model="locale" :options="localeOptions" dense borderless emit-value map-options options-dense
    @update:model-value="handleSelectLanguage">
    <template v-slot:prepend>
      <q-icon name="language" />
    </template>
  </q-select>
</template>

<script lang="ts" setup>
defineOptions({ name: "LangSelector" })
import { onMounted } from "vue"
import { useI18n } from 'vue-i18n';
import { useSettingStore } from 'src/stores/settings'

const settingStore = useSettingStore()
const { locale } = useI18n({ useScope: 'global' })
const localeOptions = [
  { value: 'zh-CN', label: '简体中文' },
  { value: 'en-US', label: 'English' },
  { value: 'zh-TW', label: '繁體中文' }
]

onMounted(async() => {
  await settingStore.SetLanguageList()
  const language: string | null = settingStore.GetLanguage()
  if (language !== null) {
    locale.value = language
  }
})

const handleSelectLanguage = () => {
  settingStore.ChangeLanguage(locale.value)
}
</script>
