<template>
  <q-card
    flat
    class="row"
    style="border-radius: 20px; width: 85%; max-width: 400px"
  >
    <div class="absolute-top-left q-pa-md">
      <dark-mode />
    </div>
    <div class="col flex justify-center items-center q-pa-md">
      <q-card-section>
        <ToolbarTitle title="CMS管理后台" size="20px" />
      </q-card-section>
      <q-card-section align="center" class="fit q-gutter-y-sm">
        <q-form ref="loginForm" class="custom-form-error-message">
          <q-input
            v-model.trim="username"
            placeholder="请输入账号"
            dense
            clearable
            outlined
            no-error-icon
            lazy-rules
            :rules="[(val) => !!val || '请输入账号']"
          />
          <q-input
            v-model="password"
            placeholder="请输入密码"
            dense
            outlined
            no-error-icon
            :type="isPwd ? 'password' : 'text'"
            lazy-rules
            :rules="[(val) => !!val || '请输入密码']"
          >
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
              />
            </template>
          </q-input>
          <q-input
            v-model.trim="captcha"
            :disable="loading"
            outlined
            dense
            no-error-icon
            :placeholder="$t('Captcha')"
            :rules="[(val) => (val && val.length > 0) || $t('NeedInput')]"
          >
            <template #after>
              <q-btn
                padding="none"
                style="width: 120px; height: 100%"
                @click="getCaptcha"
              >
                <q-img :src="captchaImage" />
              </q-btn>
            </template>
          </q-input>
          <div class="column q-gutter-y-md q-mt-none">
            <q-checkbox
              v-model="rememberMe"
              :disable="loading"
              :label="$t('RememberMe')"
              dense
              @update:model-value="changeRememberMe"
            />
          </div>
          <q-btn
            class="full-width"
            size="1.2em"
            rounded
            unelevated
            color="primary"
            :loading="loading"
            @click="onLoginClick"
          >
            登入
          </q-btn>
          <q-banner
            v-if="message !== ''"
            inline-actions
            dense
            rounded
            class="text-red"
          >
            {{ message }}
          </q-banner>
        </q-form>
        <q-separator spaced="lg" />
        <q-card-section class="text-caption text-grey-7">
          <span>some descriptions</span>
        </q-card-section>
      </q-card-section>
    </div>
  </q-card>
</template>

<script lang="ts" setup>
import { useVModels } from "@vueuse/core";
import { QForm } from "quasar";
import { getAction } from "src/api/manage";
import DarkMode from "src/components/Toolbar/DarkMode.vue";
import { useUserStore } from "src/stores/user";
import { ref } from "vue";
import ToolbarTitle from "../Toolbar/ToolbarTitle.vue";

defineOptions({ name: "Login" });

interface Props {
  username: string | undefined;
  password: string | undefined;
  captcha: "";
  captcha_id: "";
  loading: boolean;
  message: string;
}

const props = withDefaults(defineProps<Props>(), {
  username: "",
  password: "",
  captcha: "",
  captcha_id: "",
  loading: false,
  message: "",
});

const emit = defineEmits<{
  (e: "update:username"): void;
  (e: "update:password"): void;
  (e: "update:captcha"): void;
  (e: "update:captcha_id"): void;
  (e: "update:loading"): void;
  (e: "onLoginClick"): void;
}>();

const userStore = useUserStore();
const isPwd = ref<boolean>(true);
const loginForm = ref<QForm | null>(null);
const { username, password, loading, message } = useVModels(props, emit);
const rememberMe = ref(true);
const captchaImage = ref("");
const getCaptcha = () => {
  getAction("/auth/captcha").then((res) => {
    captchaImage.value = res.data?.captcha_image;
    captcha_id = res.data?.captcha_id;
  });
};
const changeRememberMe = (value) => {
  userStore.ChangeRememberMe(value);
};
const onLoginClick = async () => {
  const success = await loginForm.value?.validate();
  if (success) {
    emit("onLoginClick");
  }
};
</script>
