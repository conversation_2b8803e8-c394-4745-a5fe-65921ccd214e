<template>
  <BaseContent scrollable>
    <div class="row q-pa-md">
      <q-card class="col">
        <q-card-section>
          <div class="text-h6">
            {{ t("admin.Edit") + t("admin.Information") }} （ 编码:
            {{ langCode }}）
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form ref="recordDetailForm">
            <div class="row q-my-md">
              <q-input
                v-model="recordDetail.title"
                class="col q-mx-sm"
                :label="t('admin.Title')"
              />
              <q-input
                v-model="recordDetail.seo_kw"
                class="col q-mx-sm"
                :label="t('admin.Seo') + t('admin.Keyword')"
              />
            </div>
            <div class="row q-my-md">
              <q-input
                v-model="recordDetail.slogan"
                class="col q-mx-sm"
                :label="t('admin.Slogan')"
              />
            </div>
            <div class="row">
              <Tinymce
                :value="recordDetail.content"
                class="col"
                @getContent="getContent"
              />
            </div>
          </q-form>
        </q-card-section>

        <q-separator />

        <q-card-actions align="left">
          <q-btn :label="t('admin.Save')" color="primary" @click="handleEdit" />
          <q-btn
            v-close-popup
            :label="t('admin.Cancel')"
            color="negative"
            @click="closeTab"
          />
        </q-card-actions>
      </q-card>
    </div>
  </BaseContent>
</template>

<script setup>
import { Notify } from "quasar";
import { putAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import Tinymce from "src/components/Editor/TinyMce.vue";
import { useSettingStore } from "src/stores/settings";
import { useTagViewStore } from "src/stores/tagView";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
const tagViewStore = useTagViewStore();
const route = useRoute();
const { t } = useI18n();
const url = {
  edit: "/api/info/articleLang",
};

const recordDetailForm = ref();
const recordDetail = ref({});
const codeName = ref("");

if (history.state.data) {
  recordDetail.value = JSON.parse(history.state.data);
}

const langCode = computed(() => {
  if (recordDetail.value.code === "default") {
    return "默认语言";
  } else {
    useSettingStore()
      .GetLanguageList()
      .forEach((item) => {
        if (item.code === recordDetail.value.code) {
          codeName.value = item.name;
        }
      });
  }
  return codeName.value;
});

const handleEdit = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.edit) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }

    const res = await putAction(url.edit, recordDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  recordDetail.value.content = v;
  console.log(recordDetail.value.content);
};

const closeTab = () => {
  tagViewStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
