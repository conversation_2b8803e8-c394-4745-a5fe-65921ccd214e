<template>
  <q-dialog v-model="recordDetailVisible" position="right">
    <q-card style="width: 800px; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Product") + t("admin.Category") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row q-my-md">
            <q-file
              v-model="uploadImage"
              class="col q-mr-md"
              name="img_files"
              filled
              use-chips
              label="上传目录形象图片，建议尺寸1440*270~1440*450左右"
            />
            <div v-if="uploadData.url" class="col">
              <q-img
                :src="transImageUrl(uploadData.url)"
                spinner-color="white"
                style="height: 140px"
              />
            </div>
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.parent_name"
              class="col q-mx-sm"
              :label="t('admin.ParentName')"
              readonly
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.name"
              class="col q-mx-sm"
              :label="t('admin.Name')"
            />
          </div>
          <div class="row">
            <q-input
              v-model.number="recordDetail.value.category_level"
              disable
              class="col q-mx-sm"
              :label="t('admin.Level')"
            />
          </div>
          <div class="row">
            <q-field
              v-if="recordDetail.value.category_level === 1"
              dense
              class="col q-mx-sm"
              :label="t('admin.Status')"
              stack-label
            >
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.is_show"
                  :options="statusDict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
            <q-input
              v-model.number="recordDetail.value.weight"
              class="col q-mx-sm"
              type="number"
              :rules="[(val) => val >= 1 || $t('SortRule')]"
              :label="t('admin.Sort')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.memo"
              type="textarea"
              :label="t('admin.Memo')"
              class="col"
            />
          </div>
          <div class="row q-mt-md">
            <q-tabs
              v-model="tab"
              dense
              class="text-grey"
              active-color="primary"
              indicator-color="primary"
              align="justify"
              narrow-indicator
            >
              <q-tab name="default" :label="$t('themeSetting.Default')" />
              <q-tab
                v-for="item in langList"
                :name="item.code"
                :label="item.name"
              />
            </q-tabs>
          </div>

          <q-tab-panels v-model="tab" animated>
            <q-tab-panel
              v-for="item in recordDetail.value.lang"
              :name="item.code"
            >
              <div class="row">
                <q-input
                  v-model="item.name"
                  class="col q-mx-sm"
                  :label="t('admin.Name')"
                />
                <q-input
                  v-model="item.seo_kw"
                  class="col q-mx-sm"
                  :label="t('admin.Seo') + t('admin.Keyword')"
                />
              </div>
              <div class="row">
                <q-input
                  v-model="item.desc"
                  class="col q-mx-sm"
                  :label="t('admin.Seo') + t('admin.Caption')"
                />
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleCreateAction"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { getAction, postAction, putAction } from "src/api/manage";
import useRecordDetail from "src/composables/useRecordDetail";
import { transImageUrl } from "src/utils/image.js";
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useSettingStore } from "../../../stores/settings";
import { generateRandomString } from "../../../utils/string";

const { t } = useI18n();
const $q = useQuasar();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/business/category",
  create: "/api/business/category",
  edit: "/api/business/category",
  delete: "/api/business/category",
  upload: "/api/business/category/image",
};
const statusDict = [
  { label: "首页展示", value: true },
  { label: "不展示", value: false },
];
const langList = computed(() => {
  return useSettingStore().GetLanguageList();
});
const tab = ref("default");
const lang = ref([]);
const uploadImage = ref();
const uploadData = ref({
  url: "",
  thumb: "",
});

watch(uploadImage, () => {
  if (uploadImage.value) {
    uploadData.value = {
      name: uploadImage.value.name,
      url: URL.createObjectURL(uploadImage.value),
    };
  } else {
    uploadData.value = {
      name: "",
      url: "",
    };
  }
});

const show = async (row) => {
  loading.value = true;
  tab.value = "default";
  lang.value = [];
  recordDetail.value = {};
  loading.value = true;
  uploadImage.value = undefined;
  uploadData.value = {
    url: "",
    thumb: "",
  };
  if (row && row.id) {
    const { code, data } = await getAction(url.item, { id: row.id });
    if (code === 200) {
      recordDetail.value = data;
      recordDetail.value.lang.push({
        code: "default",
        name: recordDetail.value.name,
        seo_kw: recordDetail.value.seo_kw,
        desc: recordDetail.value.desc,
      });
    }
    const imgRes = await getAction(url.upload, { id: row.id });
    if (imgRes.code === 200) {
      uploadData.value.url = imgRes.data.link;
    }
  } else {
    recordDetail.value.is_fixed = true;
    recordDetail.value.weight = 1;
    recordDetail.value.category_type = generateRandomString(6);
    lang.value.push({ code: "default", name: "", seo_kw: "", desc: "" });
    langList.value.map((item) => {
      lang.value.push({ code: item.code, name: "", seo_kw: "", desc: "" });
    });
    recordDetail.value.lang = lang;
  }
  recordDetailVisible.value = true;
  loading.value = false;
};
const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
} = useRecordDetail(url, emit);

defineExpose({
  show,
  formType,
  recordDetail,
});

const handleCreateAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (formType.value === "edit") {
      if (url === undefined || !url.edit) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, msg } = await putAction(url.edit, recordDetail.value);
      if (code === 200) {
        if (uploadImage.value) {
          const uploadData = new FormData();
          uploadData.append("file", uploadImage.value);
          uploadData.append("id", recordDetail.value.id);
          postAction(url.upload, uploadData);
        }
        $q.notify({
          type: "positive",
          message: msg,
        });
      }
      emit("handleFinish");
      recordDetailVisible.value = false;
    } else if (formType.value === "add") {
      if (url === undefined || !url.create) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, msg, data } = await postAction(
        url.create,
        recordDetail.value,
      );
      if (code === 200) {
        if (uploadImage.value) {
          const uploadData = new FormData();
          uploadData.append("file", uploadImage.value);
          uploadData.append("id", data.id);
          postAction(url.upload, uploadData);
        }
        $q.notify({
          type: "positive",
          message: msg,
        });
      }
      recordDetailVisible.value = false;
    } else {
      $q.notify({
        type: "negative",
        message: t("CanNotAddOrEdit"),
      });
    }
    emit("handleFinish");
  } else {
    $q.notify({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};
</script>
