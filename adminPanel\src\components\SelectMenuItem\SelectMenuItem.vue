<template>
  <q-dialog v-model="selectItemVisible">
    <q-card style="min-width: 700px; max-width: 45vw">
      <q-card-section class="row justify-between items-center">
        <div class="text-subtitle1">
          <span v-if="title !== ''">
            {{ title }}
            {{ $t("SelectSingle") }}
          </span>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="items-center row q-gutter-md">
        <q-table
          v-model:pagination="pagination"
          row-key="id"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          class="col"
          @request="onRequest"
        >
          <template #body-cell-name="props">
            <q-td :props="props">
              <span v-if="props.row.name">{{ props.row.name }} </span>
              <span v-if="props.row.title">{{ props.row.title }} </span>
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    color="secondary"
                    :label="t('admin.Select')"
                    size="xs"
                    @click="handleSelect(props.row)"
                  />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useTableData from "src/composables/useTableData";
import { computed, ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";
import { postAction } from "../../api/manage";
const { t } = useI18n();
const props = defineProps({
  // 必须传递单选多选: multiple, single
  modelType: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: false,
    default: "选择指定信息",
  },
});
const { modelType, title } = toRefs(props);
const url = {
  category: "/api/business/category/list",
  infoCategory: "/api/info/infoCategory/list",
  product: "/api/business/product/list",
  certificate: "/api/website/certificate/list",
  article: "/api/info/article/list",
  showcase: "/api/info/showcase/list",
  "faq/category": "/api/info/faq/category/list",
  faq: "/api/info/faq/list",
  jobs: "/api/info/jobs/list",
};
const columns = computed(() => {
  return [
    {
      name: "id",
      align: "center",
      label: t("admin.Id"),
      field: (row) => row.id,
    },
    { name: "name", align: "center", label: t("admin.Name"), field: "name" },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const { queryParams, pagination, pageOptions, loading, tableData } =
  useTableData(url);
const selectItemVisible = ref(false);
const selected = ref(null);

const show = () => {
  selected.value = [];
  selectItemVisible.value = true;
  pagination.value.order = "id";
  pagination.value.rowsPerPage = 50;
  getTableData();
};

const onRequest = async (props) => {
  tableData.value = [];
  loading.value = true;
  // 组装分页和过滤条件
  const params = {};
  params.sort_by = props.pagination.sortBy;
  params.desc = props.pagination.descending;
  params.page = props.pagination.page;
  params.page_size = props.pagination.rowsPerPage;
  const allParams = Object.assign({}, params, queryParams.value);
  // 带参数请求数据
  await postAction(url[modelType.value], allParams)
    .then((res) => {
      if (res.code === 200) {
        // 最终要把分页给同步掉
        pagination.value = props.pagination;
        // 并且加入总数字段
        pagination.value.rowsNumber = res.data.total;
        tableData.value = res.data.data;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const getTableData = () =>
  onRequest({ pagination: pagination.value, queryParams: queryParams.value });

defineExpose({
  show,
});
const emit = defineEmits(["handleSelect"]);
const handleSelect = (item) => {
  if (item.id) {
    emit("handleSelect", item);
  }
  selectItemVisible.value = false;
};
</script>
