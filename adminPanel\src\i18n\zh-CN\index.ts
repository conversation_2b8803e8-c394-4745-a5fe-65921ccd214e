export default {
  router: {
    Home: "首页",
    Captcha: "验证码",
    Chart: "图表",
    Table: "表格",
    calendar: "日曆",
    permission: "權限",
    directive: "權限指令",
    routerPermission: "路由權限",
    markdown: "Markdown",
    markdownViewer: "預覽",
    markdownEditor: "編輯器",
    lottie: "Lot<PERSON>",
    menu1: "多層目錄",
    menu2: "第二層",
    menu3: "第三層",
    externalLink: "外部連結",
    typeScript: "TypeScript",
    vite: "Vite",
    vue3: "Vue3",
    quasar: "Quasar",
    RememberMe: "记住我",
    ContractList: "合同列表",
    ContractDetail: "合同详情",
    ContractCreate: "合同创建",
    ContractEdit: "合同编辑",
    StockList: "库存列表",
    StockDetail: "库存详情",
    StockCreate: "库存创建",
    StockEdit: "库存编辑",
    WarehouseList: "仓库列表",
    WarehouseDetail: "仓库详情",
    WarehouseCreate: "仓库创建",
    WarehouseEdit: "仓库编辑",
    SupplierList: "供应商列表",
    SupplierDetail: "供应商详情",
    SupplierCreate: "供应商创建",
    SupplierEdit: "供应商编辑",
    CategoryList: "目录管理",
    CategoryDetail: "目录详情",
    ProductSkuList: "产品SKU管理",
    ProductSkuDetail: "产品SKU详情",
    ProductSkuCreate: "产品SKU创建",
    CompanyList: "企业管理",
    CompanyDetail: "企业管理详情",
    CompanyCreate: "企业创建",
    CompanyEdit: "企业编辑",
    PaymentList: "支付信息汇总",
    TagList: "标签汇总",
    UsageList: "用途汇总",
    ProductList: "产品管理",
    ProductDetail: "产品详情",
    OrderList: "订单列表",
    orderDetail: "订单详情",
    MenuList: "菜单列表",
    PermissionList: "权限列表",
    RoleList: "角色列表",
    UserList: "用户列表",
    UserDetail: "用户详情",
    NeedInput: "必须要输入",
    ImportRecord: "订单导入记录",
  },

  admin: {
    Menu: "目录",
    Children: "子",
    Role: "角色",
    Status: "状态",
    Stable: "内置",
    Actions: "操作",
    Edit: "编辑",
    Delete: "删除",
    Path: "路径",
    Redirect: "重定向",
    Component: "组件",
    IsLink: "外部链接",
    Hidden: "隐藏",
    Order: "排序",
    Home: "首页",
    Title: "标题",
    Icon: "图标",
    MenuList: "菜单列表",
    SortRule: "排序规则",
    Sort: "排序",
    NeedInput: "必填",
    Code: "编码",
    Parent: "父级",
    Memo: "备注",
    Save: "保存",
    Cancel: "取消",
    KeepAlive: "缓存",
    Active: "启用",
    Name: "名称",
    User: "用户",
    Username: "账号",
    RealName: "姓名",
    Search: "搜索",
    Reset: "重置",
    Add: "新增",
    Id: "Id",
    Root: "根",
    Sku: "Sku",
    Level: "级别",
    Password: "密码",
    Avatar: "头像",
    Remove: "移除",
    Admin: "管理员",
    Remark: "备注",
    Error: "错误",
    View: "查看",
    Detail: "详情",
    Permission: "权限",
    Yes: "是",
    No: "否",
    Description: "简述",
    Confirm: "确认",
    Api: "API",
    Clear: "清除",
    Select: "选择",
    All: "全部",
    Mobile: "手机",
    Company: "企业",
    Method: "方法",
    Query: "查询",
    Body: "请求体",
    Backup: "备注",
    PermissionList: "权限列表",
    RoleList: "角色列表",
    UserList: "用户列表",
    UserDetail: "用户详情",
    RoleDetail: "角色详情",
    RoleName: "角色名称",
    ApiCompany: "API分组",
    Serial: "序列号",
    Owner: "创建人",
    Content: "内容",
    Product: "产品",
    Category: "目录",
    ProductSkuList: "产品SKU列表",
    ProductSkuCreate: "产品SKU创建",
    Price: "价格",
    CreatedAt: "创建日期",
    UpdatedAt: "更新日期",
    Total: "总计",
    TotalAmount: "总计金额",
    TotalQuantity: "总计数量",
    CategoryList: "目录管理",
    ProductList: "产品管理",
    Information: "信息",
    Seo: "SEO",
    Keyword: "关键词",
    Slogan: "标语",
    LoadFailed: "加载失败",
    CreateFailed: "创建失败",
    FixTop: "置顶",
    OrderList: "订单列表",
    OrderDetail: "订单详情",
    OrderCreate: "订单创建",
    OrderEdit: "订单编辑",
    OrderStatus: "订单状态",
    OrderStatusList: "订单状态列表",
    OrderStatusDetail: "订单状态详情",
    Contract: "合同",
    ContractList: "合同列表",
    ContractDetail: "合同详情",
    ContractCreate: "合同创建",
    ContractEdit: "合同编辑",
    Stock: "库存",
    StockList: "库存列表",
    StockDetail: "库存详情",
    StockCreate: "库存创建",
    StockEdit: "库存编辑",
    Warehouse: "仓库",
    WarehouseList: "仓库列表",
    WarehouseDetail: "仓库详情",
    WarehouseCreate: "仓库创建",
    WarehouseEdit: "仓库编辑",
    Supplier: "供应商",
    SupplierList: "供应商列表",
    SupplierDetail: "供应商详情",
    SupplierCreate: "供应商创建",
    SupplierEdit: "供应商编辑",
    CompanyList: "企业列表",
  },

  layout: {
    github: "Github",
    fullScreen: "全螢幕",
    darkMode: "深色模式",
    lightMode: "淺色模式",
    refresh: "重新整理",
    notification: "通知",
    user: "使用者",
    Edit: "编辑",
    Profile: "信息",
    Logout: "登出",
    Close: "关闭",
    Right: "右边",
    Left: "左边",
    Other: "其他",
    All: "全部",
  },

  themeSetting: {
    title: "主題設定",
    themeColor: "主題顏色",
    setting: "設定",
    Default: "默认",
    Language: "语言",
  },

  content: {
    CanNotAddOrEdit: "无法新增或者编辑",
    FixForm: "修正表单信息",
    Product: "产品",
    Currency: "货币单位",
    Sales: "销售",
    Market: "市场",
    Price: "价格",
    Brand: "品牌",
    Model: "型号",
    ModelType: "数据类型",
    Size: "尺寸",
    Specification: "规格",
    Barcode: "条码",
    Unit: "单位",
    Description: "简述",
    SourceArea: "原产地",
    Weight: "重量",
    Package: "包装",
    Delivery: "运输",
    Active: "启用",
    Enable: "启用",
    Disable: "禁用",
    Fixed: "置顶",
    ShowIndex: "首页展示",
    NotShowIndex: "不展示",
    Seo: "搜索引擎优化",
    Keyword: "关键词",
    Content: "详细信息",
    Attributes: "属性",
    AttributeName: "属性名",
    Value: "值",
    Category: "目录",
    Serial: "编码",
    CreatedAt: "创建日期",
    UpdatedAt: "创建日期",
    Image: "图片",
    List: "列表",
    NotFound: "无任何信息",
    Select: "选择",
    File: "文件",
    Upload: "上传",
    Progress: "进度",
    PutOn: "启用",
    PutOff: "禁用",
    ParentName: "上级名称",
    Set: "设置",
    Cover: "封面",
    Confirm: "确认",
    Translation: "翻译",
    Change: "变更",
    Faq: "信息",
    Website: "网站",
    IptCode: "Ipt编码",
    Multi: "多",
    Language: "语言",
    Location: "位置",
    Longitude: "经度",
    Latitude: "纬度",
    Telephone: "联系电话",
    Fax: "传真号码",
    Email: "电子邮箱",
    WebsiteInfo: "网站信息",
    Address: "联系地址",
    Code: "编码",
    Alpha2: "Alpha2",
    Icon: "图标",
    Default: "默认值",
    Title: "标题",
    Target: "目标",
    Slider: "轮播图",
    Caption: "描述",
    HyperLink: "超链接",
    BuyLink: "购买链接",
    Preview: "预览",
    Type: "类型",
    FixTop: "置顶",
    Slogan: "标语",
    ProductSku: "产品SKU",
    ShowCase: "展览信息",
    Feature: "企业优势",
    Certificate: "认证资质",
    Partner: "合作伙伴",
    Contact: "联系人",
    ContactList: "联系人",
    JobTitle: "职称",
    DefaultContact: "全局默认联系人",
    Show: "展示",
    Test: "测试",
    Information: "信息",
    Attachment: "附件",
    Jobs: "职位",
    Department: "部门",
    District: "地区",
    Education: "学历",
    Action: "执行",
    RouterPath: "链接路径",
    MenuType: "菜单类型",
    SelectSingle: "选择项目",
  },

  contact: {
    Email: "电子邮箱",
    Phone: "电话号码",
    Fax: "传真号码",
    QQ: "QQ",
    Wechat: "微信",
    Weibo: "微博",
    Twitter: "推特",
    YouTube: "油管",
    Facebook: "脸书",
    Skype: "Skype",
    WhatsApp: "WhatsApp",
  },
};
