<template>
  <div class="q-gutter-sm row items-center no-wrap">
    <q-separator vertical />
    <q-btn
      v-if="$q.screen.gt.sm"
      dense
      flat
      :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
      @click="$q.fullscreen.toggle()"
    >
      <q-tooltip>{{ $t("layout.fullScreen") }}</q-tooltip>
    </q-btn>

    <dark-mode />

    <q-btn
      v-if="$q.screen.gt.sm"
      dense
      flat
      icon="refresh"
      @click="appStore.reloadPage(200)"
    >
      <q-tooltip>{{ $t("layout.refresh") }}</q-tooltip>
    </q-btn>

    <q-btn round flat>
      <q-avatar color="primary" text-color="white">
        {{ userStore.getFirstCharacterOfUserName }}
      </q-avatar>
      <q-menu class="profile-menu">
        <q-list dense>
          <q-item>
            <q-item-section>
              <div>
                <q-icon name="person" color="blue-9" size="18px" />
                <strong>{{ userStore.getRealName }}</strong>
              </div>
            </q-item-section>
          </q-item>
          <q-separator />
          <q-item clickable>
            <q-item-section>
              <div>
                <q-icon name="edit" color="blue-9" size="18px" />
                {{ $t("layout.Edit") + $t("layout.Profile") }}
              </div>
            </q-item-section>
          </q-item>
          <q-separator />

          <q-item clickable>
            <q-item-section @click="logout">{{
              $t("layout.Logout")
            }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>

      <q-tooltip>使用者</q-tooltip>
    </q-btn>
  </div>
</template>

<script lang="ts" setup>
import DarkMode from "src/components/Toolbar/DarkMode.vue";
import { useAppStore } from "src/stores/app";
import { useUserStore } from "src/stores/user";
import { ref } from "vue";
import { useRouter } from "vue-router";

defineOptions({ name: "ToolbarItem" });

const userStore = useUserStore();
const router = useRouter();
const appStore = useAppStore();
const userInfo = ref({});
userInfo.value = userStore.GetUserInfo();
const logout = () => {
  userStore.HandleLogout();
  router.push({ path: "/login" });
};
</script>
