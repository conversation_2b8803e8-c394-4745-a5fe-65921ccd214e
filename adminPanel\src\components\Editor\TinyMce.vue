<template>
  <div class="tinymce-box">
    <editor v-model="myValue" :disabled="disabled" :init="init" />
  </div>
</template>

<script setup>
// 文档 http://tinymce.ax-z.cn/
// 引入组件
import Editor from "@tinymce/tinymce-vue";
import tinymce from "tinymce/tinymce"; // tinymce默认hidden，不引入不显示

// 引入富文本编辑器主题的js和css
import "tinymce/icons/default"; // 解决了icons.lang 报错Unexpected token '<'
import "tinymce/models/dom";
import "tinymce/skins/content/default/content.css";
import "tinymce/themes/silver/theme.min.js";

// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import "tinymce/plugins/code";
import "tinymce/plugins/fullscreen";
import "tinymce/plugins/image"; // 插入上传图片插件
import "tinymce/plugins/importcss";
import "tinymce/plugins/link";
import "tinymce/plugins/lists"; // 列表插件
import "tinymce/plugins/media"; // 插入视频插件
import "tinymce/plugins/preview";
import "tinymce/plugins/table"; // 插入表格插件
import "tinymce/plugins/wordcount"; // 字数统计插件
const fonts = [
  "宋体=宋体",
  "微软雅黑=微软雅黑",
  "新宋体=新宋体",
  "黑体=黑体",
  "楷体=楷体",
  "隶书=隶书",
  "Courier New=courier new,courier",
  "AkrutiKndPadmini=Akpdmi-n",
  "Andale Mono=andale mono,times",
  "Arial=arial,helvetica,sans-serif",
  "Arial Black=arial black,avant garde",
  "Book Antiqua=book antiqua,palatino",
  "Comic Sans MS=comic sans ms,sans-serif",
  "Courier New=courier new,courier",
  "Georgia=georgia,palatino",
  "Helvetica=helvetica",
  "Impact=impact,chicago",
  "Symbol=symbol",
  "Tahoma=tahoma,arial,helvetica,sans-serif",
  "Terminal=terminal,monaco",
  "Times New Roman=times new roman,times",
  "Trebuchet MS=trebuchet ms,geneva",
  "Verdana=verdana,geneva",
  "Webdings=webdings",
  "Wingdings=wingdings,zapf dingbats",
];

import { postAction, postFormDataAction } from "src/api/manage";
import { onMounted, reactive, ref, watch } from "vue";

const emits = defineEmits(["getContent"]);
const props = defineProps({
  value: {
    type: String,
    default: "",
  },
  // 基本路径，默认为空根目录，如果你的项目发布后的地址为目录形式，
  // 即abc.com/tinymce，baseUrl需要配置成tinymce，不然发布后资源会找不到
  baseUrl: {
    type: String,
    default: window.location.origin ? window.location.origin : "",
  },
  uploadUrl: {
    type: String,
    required: false,
    default: "/api/business/productImage/content",
  },
  // 禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  plugins: {
    type: [String, Array],
    default: "link image lists media fullscreen preview code table",
  },
  toolbar: {
    type: [String, Array],
    default:
      "bold italic underline strikethrough ｜ link unlink lists table image media | styles " +
      "fontfamily fontsize formatselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | " +
      "bullist numlist outdent indent blockquote | undo redo | subscript superscript removeformat | fullscreen preview code",
  },
});

const myValue = ref(props.value);

const init = reactive({
  language_url: `/tinymce/lang/zh-Hans.js`,
  language: "zh-Hans",
  skin_url: `/tinymce/skins/ui/oxide`,
  // skin_url: 'tinymce/skins/ui/oxide-dark', // 暗色系
  // convert_urls: false,
  image_advtab: true,
  importcss_append: true,
  // content_css（为编辑区指定css文件）,加上就不显示字数统计了
  // content_css: `${this.baseUrl}tinymce/skins/content/default/content.css`,
  // （指定需加载的插件）
  toolbar_mode: "wrap",
  font_size_formats:
    "12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 64px 72px", // 字体大小
  font_family_formats: fonts.join(";"),
  height: 600, // 高度
  selector: "textarea",
  promotion: false,
  statusbar: true, // 底部的状态栏
  menubar: "file edit insert view format table tools", // （1级菜单）最上方的菜单
  branding: false, // （隐藏右下角技术支持）水印“Powered by TinyMCE”
  // image_dimensions: false, // 去除宽高属性
  plugins: props.plugins, // 这里的数据是在props里面就定义好了的
  toolbar: props.toolbar, // 这里的数据是在props里面就定义好了的
  // paste_convert_word_fake_lists: true, // 插入word文档需要该属性
  paste_webkit_styles: "all",
  paste_merge_formats: true,
  nonbreaking_force_tab: false,
  paste_auto_cleanup_on_paste: false,
  file_picker_types: "file image media",
  content_css: "/tinymce/skins/content/default/content.css", // 以css文件方式自定义可编辑区域的css样式，css文件需自己创建并引入
  // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
  // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
  // images_upload_handler: (blobInfo, progress) =>
  //   new Promise((resolve, reject) => {
  //     resolve('data:' + blobInfo.blob().type + ';base64,' + blobInfo.base64())
  //   }),
  // images_upload_handler: async(blobInfo, succFun, failFun) => {
  //   const file = blobInfo.blob() // 转化为易于理解的file对象
  //   const formData = new FormData()
  //   formData.append('file', file, file.name)// 此处与源文档不一样
  //   const { data: res } = await postAction('v1/business/com/product/content-image/temp', formData)
  //   if (res.records.url.substring(0, 11) === 'sys-upload:') {
  //     succFun(process.env.API + res.records.url.substring(11))
  //   }
  //   else {
  //     succFun(res.records.url)
  //   }
  // }
  images_upload_handler: (blobInfo, progress) =>
    new Promise((resolve, reject) => {
      const file = blobInfo.blob(); // 转化为易于理解的file对象
      const formData = new FormData();
      formData.append("file", file, file.name); // 此处与源文档不一样
      postFormDataAction(props.uploadUrl, formData).then((res) => {
        console.log(res);
        if (res.data.url.substring(0, 11) === "sys-upload:") {
          resolve(process.env.API + res.data.url.substring(11));
        } else {
          resolve(res.data.url);
        }
      });
    }),
});

// 监听外部传递进来的的数据变化
watch(
  () => props.value,
  () => {
    myValue.value = props.value;
    emits("getContent", myValue.value);
  },
);
// 监听富文本中的数据变化
watch(
  () => myValue.value,
  () => {
    emits("getContent", myValue.value);
  },
);
// 在onMounted中初始化编辑器
onMounted(() => {
  tinymce.init({
    selector: "textarea",
    plugins: props.plugins,
    toolbar: props.toolbar,
    skin_url: "/tinymce/skins/ui/oxide",
    content_css: "/tinymce/skins/content/default/content.css",
  });
});
</script>
