<template>
  <q-dialog v-model="recordDetailVisible" position="right" persistent>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Product") }} Sku:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div v-if="formType === 'add'">
            <div class="row q-my-md">
              <q-file
                v-model="imageList"
                name="img_files"
                filled
                multiple
                use-chips
                label="上传Sku图片"
              />
            </div>
            <div v-if="imageData.length" class="row">
              <q-img
                v-for="item in imageData"
                :src="item.url"
                spinner-color="white"
                style="height: 140px; max-width: 150px"
                class="q-ma-md"
              />
            </div>
          </div>
          <q-separator />
          <div class="row">
            <q-input
              v-model.number="recordDetail.value.sales_price"
              class="col q-mx-sm"
              :label="t('admin.Sales') + t('admin.Price')"
            />
            <q-input
              v-model.number="recordDetail.value.market_price"
              class="col q-mx-sm"
              :label="t('admin.Market') + t('admin.Price')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.name"
              class="col q-mx-sm"
              :label="$t('Sku') + t('admin.Name')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.model"
              class="col q-mx-sm"
              :label="t('admin.Model')"
            />
            <q-input
              v-model="recordDetail.value.size"
              class="col q-mx-sm"
              :label="t('admin.Size')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.Specification"
              class="col q-mx-sm"
              :label="t('admin.Specification')"
            />
            <q-input
              v-model="recordDetail.value.package"
              class="col q-mx-sm"
              :label="t('admin.Package')"
            />
          </div>

          <div class="row">
            <q-field
              dense
              class="col q-mx-sm"
              :label="t('admin.Status')"
              stack-label
            >
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.is_active"
                  :options="dict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleProductAction"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { postAction } from "src/api/manage";
import useRecordDetail from "src/composables/useRecordDetail";
import { computed, reactive, ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  parentId: {
    type: Number,
    required: true,
  },
  code: {
    type: String,
    required: false,
    default: "default",
  },
});
const { parentId, code } = toRefs(props);
const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/business/productSku",
  edit: "/api/business/productSku",
  upload: "/api/business/productSkuImage",
};
const dict = [
  { label: "启用", value: true },
  { label: "停用", value: false },
];

const imageList = ref();
const imageData = computed(() => {
  const res = reactive([]);
  if (imageList.value) {
    imageList.value.forEach((item) => {
      const tmp = {
        name: item.name,
        url: URL.createObjectURL(item),
      };
      res.push(tmp);
    });
  }
  return res;
});

const show = async (row) => {
  loading.value = true;
  recordDetail.value = {};
  imageList.value = [];
  recordDetailVisible.value = true;
  if (row && row.id) {
    recordDetail.value = row;
  } else {
    recordDetail.value = {};
    recordDetail.value.parent_id = parentId.value;
    recordDetail.value.code = code.value;
    recordDetail.value.is_active = true;
    recordDetailVisible.value = true;
  }
  loading.value = false;
};

const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
  handleAddOrEdit,
} = useRecordDetail(url, emit);

defineExpose({
  show,
  formType,
});

const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (formType.value === "edit") {
      await handleAddOrEdit();
    } else if (formType.value === "add") {
      if (url === undefined || !url.item) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, data, message } = await postAction(
        url.item,
        recordDetail.value,
      );
      if (code === 200) {
        if (imageList.value && data.id) {
          imageList.value.forEach((item, index) => {
            const uploadData = new FormData();
            uploadData.append("file", item);
            if (index === 0) {
              uploadData.append("cover", "true");
            } else {
              uploadData.append("cover", "false");
            }
            uploadData.append("order", index + 1);
            uploadData.append("parent_id", data.id);
            postAction(url.upload, uploadData);
          });
        }
        $q.notify({
          type: "positive",
          message: message,
        });
      }
      recordDetailVisible.value = false;
    } else {
      $q.notify({
        type: "negative",
        message: t("CanNotAddOrEdit"),
      });
    }
    emit("handleFinish");
  } else {
    $q.notify({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};
</script>
