<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table
          v-model:pagination="pagination"
          row-key="id"
          separator="cell"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          @request="onRequest"
        >
          <template #top="props">
            <q-btn
              color="primary"
              :label="t('admin.Add') + t('admin.Warehouse')"
              @click="showAddForm"
            />
            <q-space />
            <q-btn
              flat
              round
              dense
              :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
              class="q-ml-md"
              @click="props.toggleFullscreen"
            />
          </template>

          <template #body-cell-created_at="props">
            <q-td :props="props">
              {{ showDateTime(props.row.created_at) }}
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    color="primary"
                    size="xs"
                    :label="t('admin.View')"
                    @click="handleDetail(props.row)"
                  />
                  <q-btn
                    v-if="props.row.fix_top"
                    color="warning"
                    :label="t('admin.Cancel')"
                    size="xs"
                    @click="handleDetailStatus(props.row)"
                  />
                  <q-btn
                    v-else
                    color="positive"
                    :label="t('admin.FixTop')"
                    size="xs"
                    @click="handleDetailStatus(props.row)"
                  />
                  <q-btn
                    color="negative"
                    size="xs"
                    :label="t('admin.Delete')"
                    @click="handleDelete(props.row)"
                  />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
      <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
    </div>
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import { putAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { FormatDateTime } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import RecordDetail from "./modules/recordDetail.vue";

const router = useRouter();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/warehouse/list",
  delete: "/api/warehouse",
  status: "/api/warehouse/status",
};
const columns = computed(() => {
  return [
    {
      name: "name",
      required: true,
      align: "left",
      label: t("admin.Name"),
      field: "name",
    },
    {
      name: "serial",
      align: "center",
      label: t("admin.Serial"),
      field: "serial",
    },
    {
      name: "created_at",
      align: "center",
      label: t("admin.CreatedAt"),
      field: "created_at",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  getTableData,
  showAddForm,
  showEditForm,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(() => {
  pagination.value.order = "created_at";
  pagination.value.descending = true;
  getTableData();
});

const handleDetail = (item) => {
  router.push({ name: "WarehouseDetail", query: { id: item.id } });
};
const createWarehouse = () => {
  router.push({ name: "WarehouseCreate" });
};

const itemStatus = ref();
const handleDetailStatus = async (item) => {
  itemStatus.value = !item.is_fixed;
  const { code, message } = await putAction(url.status, {
    id: item.id,
    is_fixed: itemStatus.value,
  });
  if (code === 200) {
    $q.notify({
      type: "positive",
      message: message,
    });
    await getTableData();
  } else {
    $q.notify({
      type: "warning",
      message: message,
    });
  }
};
const showDateTime = computed(() => {
  return (datetime) => {
    return FormatDateTime(datetime);
  };
});
</script>

<style lang="scss" scoped>
.weight-size {
  font-size: 18px;
  font-weight: 500;
  color: #ee9900;
}
</style>
