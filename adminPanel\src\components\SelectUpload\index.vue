<template>
  <q-dialog v-model="recordDetailVisible" position="right">
    <q-card style="width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ t("admin.Select") + t("admin.File") + t("admin.Upload") }}:
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section>
        <q-uploader
          style="width: 100%"
          :multiple="multiple"
          :factory="factoryFn"
          :label="title === '' ? t('admin.Upload') + t('admin.File') : title"
          :max-file-size="maxSize * 1024 * 1024"
          :color="color"
          :hide-upload-btn="hideBtn"
          @removed="removed"
          @added="added"
          @finish="finishUpload"
        >
          <template #list="scope">
            <q-list separator>
              <q-item v-for="file in scope.files" :key="file.name" v-ripple>
                <q-item-section>
                  <q-item-label class="full-width ellipsis">
                    {{ file.name }}
                  </q-item-label>

                  <q-item-label
                    caption
                    :class="file.__status === 'uploaded' ? 'text-positive' : ''"
                  >
                    <q-badge>
                      {{ t("admin.Size") }} </q-badge
                    >:{{ file.__sizeLabel }}
                    <q-badge>
                      {{ t("admin.Progress") }} </q-badge
                    >:{{ file.__progressLabel }}
                    <q-badge>
                      {{ t("admin.Status") }} </q-badge
                    >: {{ file.__status }}
                  </q-item-label>
                </q-item-section>

                <q-item-section v-if="file.__img" thumbnail class="gt-xs">
                  <img :src="file.__img.src" alt="" />
                </q-item-section>

                <q-item-section top side>
                  <q-btn
                    class="gt-xs"
                    size="12px"
                    flat
                    dense
                    round
                    icon="delete"
                    @click="scope.removeFile(file)"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </template>
        </q-uploader>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useUserStore } from "src/stores/user";
import { computed, ref, toRefs } from "vue";
const userStore = useUserStore();
const token = computed(() => userStore.GetToken());
const props = defineProps({
  title: {
    type: String,
    required: false,
    default: "",
  },
  multiple: {
    type: Boolean,
    required: false,
    default: true,
  },
  hideBtn: {
    type: Boolean,
    required: false,
    default: false,
  },
  color: {
    type: String,
    required: false,
    default: "primary",
  },
  url: {
    type: String,
    required: false,
    default: "1",
  },
  dataType: {
    type: String,
    required: false,
    default: "none",
  },
  imageType: {
    type: String,
    required: false,
    default: "none",
  },
  parentId: {
    type: Number,
    required: false,
    default: undefined,
  },
  maxSize: {
    type: Number,
    required: false,
    default: 4,
  },
});
const {
  title,
  multiple,
  color,
  hideBtn,
  url,
  parentId,
  maxSize,
  dataType,
  imageType,
} = toRefs(props);

const recordDetailVisible = ref(false);
const show = () => {
  console.log(parentId, "factory");
  recordDetailVisible.value = true;
};
const factoryFn = (files) => {
  return {
    url: process.env.API + url.value,
    headers: [{ name: "Authorization", value: token.value }],
    fieldName: "file",
    method: "POST",
    formFields: [
      { name: "parent_id", value: parentId.value },
      { name: "title", value: title.value },
      { name: "data_type", value: dataType.value },
      { name: "image_type", value: imageType.value },
    ],
  };
};
const fileList = ref([]);
const added = (files) => {
  console.log(files, "add");
};
const removed = (files) => {
  files.forEach((item) => {
    fileList.value.splice(
      fileList.value.map((i) => i.filename).indexOf(item.name),
      1
    );
  });
};
const emit = defineEmits(["finishUpload"]);
const finishUpload = () => {
  emit("finishUpload");
  recordDetailVisible.value = false;
};

defineExpose({
  show,
  title,
  url,
});
</script>
