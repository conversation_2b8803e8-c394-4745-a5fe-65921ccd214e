<template>
  <BaseContent scrollable>
    <div v-if="itemDetail?.id" class="row q-ma-md">
      <q-card class="col">
        <q-card-section>
          <div class="row">
            <div class="col">
              <div class="row">
                <div class="col-4">菜单名称</div>
                <div class="col-auto">
                  {{ itemDetail.name }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4">菜单编码</div>
                <div class="col-auto">
                  {{ itemDetail.nav_type }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4">菜单类型</div>
                <div class="col-auto">
                  <q-chip
                    v-if="itemDetail.stable"
                    square
                    outline
                    size="sm"
                    color="positive"
                    label="内置"
                  />
                  <q-chip v-else square outline size="sm" label="非内置" />
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <q-btn
      color="primary"
      :label="t('admin.Add') + $t('router.Navigator')"
      class="q-ma-md"
      @click="showAddParentForm()"
    />
    <div class="row q-ma-md">
      <div class="col">
        <q-hierarchy
          v-if="itemDetail?.menu"
          separator="cell"
          dense
          :columns="columns"
          :data="itemDetail.menu"
        >
          <template #body="props">
            <TreeTd :tree-td="props" first-td="sort" class="text-center" />
            <td class="text-center">
              {{ props.item.id }}
            </td>
            <td class="text-center">
              {{ props.item.name }}
            </td>
            <td class="text-center">
              {{ props.item.level + 1 }}
            </td>
            <td class="text-center">
              {{ props.item.route_path }}
            </td>
            <td class="text-center">
              {{ props.item.menu_type }}
            </td>
            <td class="text-center">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    size="md"
                    dense
                    color="primary"
                    :label="t('admin.Edit')"
                    @click="showEditForm(props.item)"
                  />
                  <q-btn
                    size="md"
                    dense
                    color="positive"
                    :label="
                      t('admin.Add') +
                      t('admin.Children') +
                      $t('router.Navigator')
                    "
                    @click="showAddChildrenForm(props.item)"
                  />
                  <q-btn
                    size="md"
                    dense
                    color="negative"
                    :label="t('admin.Delete')"
                    @click="handleDelete(props.item)"
                  />
                </q-btn-group>
              </div>
            </td>
          </template>
        </q-hierarchy>
        <q-card v-else class="q-py-md text-center">
          <h5>暂无目录信息</h5>
        </q-card>
      </div>
    </div>
    <RecordDetail
      v-if="itemDetail?.id"
      ref="recordDetailDialog"
      :type-id="itemDetail.id"
      @handleFinish="handleDetail"
    />
  </BaseContent>
</template>

<script setup>
import { Notify, useQuasar } from "quasar";
import { deleteAction, getAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import TreeTd from "src/components/TreeTd/index.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import RecordDetail from "./modules/recordDetail.vue";

const route = useRoute();
const $q = useQuasar();
const { t } = useI18n();
const url = {
  item: "/api/website/navigationType",
  delete: "/api/website/navigationMenu",
};
const itemDetail = ref();
const columns = computed(() => {
  return [
    { name: "sort", align: "center", label: "", field: "sort" },
    { name: "id", align: "center", label: "ID", field: "id" },
    { name: "name", align: "center", label: t("admin.Name"), field: "name" },
    {
      name: "level",
      align: "center",
      label: t("admin.Level"),
      field: "level",
    },
    {
      name: "weight",
      align: "center",
      label: t("admin.Sort"),
      field: "weight",
    },
    {
      name: "route_path",
      align: "center",
      label: t("admin.RouterPath"),
      field: "route_path",
    },
    {
      name: "menu_type",
      align: "center",
      label: t("admin.MenuType"),
      field: "menu_type",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const { recordDetailDialog } = useTableData(url);

onMounted(async () => {
  if (route.query.id) {
    await handleDetail();
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});
const handleDelete = (row) => {
  if (!url || !url.delete) {
    $q.notify({
      type: "negative",
      message: "请先配置url",
    });
    return;
  }
  $q.dialog({
    title: t("admin.Confirm"),
    message: t("admin.Confirm") + t("admin.Delete") + "?",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    const res = await deleteAction(url.delete, {
      id: row.id,
    });
    if (res.code === 200) {
      $q.notify({
        type: "positive",
        message: res.message,
      });
    }
    await handleDetail();
  });
};

const handleDetail = async () => {
  const { code, data } = await getAction(url.item, {
    id: Number(route.query.id),
  });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const showEditForm = (item) => {
  recordDetailDialog.value.formType = "edit";
  recordDetailDialog.value.show(item);
};

const showAddParentForm = () => {
  recordDetailDialog.value.formType = "add";
  recordDetailDialog.value.show({ parent_id: 0, level: 1 });
};
const showAddChildrenForm = (item) => {
  recordDetailDialog.value.formType = "add";
  recordDetailDialog.value.show({ parent_id: item.id, level: item.level + 1 });
};
</script>
