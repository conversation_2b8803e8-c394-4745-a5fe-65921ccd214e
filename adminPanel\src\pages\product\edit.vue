<template>
  <base-content scrollable>
    <div class="row q-pa-md">
      <q-card v-if="itemDetail?.id" class="col">
        <q-card-section>
          <div class="text-h6">
            {{ t("admin.Edit") + t("admin.Product") }}
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form ref="recordDetailForm">
            <div class="row">
              <q-toggle
                v-model="itemDetail.is_active"
                class="col q-mx-sm"
                :label="t('admin.Active')"
              />
              <q-toggle
                v-model="itemDetail.fix_top"
                class="col q-mx-sm"
                :label="t('admin.Fixed')"
              />
              <q-input
                v-model.number="itemDetail.sort"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.ShowIndex') + t('admin.Sort')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.name"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Name')"
              />
              <SelectCategory
                v-model:selectItem="selectItem"
                v-model:selectItemId="itemDetail.category_id"
                v-model:selectItemName="itemDetail.category_name"
                selection="single"
                class="col q-mx-sm"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.brand"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Brand')"
              />
              <q-input
                v-model="itemDetail.model"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Model')"
              />
              <q-input
                v-model="itemDetail.source_area"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.SourceArea')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.weight"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Weight')"
              />
              <q-input
                v-model="itemDetail.size"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Size')"
              />
              <q-input
                v-model="itemDetail.unit"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Unit')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.barcode"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Barcode')"
              />
              <q-input
                v-model="itemDetail.pack_size"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Package') + t('admin.Size')"
              />
              <q-input
                v-model="itemDetail.delivery_size"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Delivery') + t('admin.Size')"
              />
            </div>
            <div class="row">
              <q-input
                v-model.number="itemDetail.currency"
                maxlength="50"
                counter
                class="col q-mx-sm"
                :label="t('admin.Currency')"
              />
              <q-input
                v-model.number="itemDetail.sales_price"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.Sales') + t('admin.Price')"
              />
              <q-input
                v-model.number="itemDetail.market_price"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.Market') + t('admin.Price')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.buy_link"
                maxlength="250"
                counter
                :label="t('admin.BuyLink')"
                class="col q-mx-sm"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.remark"
                maxlength="250"
                counter
                :label="t('admin.Remark')"
                class="col q-mx-sm"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.seo_kw"
                maxlength="250"
                counter
                :label="t('admin.Seo') + t('admin.Keyword')"
                class="col q-mx-sm"
              />
              <q-input
                v-model="itemDetail.desc"
                maxlength="250"
                counter
                :label="t('admin.Seo') + t('admin.Description')"
                class="col q-mx-sm"
              />
            </div>
            <q-separator class="q-my-md" />
            <div class="row">
              <q-tabs
                v-model="tab"
                dense
                class="text-grey"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
              >
                <q-tab name="content" :label="t('admin.Content')" />
                <q-tab name="specification" :label="t('admin.Specification')" />
              </q-tabs>
            </div>
            <div class="row">
              <q-tab-panels v-model="tab" animated class="col">
                <q-tab-panel name="content">
                  <div class="row">
                    <Tinymce
                      :value="itemDetail.content"
                      class="col"
                      @getContent="getContent"
                    />
                  </div>
                </q-tab-panel>

                <q-tab-panel name="specification">
                  <Tinymce
                    :value="itemDetail.specification"
                    class="col"
                    @getContent="getSpecification"
                  />
                </q-tab-panel>
              </q-tab-panels>
            </div>
          </q-form>
        </q-card-section>

        <q-separator />

        <q-card-actions align="left">
          <q-btn
            :label="t('admin.Save')"
            color="primary"
            @click="handleProductAction"
          />
          <q-btn
            v-close-popup
            :label="t('admin.Cancel')"
            color="negative"
            @click="closeTab"
          />
        </q-card-actions>
      </q-card>
    </div>
  </base-content>
</template>

<script setup>
import { Notify } from "quasar";
import { getAction, putAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import Tinymce from "src/components/Editor/TinyMce.vue";
import SelectCategory from "src/components/SelectCategory/index.vue";
import { useTagViewStore } from "src/stores/tagView";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

const tagViewStore = useTagViewStore();
const route = useRoute();
const { t } = useI18n();
const url = {
  get: "/api/business/product",
  edit: "/api/business/product",
};

const itemDetail = ref();
const tab = ref("content");
const selectLang = ref("default");

onMounted(() => {
  // getTableData()
  if (route.query.id) {
    selectLang.value = route.query.code;
    handleDetail(route.query.id);
  }
});
const recordDetailForm = ref();
const handleDetail = async (id) => {
  const { code, data } = await getAction(url.get, { id: Number(id) });
  if (code === 200) {
    itemDetail.value = data;
    Notify.create({
      type: "positive",
      message: "信息查询成功",
      position: "top-right",
    });
  } else {
    itemDetail.value = {};
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    console.log(itemDetail.value);
    if (url === undefined || !url.edit) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await putAction(url.edit, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  itemDetail.value.content = v;
};

const getSpecification = (v) => {
  itemDetail.value.specification = v;
};

const closeTab = () => {
  tagViewStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
