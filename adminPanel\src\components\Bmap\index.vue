<template>
  <q-dialog v-model="recordDetailVisible" position="right">
    <div style="width: 1024px">
      <!-- 仿百度地图官网 -->
      <BMap
        v-if="initMap"
        ref="map"
        width="100%"
        height="100vh"
        zoom="17"
        ak="0qcW0PsvXVRqNaaw2K4jhDWANKsS7xF6"
        enable-scroll-wheel-zoom
        :center="location?.point || undefined"
        :map-type="mapType"
        @initd="get"
      >
        <BZoom :offset="{ x: 22, y: 40 }" />
        <BCityList :offset="{ x: 20, y: 20 }" />
        <BScale anchor="BMAP_ANCHOR_BOTTOM_RIGHT" />
        <BNavigation3d anchor="BMAP_ANCHOR_BOTTOM_RIGHT" :offset="{ x: 10, y: 140 }" />
        <template v-if="!loading">
          <BMarker :position="location?.point" :title="title" />
          <BCircle
            stroke-style="solid"
            stroke-color="#0099ff"
            :stroke-opacity="0.8"
            fill-color="#0099ff"
            :fill-opacity="0.5"
            :center="location.point"
            :radius="location.accuracy"
          />
        </template>
        <BControl
          anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
          :offset="{ x: 22, y: 103 }"
          class="custom-control location-btn"
          @click="get"
        >
          <span v-if="loading" class="location-text">定位中..</span>
          <LocationIcon v-if="!loading" />
        </BControl>
      </BMap>
    </div>
  </q-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {
  BMap,
  BZoom,
  BScale,
  BCityList,
  BNavigation3d,
  BControl,
  BCircle,
  BMarker,
  useBrowserLocation
} from 'vue3-baidu-map-gl'

const recordDetailVisible = ref(false)
const loading = ref(false)
const initMap = ref(false)
const title = ref()
const location = reactive({})
const { get } = useBrowserLocation(null, () => {
  map.value.resetCenter()
})
const show = (item) => {
  initMap.value = false
  loading.value = true
  recordDetailVisible.value = true
  location.point = {
    lng: item.location_x,
    lat: item.location_y,
  }
  title.value = item.name
  location.accuracy = 1
  console.log(location)
  initMap.value = true
  loading.value = false
}

const map = ref()
const mapType = ref('BMAP_NORMAL_MAP')

defineExpose({
  show,
})
</script>

<style scoped>

</style>
