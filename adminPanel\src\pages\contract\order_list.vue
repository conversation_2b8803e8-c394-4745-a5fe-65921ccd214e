<template>
    <div class="q-pa-sm">
      <q-table
        v-model:pagination="pagination"
        row-key="id"
        separator="cell"
        :rows="tableData"
        :columns="columns"
        :rows-per-page-options="pageOptions"
        :loading="loading"
        @request="onRequest"
      >
        <template #top="props">
           <!-- 订单搜索过滤器 -->
            <div class="row q-gutter-md items-end">
        <div class="col-auto" style="min-width: 200px;">
          <q-input
            v-model="searchParams.platform_order_serial"
            outlined
            dense
            clearable
            label="平台订单编号"
            placeholder="请输入平台订单编号"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="col-auto" style="min-width: 200px;">
          <q-input
            v-model="searchParams.serial"
            outlined
            dense
            clearable
            label="订单编号"
            placeholder="请输入订单编号"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="col-auto" style="min-width: 200px;">
          <q-input
            v-model="searchParams.platform_name"
            outlined
            dense
            clearable
            label="销售平台"
            placeholder="请输入销售平台"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="col-auto" style="min-width: 150px;">
          <q-input
            v-model="searchParams.status"
            outlined
            dense
            clearable
            label="订单状态"
            placeholder="请选择订单状态"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="col-auto">
          <div class="row q-gutter-sm">
            <q-btn
              color="primary"
              label="搜索"
              icon="search"
              @click="handleSearch"
            />
            <q-btn
              color="grey"
              label="重置"
              icon="refresh"
              @click="resetSearch"
            />
          </div>
        </div>
      </div>
          <q-space />
          <q-btn
            flat
            round
            dense
            :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
            class="q-ml-md"
            @click="props.toggleFullscreen"
          />
        </template>

        <template #body-cell-status="props">
          <q-td :props="props">
            <q-chip
              v-if="props.row.status === 'success' || props.row.status === '已完成' || props.row.status === 1"
              square
              outline
              size="sm"
              color="positive"
              label="已完成"
            />
            <q-chip
              v-else-if="props.row.status === 'failed' || props.row.status === '已退款' || props.row.status === 0"
              square
              outline
              size="sm"
              color="negative"
              label="已退款"
            />
            <q-chip
              v-else-if="props.row.status === 'pending' || props.row.status === '备货' || props.row.status === 2"
              square
              outline
              size="sm"
              color="warning"
              label="备货中"
            />
            <q-chip
              v-else-if="props.row.status === '配送中' || props.row.status === 3"
              square
              outline
              size="sm"
              color="accent"
              label="配送中"
            />
            <q-chip
              v-else
              square
              outline
              size="sm"
              color="grey"
              :label="props.row.status || '未知'"
            />
          </q-td>
        </template>

        <template #body-cell-total_payment="props">
          <q-td :props="props">
            ¥{{ formatAmount(props.row.total_payment) }}
          </q-td>
        </template>

        <template #body-cell-purchase_time="props">
          <q-td :props="props">
            {{ showDateTime(props.row.purchase_time) }}
          </q-td>
        </template>

        <template #body-cell-pay_time="props">
          <q-td :props="props">
            {{ showDateTime(props.row.pay_time) }}
          </q-td>
        </template>

        <template #body-cell-delivery_time="props">
          <q-td :props="props">
            {{ showDateTime(props.row.delivery_time) }}
          </q-td>
        </template>

        <template #body-cell-actions="props">
          <q-td :props="props">
            <div class="q-gutter-xs">
              <q-btn-group>
                <q-btn
                  color="secondary"
                  label="详情"
                  size="sm"
                  @click="handleDetail(props.row.id)"
                />
              </q-btn-group>
            </div>
          </q-td>
        </template>
      </q-table>
    </div>
</template>

<script setup>
import { Notify } from "quasar";
import { getActionByPath } from "src/api/manage";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import useTableData from "src/composables/useTableData";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const router = useRouter();
const searchParams = ref({
  platform_order_serial: '',
  serial: '',
  platform_name: '',
  status: '',
});

// 搜索功能
const handleSearch = () => {
  // 过滤空值，只传递有值的搜索参数
  const filteredParams = {};
  Object.keys(searchParams.value).forEach(key => {
    if (searchParams.value[key] && searchParams.value[key].trim() !== '') {
      filteredParams[key] = searchParams.value[key].trim();
    }
  });
  // 保持原有的contract_id过滤条件
  if (props.itemId) {
    filteredParams.contract_id = props.itemId;
  }
  queryParams.value = filteredParams;
  getTableData();
};

// 重置搜索
const resetSearch = () => {
  searchParams.value = {
    platform_order_serial: '',
    serial: '',
    platform_name: '',
    status: '',
  };
  // 重置时保持contract_id过滤条件
  queryParams.value = props.itemId ? { contract_id: props.itemId } : {};
  getTableData();
};
// Props定义
const props = defineProps({
  itemId: {
    type: String,
    required: true
  }
});

const url = {
  list: "/api/sales_order/list",
  item: "/api/sales_order",
  delete: "/api/sales_order",
};
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
} = useTableData(url);

// 表格列定义
const columns = computed(() => {
  return [
    {
      name: "platform_order_serial",
      align: "center",
      label: "平台订单编号",
      field: "platform_order_serial",
    },
    {
      name: "platform_name",
      align: "center",
      label: "销售平台",
      field: "platform_name",
    },
    {
      name: "serial",
      required: true,
      align: "center",
      label: "订单编号",
      field: "serial",
    },
    {
      name: "total_payment",
      required: true,
      align: "center",
      label: "订单总额",
      field: "total_payment",
    },
    {
      name: "purchase_time",
      required: true,
      align: "center",
      label: "下单时间",
      field: "purchase_time",
    },
    {
      name: "pay_time",
      required: true,
      align: "center",
      label: "支付时间",
      field: "pay_time",
    },
    {
      name: "delivery_time",
      required: true,
      align: "center",
      label: "发货时间",
      field: "delivery_time",
    },
    {
      name: "status",
      align: "center",
      label: "订单状态",
      field: "status",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: "操作",
      field: "actions",
    },
  ];
});

onMounted(async () => {
  if (props.itemId) {
    pagination.value.order = "created_at";
    queryParams.value = {
      'contract_id': props.itemId
    }
    await getTableData();
  } else {
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
  }
});

// 格式化时间显示
const showDateTime = (datetime) => {
  return FormatTimeStamp(datetime);
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

// 查看订单详情
const handleDetail = (id) => {
  router.push({ name: "OrderDetail", query: { id: id } });
};


</script>

<style scoped lang="scss">
.row-title {
  font-weight: 600;
  color: #666;
  padding: 8px 0;
}

.contract-desc {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.text-h6 {
  color: #1976d2;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

:deep(img) {
  max-width: 100%;
}

@media print {
  .q-btn-group {
    display: none;
  }
}
</style>
