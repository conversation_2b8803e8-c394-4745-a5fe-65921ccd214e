import { defineStore } from "pinia";
import { RouteData } from "src/types";
import { getFirst } from "src/utils";
import { LocationQuery, RouteRecordNormalized } from "vue-router";
export const useBreadcrumbsStore = defineStore("breadCrumbs", {
  state: () => ({
    breadcrumbs: [] as RouteData[],
  }),

  getters: {
    getBreadCrumbs(state) {
      return state.breadcrumbs;
    },
  },

  actions: {
    setBreadCrumbs(matched: RouteRecordNormalized[], query: LocationQuery) {
      const temp = [];
      for (let i = 0; i < matched.length; i++) {
        const breadcrumb: RouteData = {
          title: matched[i].meta.title,
          name: matched[i].name,
          fullPath: matched[i].path,
          icon: matched[i].meta.icon,
          keepAlive: matched[i].meta.keepAlive,
        };

        temp.push(breadcrumb);
      }

      const last = temp.length - 1;
      if (Object.keys(query).length) {
        let value = getFirst(query).split(":").slice(1).join(":");
        temp[last].title += "：" + value;
      }

      this.breadcrumbs = temp;
    },
  },
});
