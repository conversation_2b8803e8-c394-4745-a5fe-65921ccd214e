/* eslint-disable */
/**
 * THIS FILE IS GENERATED AUTOMATICALLY.
 * DO NOT EDIT.
 *
 * You are probably looking on adding startup/initialization code.
 * Use "quasar new boot <name>" and add it there.
 * One boot file per concern. Then reference the file(s) in quasar.config.js > boot:
 * boot: ['file', ...] // do not add ".js" extension to it.
 *
 * Boot files are your "main.js"
 **/

import lang from 'quasar/lang/zh-CN.js'

import iconSet from 'quasar/icon-set/material-icons.js'



import {SessionStorage,LocalStorage,Loading,LoadingBar,Notify,Dialog,Meta,AppFullscreen,Cookies} from 'quasar'



export default { config: {},lang,iconSet,plugins: {SessionStorage,LocalStorage,Loading,LoadingBar,Notify,Dialog,Meta,AppFullscreen,Cookies} }

