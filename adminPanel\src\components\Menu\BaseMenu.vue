<template>
  <q-scroll-area :thumb-style="thumbStyle">
    <q-list>
      <BaseMenuItem :my-router="router" />
    </q-list>
  </q-scroll-area>
</template>

<script lang="js" setup>
import BaseMenuItem from "src/components/Menu/BaseMenuItem.vue";
import { useRouterStore } from "src/stores/permission";

defineOptions({ name: "BaseMenu" });

const thumbStyle = {
  right: "1px",
  borderRadius: "5px",
  backgroundColor: "#616161",
  width: "5px",
};

const store = useRouterStore();
const router = store.getVisibleMenu;
</script>
