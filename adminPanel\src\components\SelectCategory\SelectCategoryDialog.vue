<template>
  <q-dialog v-model="selectItemVisible">
    <q-card style="min-width: 700px; max-width: 45vw">
      <q-card-section class="row justify-between items-center">
        <div class="text-subtitle1">
          <span v-if="title !== ''">
            {{ title }}
            {{
              selection === "multiple"
                ? $t("SelectMultiple")
                : $t("SelectSingle")
            }}
          </span>
          <span v-else>
            {{
              $t("SelectOrderComponent", {
                oneOrMultiple:
                  selection === "multiple"
                    ? $t("SelectMultiple")
                    : $t("SelectSingle"),
              })
            }}
          </span>
        </div>
        <span
          v-if="selection === 'multiple'"
          class="text-subtitle2 text-negative row justify-center"
        >
          多选打勾后，翻页不会保存
        </span>
        <q-btn
          v-if="selection === 'multiple'"
          dense
          color="primary"
          :label="$t('Select')"
          @click="handleSelect"
        />
      </q-card-section>

      <q-separator />

      <q-card-section class="items-center row q-gutter-md">
        <q-hierarchy
          class="col"
          separator="cell"
          dense
          :columns="columns"
          :data="tableData"
        >
          <template #body="props">
            <TreeTd :tree-td="props" first-td="sort" class="text-center" />
            <td class="text-center">
              {{ props.item.id }}
            </td>
            <td class="text-center">
              {{ props.item.name }}
            </td>
            <td class="text-center">
              {{ props.item.level + 1 }}
            </td>
            <td class="text-center">
              <div class="q-gutter-xs">
                <q-btn
                  v-if="selection !== 'multiple'"
                  dense
                  color="primary"
                  :label="$t('Select')"
                  @click="handleSelect(props.item)"
                />
              </div>
            </td>
          </template>
        </q-hierarchy>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import TreeTd from "src/components/TreeTd/index.vue";
import useTableData from "src/composables/useTableData";
import { ArrayOrObject } from "src/utils/arrayOrObject";
import { computed, ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  // 必须传递单选多选: multiple, single
  selection: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: false,
    default: "",
  },
});
const { selection, title } = toRefs(props);
const url = {
  list: "/api/business/category/list",
};
const columns = computed(() => {
  return [
    { name: "sort", align: "center", label: t("Sort"), field: "sort" },
    { name: "id", align: "center", label: t("ID"), field: "id" },
    { name: "name", align: "center", label: t("Name"), field: "name" },
    { name: "level", align: "center", label: t("Level"), field: "level" },
    { name: "actions", align: "center", label: t("Actions"), field: "actions" },
  ];
});
const { tableData, queryParams, pagination, getTableData } = useTableData(url);
const selectItemVisible = ref(false);
const selected = ref(null);

const show = (selectItem) => {
  selected.value = [];
  selectItemVisible.value = true;
  queryParams.value.parent_id = 0;
  pagination.value.order = "name";
  pagination.value.rowsPerPage = 9999;
  getTableData();
  if (selection.value === "multiple") {
    if (ArrayOrObject(selectItem) === "Array") {
      selected.value = selectItem;
    } else {
      selected.value = [];
    }
  } else {
    selected.value.push();
  }
};

defineExpose({
  show,
});
const emit = defineEmits(["handleSelect"]);
const handleSelect = (item) => {
  if (item.id) {
    emit("handleSelect", item);
  } else {
    emit("handleSelect", selected.value);
  }
  selectItemVisible.value = false;
};
</script>
