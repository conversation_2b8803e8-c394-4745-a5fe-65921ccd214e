<template>
  <q-page padding>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ t("admin.Add") + $t("Product") + $t("Sku") }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-field
              dense
              class="col-4 q-mx-sm"
              :label="$t('ParentProduct')"
              stack-label
            >
              <template #control>
                <q-option-group
                  v-model="hasParent"
                  :options="parentDict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
            <SelectParentProduct
              v-if="hasParent"
              v-model:selectItem="parent"
              v-model:selectItemName="parent.name"
              selection="single"
              class="col q-mx-sm"
            />
          </div>

          <div class="row q-mt-lg">
            <q-input
              v-model="itemDetail.name"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="t('admin.Name')"
            />
          </div>
          <div class="row">
            <q-input
              v-model.number="itemDetail.costPrice"
              dense
              type="number"
              class="col q-mx-sm"
              :label="$t('Default') + $t('Cost')"
              :input-style="{
                fontSize: ' 24px',
                lineHeight: '36px',
                fontWeight: '600',
                letterSpacing: '8px',
                color: '#F57C00',
              }"
            />
            <q-input
              v-model.number="itemDetail.salesPrice"
              dense
              type="number"
              class="col q-mx-sm"
              :label="$t('SalesPrice')"
              :input-style="{
                fontSize: ' 24px',
                lineHeight: '36px',
                fontWeight: '600',
                letterSpacing: '8px',
                color: '#F57C00',
              }"
            />
            <q-input
              v-model.number="itemDetail.marketPrice"
              dense
              type="number"
              class="col q-mx-sm"
              :label="$t('MarketPrice')"
              :input-style="{
                fontSize: ' 24px',
                lineHeight: '36px',
                fontWeight: '600',
                letterSpacing: '8px',
                color: '#F57C00',
              }"
            />
          </div>
          <div class="row">
            <SelectManufacture
              v-model:selectItem="manufacture"
              v-model:selectItemName="manufacture.name"
              selection="single"
              class="col q-mx-sm"
            />
            <q-input
              v-model="itemDetail.brand"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Brand')"
            />
            <q-input
              v-model="itemDetail.sourceArea"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('SourceArea')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="itemDetail.model"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Model')"
            />
            <q-input
              v-model="itemDetail.specification"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Specification')"
            />
            <q-input
              v-model="itemDetail.size"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Size')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="itemDetail.barcode"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Barcode')"
            />
            <q-input
              v-model="itemDetail.package"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Package')"
            />
            <q-input
              v-model="itemDetail.unit"
              maxlength="250"
              dense
              class="col q-mx-sm"
              :label="$t('Unit')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="itemDetail.desc"
              maxlength="250"
              type="textarea"
              rows="2"
              dense
              counter
              :label="$t('Desc')"
              class="q-mx-sm col"
            />
          </div>
          <q-separator class="q-my-md" />
          <div class="row">
            <q-btn
              label="新增属性值"
              color="primary"
              size="sm"
              @click="addAttr"
            />
          </div>
          <div class="row">
            <div
              v-for="(item, index) in attributeList"
              :key="index"
              class="col-6"
            >
              <div class="row items-center justify-center">
                <span class="col-1 text-h5 text-center">{{ index + 1 }}</span>
                <q-input
                  v-model="item.name"
                  dense
                  label="属性名"
                  class="col q-mx-sm"
                />
                <q-input
                  v-model="item.value"
                  dense
                  label="属性值"
                  class="col q-mx-sm"
                />
                <div class="col-1 q-mx-sm">
                  <q-btn color="negative" size="xs" @click="removeAttr">
                    <q-icon name="close" />
                  </q-btn>
                </div>
              </div>
            </div>
          </div>
          <q-separator class="q-my-md" />
          <div class="row q-my-md">
            <Tinymce
              :value="itemDetail.content"
              class="col"
              @getContent="getContent"
            />
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="$t('Save')"
          color="primary"
          @click="handleProductAction"
        />
        <q-btn
          v-close-popup
          :label="$t('Cancel')"
          color="negative"
          @click="closeTab"
        />
      </q-card-actions>
    </q-card>
  </q-page>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import Tinymce from "src/components/Editor/TinyMce";
import SelectManufacture from "src/components/SelectManufacture";
import SelectParentProduct from "src/components/SelectParentProduct";
import { useTabMenuStore } from "stores/tabMenu";
import { nextTick, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

const tabMenuStore = useTabMenuStore();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const url = {
  item: "system/product/v1/productSku",
  getAttr: "system/product/v1/product/attr/list",
};

const itemDetail = ref({});
const parent = ref({});
const hasParent = ref(false);
const parentDict = [
  { label: "新增归属产品", value: false },
  { label: "选择已有产品", value: true },
];
const manufacture = ref({});
const attributeList = ref([]);

onMounted(async () => {
  if (route.query.id) {
    itemDetail.value.parentId = Number(route.query.id);
  } else {
    Notify.create({
      type: "warning",
      message: "信息查询失败，请重试",
      position: "top-right",
    });
    closeTab();
  }
});
watch(parent, async (val) => {
  if (val.id) {
    const { data } = await postAction(url.getAttr, {
      parentId: val.id,
      page: 1,
      pageSize: 999999,
      sortBy: "id",
      desc: false,
    });
    if (data.data) {
      attributeList.value = [];
      data.data.map((item) => {
        attributeList.value.push({
          name: item.name,
          value: "",
        });
      });
    }
  }
});
const recordDetailForm = ref();
const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.item) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    if (parent.value.id) {
      itemDetail.value.parentId = parent.value.id;
    }
    const res = await postAction(url.item, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  itemDetail.value.content = v;
};

const closeTab = () => {
  tabMenuStore.CloseTab(route.fullPath);
  nextTick(() => {
    const currentTab = tabMenuStore.currentTab;
    router.push({ path: currentTab.path, query: currentTab.query });
  });
};

const addAttr = () => {
  attributeList.value.push({
    name: "",
    value: "",
  });
};

const removeAttr = (index) => {
  attributeList.value.splice(index, 1);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
