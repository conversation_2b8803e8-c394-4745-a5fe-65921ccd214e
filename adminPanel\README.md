## Quasar Admin

Demo: [https://york11122.github.io/quasar-admin-vue3-typescript/](https://york11122.github.io/quasar-admin-vue3-typescript/)

Built using [Vue3](https://vuejs.org/) + [Pinia ](https://pinia.vuejs.org/)+ [TypeScript ](https://www.typescriptlang.org/)+ [Quasar ](https://quasar.dev/)

Project was created using [quasar-cli](https://quasar.dev/start/quasar-cli)

![IMG_0090](https://user-images.githubusercontent.com/21119213/233842368-44f8caa3-1d02-480a-89df-ab092e2e643d.jpeg)

**Structure**
``` 
src
 |-assets        # static files
 |-boot          # quasar boot files
 |-components    # components
 |-composables   # composable functions
 |-css           # css files
 |-i18n          # multilingual support
 |-layouts       # main layout
 |-pages         # pages
 |-router        # router related
 |-stores        # pinia shared state
 |-utils         # common utilities
 |-types         # typescript type definitions
 quasar.conf.js  # quasar configuration file
 
``` 
