import { useUserStore } from "src/stores/user";

export const usePermission = () => {
  const userStore = useUserStore();
  const isAdmin = userStore.GetAdmin
  function hasPermission(permission: string[]) {
    const { getUserRole } = userStore;
    if (isAdmin) {
      return true
    }
    if (getUserRole) {
      let has = getUserRole.includes("super");
      if (!has) {
        has = getUserRole.some((role) => permission.includes(role));
      }
      return has;
    }
    return false;
  }

  return {
    hasPermission,
  };
};
