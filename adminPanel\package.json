{"name": "cms-console-panel", "version": "1.0.0", "description": "CMS Console Panel", "productName": "CMS Console Panel", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://www.ferychen.com", "private": true, "scripts": {"dev": "quasar dev", "predeploy": "npm run build", "deploy": "gh-pages -d dist/spa -t true", "build": "quasar build", "clean": "quasar clean", "test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@quasar/extras": "^1.16.15", "@tinymce/tinymce-vue": "^4.0.7", "@toast-ui/editor": "^3.2.2", "@toast-ui/editor-plugin-chart": "^3.0.1", "@toast-ui/editor-plugin-code-syntax-highlight": "^3.1.0", "@toast-ui/editor-plugin-table-merged-cell": "^3.1.0", "@toast-ui/vue-editor": "^3.2.3", "@types/prismjs": "^1.26.0", "@vueuse/core": "^12.4.0", "ali-oss": "^6.17.1", "axios": "^1.7.9", "caniuse-lite": "^1.0.30001727", "chart.js": "^4.2.1", "echarts": "^5.4.2", "lottie-web": "^5.11.0", "pinia": "^2.0.33", "prismjs": "^1.29.0", "quasar": "^2.14.3", "tinymce": "^6.3.1", "vue": "^3.2.47", "vue-i18n": "^11.0.1", "vue-router": "^4.1.6", "vue3-baidu-map-gl": "^2.2.2", "xe-utils": "^3.5.7"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^6.0.3", "@quasar/app-vite": "^1.7.3", "@quasar/quasar-app-extension-qiconpicker": "^2.0.7", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@vue-macros/volar": "^0.30.10", "autoprefixer": "^10.4.14", "eslint": "^7.32.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.15.1", "quasar-app-extension-qhierarchy": "^1.0.14", "typescript": "^5.0.2", "unplugin-vue-define-options": "^1.3.2"}, "engines": {"node": ">= 14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}