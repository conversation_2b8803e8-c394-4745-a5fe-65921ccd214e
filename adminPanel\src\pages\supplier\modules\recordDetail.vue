<template>
  <q-dialog
    v-model="recordDetailVisible"
    position="right"
    persistent
    :allow-focus-outside="true"
  >
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Supplier") }}:
          {{ recordDetail.value.name }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row q-my-md">
            <q-input
              v-model="recordDetail.value.serial"
              class="col q-mx-sm"
              :rules="[(val) => !!val || $t('Required')]"
              :label="t('admin.Serial')"
            />
            <q-input
              v-model="recordDetail.value.name"
              class="col q-mx-sm"
              :rules="[(val) => !!val || $t('Required')]"
              label="供应商名称"
            />
          </div>

          <div class="row q-my-md">
            <q-input
              v-model="recordDetail.value.address"
              class="col q-mx-sm"
              label="联系地址"
            />
            <q-input
              v-model="recordDetail.value.website"
              class="col q-mx-sm"
              label="企业网址"
            />
          </div>

          <div class="row q-my-md">
            <q-field
              dense
              class="col q-mx-sm"
              :label="t('admin.Status')"
              stack-label
            >
              <template #control>
                <q-option-group
                  v-model="recordDetail.value.status"
                  :options="statusDict"
                  color="primary"
                  inline
                />
              </template>
            </q-field>
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleCreateAction"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import { getAction, postAction, putAction } from "src/api/manage";
import useRecordDetail from "src/composables/useRecordDetail";
import { useI18n } from "vue-i18n";

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  item: "/api/supplier",
  create: "/api/supplier",
  edit: "/api/supplier",
  delete: "/api/supplier",
  upload: "/api/supplier/image",
};
const statusDict = [
  { label: "启用", value: "on" },
  { label: "停用", value: "off" },
];

const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
} = useRecordDetail(url, emit);

const show = async (row) => {
  recordDetail.value = {};
  loading.value = true;
  if (row && row.id) {
    const { code, data } = await getAction(url.item, { id: row.id });
    if (code === 200) {
      recordDetail.value = data;
    }
  } else {
    recordDetail.value.status = "on";
    recordDetail.value.order = 1;
  }
  recordDetailVisible.value = true;
  loading.value = false;
};

defineExpose({
  show,
  formType,
});

const handleCreateAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (formType.value === "edit") {
      if (url === undefined || !url.edit) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, msg } = await putAction(url.edit, recordDetail.value);
      if (code === 200) {
        $q.notify({
          type: "positive",
          message: msg,
        });
      }
      emit("handleFinish");
      recordDetailVisible.value = false;
    } else if (formType.value === "add") {
      if (url === undefined || !url.create) {
        $q.notify({
          type: "negative",
          message: "请先配置url",
        });
        return;
      }
      const { code, msg, data } = await postAction(
        url.create,
        recordDetail.value,
      );
      if (code === 200) {
        $q.notify({
          type: "positive",
          message: msg,
        });
      }
      recordDetailVisible.value = false;
    } else {
      $q.notify({
        type: "negative",
        message: t("CanNotAddOrEdit"),
      });
    }
    emit("handleFinish");
  } else {
    $q.notify({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};
</script>
