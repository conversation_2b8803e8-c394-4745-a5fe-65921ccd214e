<template>
  <base-content scrollable>
    <div class="q-pa-md q-gutter-sm">
      <q-btn
        class="q-pa-lg"
        color="white" text-color="black"
        label="查看产品列表"
        @click="viewProduct"
      />
      <q-btn
        class="q-pa-lg" color="primary"
        label="查看文章列表"
        @click="viewArticle"
      />
      <q-btn
        class="q-pa-lg" color="secondary"
        label="查看轮播图列表"
        @click="viewSlider"
      />
      <q-btn
        class="q-pa-lg"
        color="amber"
        label="网站基本信息"
        @click="viewWebsite"
      />
    </div>
  </base-content>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Dashboard' })
// import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import BaseContent from 'src/components/BaseContent/BaseContent.vue'

const router = useRouter()

const viewProduct = () => {
  router.push({ name: 'ProductList' })
}

const viewArticle = () => {
  router.push({ name: 'CompanyInfoList' })
}

const viewSlider = () => {
  router.push({ name: 'Slider' })
}

const viewWebsite = () => {
  router.push({ name: 'SiteInfo' })
}

</script>
