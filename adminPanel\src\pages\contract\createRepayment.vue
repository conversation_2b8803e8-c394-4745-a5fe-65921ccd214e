<template>
  <base-content scrollable>
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section class="row">
        <div class="text-h6 q-mx-sm col-auto">创建还款计划</div>
        <div class="col">
          <q-btn label="保存提交" color="primary" class="q-mx-sm" @click="handleSaveAction" />
          <q-btn v-close-popup label="取消" color="negative" class="q-mx-sm" @click="closeTab" />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <!-- 基本信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">基本信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="itemDetail.serial" label="还款计划编号" outlined dense />
            </div>
            <div class="col">
              <q-select v-model="itemDetail.status" :options="statusDict" option-label="label" option-value="value"
                emit-value map-options label="状态" outlined dense />
            </div>
            <div class="col">
              <q-select v-model="itemDetail.repay_status" :options="repayStatusDict" option-label="label"
                option-value="value" emit-value map-options label="还款状态" outlined dense />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.begin_date" label="开始日期" outlined dense type="date" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.end_date" label="结束日期" outlined dense type="date" />
            </div>
          </div>

          <!-- 预计还款信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">预计还款信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="itemDetail.profit_predict" label="预计利润" outlined dense type="number" prefix="¥" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.principal_predict" label="预计本金" outlined dense type="number" prefix="¥" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.amount_predict" label="预计总额" outlined dense type="number" prefix="¥" />
            </div>
          </div>

          <!-- 实际还款信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">实际还款信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="itemDetail.profit_actual" label="实际利润" outlined dense type="number" prefix="¥" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.principal_actual" label="实际本金" outlined dense type="number" prefix="¥" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.amount_actual" label="实际总额" outlined dense type="number" prefix="¥" />
            </div>
          </div>

          <!-- 剩余还款信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">剩余还款信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col">
              <q-input v-model="itemDetail.profit_remain" label="剩余利润" outlined dense type="number" prefix="¥" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.principal_remain" label="剩余本金" outlined dense type="number" prefix="¥" />
            </div>
            <div class="col">
              <q-input v-model="itemDetail.amount_remain" label="剩余总额" outlined dense type="number" prefix="¥" />
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="text-subtitle1 q-mb-md text-primary">备注信息</div>
          <div class="row q-gutter-md q-mb-md">
            <div class="col-12">
              <q-input v-model="itemDetail.remark" label="备注" outlined dense type="textarea" rows="3" />
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions class="q-ma-md">
        <q-btn label="保存提交" color="primary" @click="handleSaveAction" />
        <q-btn v-close-popup label="取消" color="negative" @click="closeTab" />
      </q-card-actions>
    </q-card>
  </base-content>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import { useTagViewStore } from "src/stores/tagView";
import { onMounted, ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";

const tabMenuStore = useTagViewStore();
const route = useRoute();
const router = useRouter();
const url = {
  item: "/api/repayment",
  create: "/api/repayment",
  edit: "/api/repayment",
  order: "/api/sales_order/list",
};

const itemDetail = ref({
});
const selectSupplier = ref({});
const selectWarehouse = ref({});
const selectBidWinner = ref({});
const selectBidOwner = ref({});
const selectApplier = ref({});
const selectFunder = ref({});
const selectScfCompany = ref({});

// 控制显示选择组件还是输入框的布尔字段
const selectSysSupplier = ref(true);
const selectSysWarehouse = ref(true);
const selectSysBidWinner = ref(true);
const selectSysBidOwner = ref(true);
const selectSysApplier = ref(true);
const selectSysFunder = ref(true);
const selectSysScfCompany = ref(true);


const recordDetailForm = ref();

onMounted(async () => {
  await handleInitRecord();
});

// 项目状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
];

// 还款状态字典
const repayStatusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
];

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" },
];

const handleInitRecord = async () => {
  try {
    // 构建RepaymentCreate数据结构
    const repaymentData = {
      serial: null,                    // 还款计划编号
      contract_id: route.query.id,     // 合作项目ID
      profit_predict: null,            // 需要偿还的利润
      principal_predict: null,         // 需要偿还的本金
      amount_predict: null,            // 需要偿还的总额
      profit_actual: null,             // 实际偿还的利润
      principal_actual: null,          // 需要偿还的本金
      amount_actual: null,             // 实际偿还的总额
      profit_remain: null,             // 剩余需要偿还的利润
      principal_remain: null,          // 需要偿还的本金
      amount_remain: null,             // 剩余需要偿还的总额
      begin_date: null,                // 第一笔还款日期
      end_date: null,                  // 最后一笔还款日期
      repay_status: null,              // 还款状态
      remark: null,                    // 备注
      status: "draft",                 // 状态设置为草稿
      // creator_id: null,                // 创建人ID
      // updater_id: null,                // 更新人ID
      // created_at: Date.now(),          // 创建时间
      // updated_at: Date.now(),          // 更新时间
    };

    // 使用url.create创建还款数据
    const res = await postAction(url.create, repaymentData);

    if (res.code === 200) {
      // 创建成功，将返回的数据设置到itemDetail中
      itemDetail.value = repaymentData;
    } else {
      // 创建失败，使用默认数据
      itemDetail.value = repaymentData;
    }
  } catch (error) {
    console.error('初始化还款记录失败:', error);

    // 发生错误时，使用默认数据
    itemDetail.value = {
      serial: null,
      profit_predict: null,
      principal_predict: null,
      amount_predict: null,
      profit_actual: null,
      principal_actual: null,
      amount_actual: null,
      profit_remain: null,
      principal_remain: null,
      amount_remain: null,
      begin_date: null,
      end_date: null,
      repay_status: null,
      remark: null,
      status: "draft",
      creator_id: null,
      updater_id: null,
      created_at: Date.now(),
      updated_at: Date.now(),
    };
  }
};

const handleSaveAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.item) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await postAction(url.item, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const closeTab = () => {
  tabMenuStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
