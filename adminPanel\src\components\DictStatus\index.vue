<template>
  <div>
    <q-chip dense text-color="white" :color="dictStatus ? 'positive' : 'negative'">
      <span v-if="dictCode === 'status'">
        {{ dictStatus ? $t('admin.Enable') : $t('admin.Disable') }}
      </span>
      <span v-if="dictCode === 'stable'">
        {{ dictStatus ? $t('admin.Yes') : $t('admin.No') }}
      </span>
    </q-chip>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
const props = defineProps({
  dictCode: {
    type: String,
    default: '',
  },
  dictStatus: {
    type: Boolean,
    default: false,
  }
})
const { dictCode, dictStatus } = toRefs(props)

</script>
