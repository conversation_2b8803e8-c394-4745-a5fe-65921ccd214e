import { useUserStore } from "src/stores/user";
import { Route } from "src/types"
import { usePermission } from "src/composables/permission"

export default function constructionRouters(router: Route[]): Route[] {
  const userStore = useUserStore();
  const { hasPermission } = usePermission()
  let temp: Route[] = router.filter((item) => {
    // if no roles is setting, everyone can access
    if (!item.meta?.roles || item.meta.roles.length === 0) return true;
    return hasPermission(item.meta.roles)
  }) as Route[];

  // construct router with user permission
  for (const i in temp) {
    if (temp[i].children) {
      temp[i].children = constructionRouters(
        temp[i].children as Route[]
      );
    }
  }
  return temp;
}
