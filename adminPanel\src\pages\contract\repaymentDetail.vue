<template>
  <base-content scrollable>
    <q-card class="q-ma-md">
      <q-card-section class="row">
        <div class="text-h6 q-mx-sm col-auto">
          管理还款计划
          <q-badge v-if="itemDetail.status" :color="getStatusColor(itemDetail.status)"
            :label="getStatusLabel(itemDetail.status)" class="q-ml-sm" />
        </div>
        <div class="col">
          <q-btn label="编辑计划" color="primary" class="q-mx-sm" @click="handleEditRepayment" />
          <q-btn v-close-popup label="关闭页面" color="negative" class="q-mx-sm" @click="closeTab" />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <!-- 基本信息 -->
        <div class="text-subtitle1 q-mb-md text-primary">基本信息</div>
        <div class="row q-gutter-md q-mb-md">
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">还款计划编号</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.serial || '系统自动生成' }}</div>
          </div>
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">状态</div>
            <div class="q-mt-xs">
              <q-badge v-if="itemDetail.status" :color="getStatusColor(itemDetail.status)"
                :label="getStatusLabel(itemDetail.status)" />
              <span v-else class="text-grey-5">未设置</span>
            </div>
          </div>
        </div>

        <!-- 金额信息 -->
        <div class="text-subtitle1 q-mb-md text-primary">金额信息</div>
        <div class="row q-gutter-md q-mb-md">
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">支取本金金额</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.principal_amount ?
              `¥${parseFloat(itemDetail.principal_amount).toFixed(2)}` : '¥0.00' }}</div>
          </div>
          <div class="col">
            <div class="row">
              <div class="col-auto text-h6 text-blue-10">已关联订单金额：</div>
              <div class="col-8 text-h6 text-orange-10">{{ itemDetail.target_amount ? itemDetail.target_amount : 0 }}（元）
              </div>
            </div>
          </div>
        </div>

        <!-- 时间限制 -->
        <div class="text-subtitle1 q-mb-md text-primary">时间限制</div>
        <div class="row q-gutter-md q-mb-md">
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">开始日期</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.begin_date || '未设置' }}</div>
          </div>
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">结束日期</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.end_date || '未设置' }}</div>
          </div>
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">宽限天数</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.grace_date || 0 }} 天</div>
          </div>
        </div>

        <!-- 利润计算配置 -->
        <div class="text-subtitle1 q-mb-md text-primary">利润计算配置</div>
        <div class="row q-gutter-md q-mb-md">
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">利润计算费率</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.profit_calc_fee ? `${(itemDetail.profit_calc_fee *
              100).toFixed(2)}%` : '0.00%' }}</div>
            <div class="text-caption text-grey-6">年化利率</div>
          </div>
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">利润计算周期</div>
            <div class="text-body1 q-mt-xs">{{ getCalcPeriodLabel(itemDetail.profit_calc_period) }}</div>
          </div>
          <div class="col">
            <div class="q-field__label text-caption text-grey-7">违约金计算费率</div>
            <div class="text-body1 q-mt-xs">{{ itemDetail.penalty_calc_fee ? `${(itemDetail.penalty_calc_fee *
              100).toFixed(4)}%` : '0.0000%' }}</div>
            <div class="text-caption text-grey-6">日息利率</div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="text-subtitle1 q-mb-md text-primary">备注信息</div>
        <div class="row">
          <div class="col-12">
            <div class="q-field__label text-caption text-grey-7">还款计划备注</div>
            <div class="text-body1 q-mt-xs q-pa-md bg-grey-1 rounded-borders"
              style="min-height: 80px; white-space: pre-wrap;">{{ itemDetail.remark || '无备注信息' }}</div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <q-card class="q-ma-md">
      <q-card-section>
        <q-btn label="关联订单" color="primary" @click="getOrderList" />
      </q-card-section>
    </q-card>

    <!-- 关联订单列表 -->
    <q-card class="q-ma-md">
      <q-card-section>
        <div class="text-h6 q-mb-md">关联订单列表</div>
        <q-table :rows="relatedOrders" :columns="orderColumns" :loading="ordersLoading" :pagination="pagination"
          @request="handleOrderRequest" row-key="id" flat bordered>
          <!-- 状态列自定义显示 -->
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-badge :color="getOrderStatusColor(props.value)" :label="getOrderStatusLabel(props.value)" />
            </q-td>
          </template>

          <!-- 操作列 -->
          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <q-btn flat dense color="primary" icon="visibility" size="sm" @click="handleViewOrder(props.row)">
                <q-tooltip>查看</q-tooltip>
              </q-btn>
              <q-btn flat dense color="negative" icon="remove_circle" size="sm" @click="handleRemoveOrder(props.row)"
                class="q-ml-xs">
                <q-tooltip>移除</q-tooltip>
              </q-btn>
            </q-td>
          </template>

          <!-- 无数据显示 -->
          <template v-slot:no-data>
            <div class="full-width row flex-center text-grey q-gutter-sm">
              <q-icon size="2em" name="inbox" />
              <span>暂无关联订单</span>
            </div>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <SelectOrderList v-if="itemDetail.id" ref="selectOrderListDialogue" :url-list="url.list"
      :contract-id="itemDetail.contract_id" @handleSelectItem="handleSelectOrder" />

    <!-- 还款计划编辑对话框 -->
    <RepaymentEdit ref="repaymentEditDialog" @handleFinish="handleRepaymentSave" />
  </base-content>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction, putAction, getActionByPath } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import { useTagViewStore } from "src/stores/tagView";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import SelectOrderList from "src/components/SelectOrderList/SelectItemDialog.vue";
import useTableData from "src/composables/useTableData";
import RepaymentEdit from "./modules/repaymentEdit.vue";

const tabMenuStore = useTagViewStore();
const route = useRoute();
const url = {
  item: "/api/repayment",
  create: "/api/repayment",
  edit: "/api/repayment",
  list: "/api/sales_order/list",
  relate: "/api/repayment/relate",
  contract: "/api/financial_contract"
};

const itemDetail = ref({});

const selectOrderListDialogue = ref(null);
const repaymentEditDialog = ref(null);

// 使用 useTableData 管理订单列表
const {
  tableData: relatedOrders,
  pagination,
  loading: ordersLoading,
  queryParams: orderParams,
  onRequest: handleOrderRequest
} = useTableData(url);

// 订单列表表格列定义
const orderColumns = [
  {
    name: 'platform_order_serial',
    label: '平台订单号',
    field: 'platform_order_serial',
    align: 'left',
    sortable: true
  },
  {
    name: 'platform_name',
    label: '平台名称',
    field: 'platform_name',
    align: 'left',
    sortable: true
  },
  {
    name: 'total_payment',
    label: '订单金额',
    field: 'total_payment',
    align: 'right',
    sortable: true,
    format: (val) => val ? `¥${parseFloat(val).toFixed(2)}` : '-'
  },
  {
    name: 'purchase_time',
    label: '下单时间',
    field: 'purchase_time',
    align: 'center',
    sortable: true
  },
  {
    name: 'complete_time',
    label: '完成时间',
    field: 'complete_time',
    align: 'center',
    sortable: true
  },
  {
    name: 'status',
    label: '状态',
    field: 'status',
    align: 'center',
    sortable: true
  },
  {
    name: 'actions',
    label: '操作',
    field: 'actions',
    align: 'center'
  }
];

const getOrderList = () => {
  selectOrderListDialogue.value.show();
};

const handleSelectOrder = async (items) => {
  try {
    // 检查是否有选中的订单
    if (!items || items.length === 0) {
      Notify.create({
        type: "warning",
        message: "请选择要关联的订单",
        position: "top-right",
      });
      return;
    }

    // 检查还款计划ID是否存在
    if (!itemDetail.value.id) {
      Notify.create({
        type: "negative",
        message: "还款计划ID不存在，无法关联订单",
        position: "top-right",
      });
      return;
    }

    // 提取订单ID列表
    const orderIds = items.map(item => item.id);

    // 构建请求数据，符合 RelateOrders 结构
    const requestData = {
      id: itemDetail.value.id,  // 还款计划ID
      ids: orderIds             // 订单ID列表
    };

    console.log('关联订单请求数据:', requestData);

    // 调用关联订单API
    const res = await postAction(url.relate, requestData);

    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: `成功关联 ${items.length} 个订单`,
        position: "top-right",
      });

      // 关联成功后刷新数据
      await handleGetDetail();
      await getRelatedOrderList();
    } else {
      Notify.create({
        type: "negative",
        message: res.msg || "关联订单失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('关联订单出错:', error);
    Notify.create({
      type: "negative",
      message: "关联订单时发生错误，请重试",
      position: "top-right",
    });
  }
}

const contractDetail = ref({});

const getContractDetail = async () => {
  try {
    const { code, data } = await getActionByPath(url.contract, [itemDetail.value.contract_id]);
    if (code === 200) {
      contractDetail.value = data;
    } else {
      contractDetail.value = {};
    }
  } catch (error) {
    console.error('获取合同详情出错:', error);
    contractDetail.value = {};
  }
}

// 获取关联订单列表
const getRelatedOrderList = async () => {
  if (!itemDetail.value.id) return;

  // 设置查询参数
  orderParams.value = {
    repayment_id: itemDetail.value.id
  };

  // 调用表格请求处理函数
  await handleOrderRequest({
    pagination: pagination.value,
    url: url.order,
    params: orderParams.value
  });
};

// 查看订单详情
const handleViewOrder = (row) => {
  // TODO: 实现查看订单详情功能
  console.log('查看订单:', row);
};

// 移除订单关联
const handleRemoveOrder = (row) => {
  // TODO: 实现移除订单关联功能
  console.log('移除订单:', row);
};

// 订单状态字典
const orderStatusDict = [
  { label: "待付款", value: "pending_payment" },
  { label: "已付款", value: "paid" },
  { label: "已发货", value: "shipped" },
  { label: "已完成", value: "completed" },
  { label: "已取消", value: "cancelled" },
  { label: "退款中", value: "refunding" },
  { label: "已退款", value: "refunded" },
];

// 获取订单状态标签
const getOrderStatusLabel = (status) => {
  const statusItem = orderStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取订单状态颜色
const getOrderStatusColor = (status) => {
  const colorMap = {
    'pending_payment': 'orange',
    'paid': 'blue',
    'shipped': 'purple',
    'completed': 'green',
    'cancelled': 'grey',
    'refunding': 'amber',
    'refunded': 'red'
  };
  return colorMap[status] || 'grey';
};

onMounted(async () => {
  console.log(route.query.id, "当前route");
  if (route.query.id) {
    await handleGetDetail();
    await getContractDetail();
    await getRelatedOrderList();
  } else {
    closeTab();
  }

});

// 项目状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "待还款", value: "pending" },
  { label: "部分还款", value: "partial" },
  { label: "已完成", value: "completed" },
  { label: "逾期", value: "overdue" },
];

// 获取状态标签
const getStatusLabel = (status) => {
  const statusItem = statusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'draft': 'grey',
    'new': 'blue',
    'processing': 'orange',
    'pending': 'purple',
    'partial': 'amber',
    'completed': 'green',
    'overdue': 'red'
  };
  return colorMap[status] || 'grey';
};

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" },
];

// 获取计算周期标签
const getCalcPeriodLabel = (value) => {
  const periodItem = calcPeriodDict.find(item => item.value === value);
  return periodItem ? periodItem.label : value || '未设置';
};

const handleGetDetail = async () => {
  const res = await getActionByPath(url.item, [route.query.id]);
  if (res.code === 200) {
    itemDetail.value = res.data;
    // 异步加载关联订单列表
    setTimeout(() => {
      getRelatedOrderList();
    }, 100);
  }
};

// 打开编辑对话框
const handleEditRepayment = () => {
  itemDetail.value.contract = contractDetail.value
  repaymentEditDialog.value.show(itemDetail.value);
};

// 处理还款计划保存
const handleRepaymentSave = async (data) => {
  try {
    delete data.contract;
    const res = await putAction(url.item, data);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg || "保存成功",
        position: "top-right",
      });
      // 重新获取详情数据
      await handleGetDetail();
    } else {
      Notify.create({
        type: "negative",
        message: res.msg || "保存失败",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('保存还款计划失败:', error);
    Notify.create({
      type: "negative",
      message: "保存失败，请重试",
      position: "top-right",
    });
  }
};

const closeTab = () => {
  tabMenuStore.removeTagViewByFullPath(route.fullPath);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
