<template>
  <q-dialog
    v-model="recordDetailVisible"
    position="right"
    persistent
    :allow-focus-outside="true"
  >
    <q-card style="width: 80vw; max-width: 80vw">
      <q-card-section>
        <div class="text-h6 q-mb-md">导入记录详情</div>
        <div v-if="recordItem">
          <div class="row">
            <div class="col q-mx-sm">
              <q-field label="合同名称" stack-label>
                <template v-slot:control>
                  <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.contract_name || '-' }}</div>
                </template>
              </q-field>
            </div>
            <div class="col q-mx-sm">
              <q-field label="数据来源" stack-label>
                <template v-slot:control>
                  <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.source }}</div>
                </template>
              </q-field>
            </div>
          </div>
         <div class="row">
           <div class="col q-mx-sm">
            <q-field label="总数量" stack-label>
              <template v-slot:control>
                <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.total_count }}</div>
              </template>
            </q-field>
          </div>
          <div class="col q-mx-sm">
            <q-field label="新增数量" stack-label>
              <template v-slot:control>
                <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.new_count }}</div>
              </template>
            </q-field>
          </div>
          <div class="col q-mx-sm">
            <q-field label="更新数量" stack-label>
              <template v-slot:control>
                <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.update_count }}</div>
              </template>
            </q-field>
          </div>
          <div class="col q-mx-sm">
            <q-field label="无变化数量" stack-label>
              <template v-slot:control>
                <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.no_change_count }}</div>
              </template>
            </q-field>
          </div>
          <div class="col q-mx-sm">
            <q-field label="成功数量" stack-label>
              <template v-slot:control>
                <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.success_count }}</div>
              </template>
            </q-field>
          </div>
          <div class="col q-mx-sm">
            <q-field label="失败数量" stack-label>
              <template v-slot:control>
                <div class="self-center full-width no-outline" tabindex="0">{{ recordItem.fail_count }}</div>
              </template>
            </q-field>
          </div>
         </div>
        </div>
        <div v-else class="text-center text-grey-6">
          暂无数据
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <div class="text-h6 q-mb-md">关联销售订单</div>
        <div v-if="salesOrderList && salesOrderList.length > 0">
          <q-table
            :rows="salesOrderList"
            :columns="[
              { name: 'platform_order_serial', label: '平台订单号', field: 'platform_order_serial', align: 'left' },
              { name: 'platform_name', label: '平台名称', field: 'platform_name', align: 'left' },
              { name: 'total_payment', label: '总金额', field: 'total_payment', align: 'right', format: (val) => `¥${val}` },
              { name: 'status', label: '状态', field: 'status', align: 'center' }
            ]"
            row-key="id"
            flat
            bordered
            v-model:pagination="pagination"
            @request="onRequest"
          >
            <template v-slot:body-cell-status="props">
              <q-td :props="props">
                <q-badge :color="props.value === '已完成' ? 'positive' : props.value === '配送中' ? 'acent' : 'warning'">
                  {{ props.value }}
                </q-badge>
              </q-td>
            </template>
          </q-table>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">
          暂无关联的销售订单
        </div>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleCreateAction"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from "quasar";
import useRecordDetail from "src/composables/useRecordDetail";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { postAction, getActionByPath } from "src/api/manage";

const $q = useQuasar();
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  list: "/api/sales_order/list",
  item: "/api/import_record",
};
const {
  recordDetail,
  recordDetailVisible,
  loading,
  recordDetailForm,
} = useRecordDetail(url, emit);

const pagination = ref({
  sortBy: "created_at",
  descending: false,
  page: 1,
  rowsPerPage: 15,
  rowsNumber: 0
});

const recordItem = ref();
const salesOrderList = ref([]);

const show = async (row) => {
  if (!row || !row.id) {
    $q.notify({
      message: "记录数据不存在或缺少ID",
      color: "negative",
    });
    return;
  }

  loading.value = true;

  await getRecordDetail(row.id);
  loading.value = false;

  recordDetailVisible.value = true;
};

const getRecordDetail = async (id) => {
  try {
    const res = await getActionByPath(url.item, [id]);
    recordItem.value = res.data;
    // 获取到recordItem后，调用getRecordOrderList
    await getRecordOrderList();
  } catch (error) {
    $q.notify({
      message: error.message,
      color: "negative",
    });
  }
};

const getRecordOrderList = async () => {
  if (!recordItem.value || !recordItem.value.serial) {
    return;
  }

  const allParams = {
    options: {
      order_by: pagination.value.sortBy,
      desc: pagination.value.descending,
    },
    page: {
      page: pagination.value.page,
      limit: pagination.value.rowsPerPage,
    },
    params: [{
      var: 'import_record',
      val: recordItem.value.serial
    }]
  }

  try {
    const {code, data,  msg} = await postAction(url.list, allParams);
    if (code === 200) {
      salesOrderList.value = data.data;
      pagination.value.rowsNumber = data.total;
    } else {
      $q.notify({
        message: msg,
        color: "negative",
      });
    }
  } catch (error) {
    $q.notify({
      message: error.message,
      color: "negative",
    });
    salesOrderList.value = [];
  }
}

// 处理表格分页请求
const onRequest = async (props) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  // 更新分页信息
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  // 重新获取数据
  await getRecordOrderList();
}


defineExpose({
  show,
});
</script>
