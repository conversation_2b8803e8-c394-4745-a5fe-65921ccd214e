<template>
  <base-content scrollable>
    <div class="row q-pa-md">
      <q-card class="col">
        <q-card-section>
          <div class="text-h6">
            {{ t("admin.Add") + t("admin.Product") }}
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form ref="recordDetailForm">
            <div class="row">
              <q-toggle
                v-model="itemDetail.is_active"
                class="col q-mx-sm q-py-sm"
                :label="t('admin.Active')"
              />
              <q-toggle
                v-model="itemDetail.fix_top"
                class="col q-mx-sm q-py-sm"
                :label="t('admin.Fixed')"
              />
              <q-input
                v-model.number="itemDetail.sort"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.ShowIndex') + t('admin.Sort')"
              />
            </div>
            <q-separator />
            <div class="row">
              <q-input
                v-model="itemDetail.name"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Name')"
              />
              <SelectCategory
                v-model:selectItem="selectItem"
                v-model:selectItemId="itemDetail.category_id"
                v-model:selectItemName="itemDetail.category_name"
                selection="single"
                class="col q-mx-sm"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.brand"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Brand')"
              />
              <q-input
                v-model="itemDetail.model"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Model')"
              />
              <q-input
                v-model="itemDetail.source_area"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.SourceArea')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.weight"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Weight')"
              />
              <q-input
                v-model="itemDetail.size"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Size')"
              />
              <q-input
                v-model="itemDetail.unit"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Unit')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.barcode"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Barcode')"
              />
              <q-input
                v-model="itemDetail.pack_size"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Package') + t('admin.Size')"
              />
              <q-input
                v-model="itemDetail.delivery_size"
                maxlength="250"
                counter
                class="col q-mx-sm"
                :label="t('admin.Delivery') + t('admin.Size')"
              />
            </div>
            <div class="row">
              <q-input
                v-model.number="itemDetail.currency"
                maxlength="50"
                counter
                class="col q-mx-sm"
                :label="t('admin.Currency')"
              />
              <q-input
                v-model.number="itemDetail.sales_price"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.Sales') + t('admin.Price')"
              />
              <q-input
                v-model.number="itemDetail.market_price"
                type="number"
                class="col q-mx-sm"
                :label="t('admin.Market') + t('admin.Price')"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.buy_link"
                maxlength="250"
                counter
                :label="t('admin.BuyLink')"
                class="col q-mx-sm"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.remark"
                maxlength="250"
                counter
                :label="t('admin.Remark')"
                class="col q-mx-sm"
              />
            </div>
            <div class="row">
              <q-input
                v-model="itemDetail.seo_kw"
                maxlength="250"
                counter
                :label="t('admin.Seo') + t('admin.Keyword')"
                class="col q-mx-sm"
              />
              <q-input
                v-model="itemDetail.desc"
                maxlength="250"
                counter
                :label="t('admin.Seo') + t('admin.Description')"
                class="col q-mx-sm"
              />
            </div>
            <q-card class="q-ma-sm">
              <div class="row">
                <div class="col q-pa-lg">
                  <strong class="q-mr-md">产品SKU</strong>
                  <q-toggle
                    v-model="hasSku"
                    class="col q-mx-lg"
                    :label="t('admin.Active')"
                    @click="changeSkuList"
                  />
                  <q-btn
                    v-if="hasSku"
                    color="primary"
                    size="sm"
                    :label="t('admin.Add') + t('admin.Sku')"
                    @click="handleSkuAdd"
                  />
                </div>
              </div>
              <div v-if="hasSku" class="row">
                <q-table
                  class="col"
                  row-key="index"
                  separator="cell"
                  :rows="itemDetail.sku"
                  :columns="skuColumns"
                >
                  <template #body-cell-actions="props">
                    <q-td :props="props">
                      <div class="q-gutter-xs">
                        <q-btn-group>
                          <q-btn
                            color="primary"
                            :label="t('admin.Edit')"
                            size="sm"
                            @click="handleSkuEdit(props.row, props.rowIndex)"
                          />
                          <q-btn
                            color="negative"
                            :label="t('admin.Remove')"
                            @click="handleSkuRemove(props.rowIndex)"
                          />
                        </q-btn-group>
                      </div>
                    </q-td>
                  </template>
                </q-table>
              </div>
            </q-card>
            <q-card class="q-ma-sm">
              <div class="row">
                <div class="col q-pa-lg">
                  <strong>产品其他属性</strong>
                  <q-toggle
                    v-model="hasAttr"
                    class="col q-mx-lg"
                    :label="t('admin.Active')"
                    @click="changeAttrList"
                  />
                  <q-btn
                    v-if="hasAttr"
                    color="primary"
                    size="sm"
                    :label="t('admin.Add') + t('admin.Attributes')"
                    @click="handleAttrAdd"
                  />
                </div>
              </div>
              <div v-if="hasAttr">
                <q-table
                  v-if="itemDetail.attributes"
                  row-key="id"
                  grid
                  :hide-pagination="true"
                  :rows="itemDetail.attributes"
                >
                  <template #item="props">
                    <div class="q-pa-xs col-xs-6 col-sm-4 col-md-4">
                      <q-card>
                        <q-card-section>
                          <div class="row">
                            <div class="col-3 attr-label">
                              {{ props.row.name }}
                            </div>
                            <div class="col">
                              {{ props.row.value }}
                            </div>
                            <div class="col">
                              ({{ t("admin.Sort") }}:{{ props.row.sort }})
                            </div>
                            <div class="col-auto">
                              <q-btn-group push>
                                <q-btn
                                  size="sm"
                                  color="primary"
                                  icon="edit"
                                  @click="
                                    handleAttrEdit(props.row, props.rowIndex)
                                  "
                                />
                                <q-btn
                                  size="sm"
                                  color="negative"
                                  icon="delete"
                                  @click="handleAttrRemove(props.rowIndex)"
                                />
                              </q-btn-group>
                            </div>
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </template>
                </q-table>
              </div>
            </q-card>
            <q-separator class="q-my-md" />
            <div class="row">
              <q-tabs
                v-model="tab"
                dense
                class="text-grey"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
              >
                <q-tab name="content" :label="t('admin.Content')" />
                <q-tab name="specification" :label="t('admin.Specification')" />
              </q-tabs>
            </div>
            <div class="row">
              <q-tab-panels v-model="tab" animated class="col">
                <q-tab-panel name="content">
                  <div class="row">
                    <Tinymce
                      :value="itemDetail.content"
                      class="col"
                      @getContent="getContent"
                    />
                  </div>
                </q-tab-panel>

                <q-tab-panel name="specification">
                  <Tinymce
                    :value="itemDetail.specification"
                    class="col"
                    @getContent="getSpecification"
                  />
                </q-tab-panel>
              </q-tab-panels>
            </div>
          </q-form>
        </q-card-section>

        <q-separator />

        <q-card-actions align="left">
          <q-btn
            :label="t('admin.Save')"
            color="primary"
            @click="handleProductAction"
          />
          <q-btn
            v-close-popup
            :label="t('admin.Cancel')"
            color="negative"
            @click="closeTab"
          />
        </q-card-actions>
      </q-card>
      <SkuDetail ref="skuDetailDialog" @handleFinish="handleProductSku" />
      <AttrDetail ref="attrDetailDialog" @handleFinish="handleProductAttr" />
    </div>
  </base-content>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import Tinymce from "src/components/Editor/TinyMce.vue";
import SelectCategory from "src/components/SelectCategory/index.vue";
import AttrDetail from "src/pages/product/modules/attributeDetail.vue";
import SkuDetail from "src/pages/product/modules/skuDetail.vue";
import { useTagViewStore } from "src/stores/tagView";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

const tagViewStore = useTagViewStore();
const route = useRoute();
const { t } = useI18n();
const url = {
  create: "/api/business/product",
  edit: "/api/business/product",
  delete: "/api/business/product",
};

const itemDetail = ref({});
const tab = ref("content");
const skuDetailDialog = ref(null);
const attrDetailDialog = ref(null);

onMounted(() => {
  itemDetail.value.is_active = true;
  itemDetail.value.fix_top = false;
  itemDetail.value.show_index = false;
  itemDetail.value.sku = [];
  itemDetail.value.attributes = [];
  itemDetail.value.sort = 0;
});
const recordDetailForm = ref();
const selectItem = ref();
const hasSku = ref(false);
const hasAttr = ref(false);

const handleProductAction = async () => {
  const success = await recordDetailForm.value.validate();
  if (success) {
    if (url === undefined || !url.create) {
      Notify.create({
        type: "negative",
        message: "请先配置url",
      });
      return;
    }
    const res = await postAction(url.create, itemDetail.value);
    if (res.code === 200) {
      Notify.create({
        type: "positive",
        message: res.msg,
      });
      closeTab();
    }
  } else {
    Notify.create({
      type: "negative",
      message: "请检查表单信息是否正确",
    });
  }
};

const getContent = (v) => {
  itemDetail.value.content = v;
};

const getSpecification = (v) => {
  itemDetail.value.specification = v;
};

const closeTab = () => {
  tagViewStore.removeTagViewByFullPath(route.fullPath);
};

const changeSkuList = () => {
  if (!hasSku) {
    itemDetail.value.sku = [];
  }
};

const changeAttrList = () => {
  if (!hasAttr) {
    itemDetail.value.attributes = [];
  }
};

const skuColumns = computed(() => {
  return [
    {
      name: "name",
      required: true,
      align: "center",
      label: t("admin.Name"),
      field: "name",
    },
    {
      name: "sales_price",
      align: "center",
      label: t("admin.Sales") + t("admin.Price"),
      field: "sales_price",
    },
    {
      name: "market_price",
      align: "center",
      label: t("admin.Market") + t("admin.Price"),
      field: "market_price",
    },
    {
      name: "model",
      align: "center",
      label: t("admin.Model"),
      field: "model",
    },
    {
      name: "specification",
      align: "center",
      label: t("admin.Specification"),
      field: "specification",
    },
    { name: "size", align: "center", label: t("admin.Size"), field: "size" },
    {
      name: "package",
      align: "center",
      label: t("admin.Package"),
      field: "package",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});

const handleProductSku = (item, idx) => {
  console.log(item, idx);
  if (idx >= 0) {
    itemDetail.value.sku[idx] = item;
  } else {
    itemDetail.value.sku.push(item);
  }
};

const handleSkuAdd = () => {
  skuDetailDialog.value.formType = "add";
  skuDetailDialog.value.show();
};

const handleSkuEdit = (item, index) => {
  console.log(item);
  skuDetailDialog.value.formType = "edit";
  skuDetailDialog.value.show(item, index);
};

const handleSkuRemove = (idx) => {
  itemDetail.value.sku.splice(idx, 1);
};

const handleProductAttr = (item, idx) => {
  console.log(item, idx);
  if (idx >= 0) {
    itemDetail.value.attributes[idx] = item;
  } else {
    itemDetail.value.attributes.push(item);
  }
};

const handleAttrAdd = () => {
  attrDetailDialog.value.formType = "add";
  attrDetailDialog.value.show();
};

const handleAttrEdit = (item, index) => {
  console.log(item);
  attrDetailDialog.value.formType = "edit";
  attrDetailDialog.value.show(item, index);
};

const handleAttrRemove = (idx) => {
  itemDetail.value.attributes.splice(idx, 1);
};
</script>

<style scoped lang="scss">
.product-detail {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .title-place {
    font-weight: 600;
    color: rgb(58, 111, 204);
  }

  .product-detail-row {
    padding: 5px;
  }

  .product-detail-label:after {
    content: "=";
    display: inline-block;
    padding: 0 12px;
    color: #26ceba;
  }

  .product-detail-value:before {
    content: "( ";
    color: #ffc069;
  }

  .product-detail-value:after {
    content: " )";
    color: #ffc069;
  }
}

.attr-label:after {
  content: "=";
  display: inline-block;
  padding: 0 12px;
  color: #26ceba;
}
</style>
