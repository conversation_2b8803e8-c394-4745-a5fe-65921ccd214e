/**
 * TinyMCE version 6.3.1 (2022-12-06)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,r=e=>t=>e===t,s=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=r(null),d=n("boolean"),u=r(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,x=e=>e,w=(e,t)=>e===t;function S(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),C=e=>()=>{throw new Error(e)},O=e=>e(),_=y(!1),T=y(!0);var E=tinymce.util.Tools.resolve("tinymce.ThemeManager");class M{constructor(e,t){this.tag=e,this.value=t}static some(e){return new M(!0,e)}static none(){return M.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?M.some(e(this.value)):M.none()}bind(e){return this.tag?e(this.value):M.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:M.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?M.some(e):M.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}M.singletonNone=new M(!1);const A=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,F=(e,t)=>D.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?M.none():M.some(o)},R=(e,t)=>F(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},V=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},H=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const r=A.call(e,n,n+t);o.push(r)}return o},z=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},L=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(L(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return M.some(r);if(o(r,n))break}return M.none()})(e,t,_),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return M.some(o);return M.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},X=(e,t)=>q(z(e,t)),K=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=A.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=A.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?M.some(e[t]):M.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),re=p(Array.from)?Array.from:e=>A.call(e),se=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return M.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,r=o.length;n<r;n++){const r=o[n],s=e[r];if(t(s,r,e))return M.some(s)}return M.none()},fe=e=>pe(e,x),be=(e,t)=>ve(e,t)?M.from(e[t]):M.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=w)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?M.some(o(e.getOrDie(),t.getOrDie())):M.none(),ke=(e,t)=>e?M.some(t):M.none(),Ce=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Oe=(e,t,o=0,n)=>{const r=e.indexOf(t,o);return-1!==r&&(!!u(n)||r+t.length<=n)},_e=(e,t)=>Ce(e,t,e.length-t.length),Te=(Co=/^\s+|\s+$/g,e=>e.replace(Co,"")),Ee=e=>e.length>0,Me=e=>void 0!==e.style&&p(e.style.getPropertyValue),Ae=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},De=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Ae(o.childNodes[0])},Be=(e,t)=>{const o=(t||document).createElement(e);return Ae(o)},Fe=(e,t)=>{const o=(t||document).createTextNode(e);return Ae(o)},Ie=Ae,Re="undefined"!=typeof window?window:Function("return this;")(),Ne=(e,t)=>((e,t)=>{let o=null!=t?t:Re;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Ve=Object.getPrototypeOf,He=e=>{const t=Ne("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Ne(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Ve(e).constructor.name))},ze=e=>e.dom.nodeName.toLowerCase(),Le=e=>t=>(e=>e.dom.nodeType)(t)===e,Pe=Le(1),Ue=Le(3),We=Le(9),je=Le(11),Ge=e=>t=>Pe(t)&&ze(t)===e,$e=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},qe=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Xe=(e,t)=>e.dom===t.dom,Ke=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Ye=e=>Ie(e.dom.ownerDocument),Je=e=>We(e)?e:Ye(e),Ze=e=>Ie(Je(e).dom.documentElement),Qe=e=>Ie(Je(e).dom.defaultView),et=e=>M.from(e.dom.parentNode).map(Ie),tt=e=>M.from(e.dom.parentElement).map(Ie),ot=e=>M.from(e.dom.offsetParent).map(Ie),nt=e=>z(e.dom.childNodes,Ie),rt=(e,t)=>{const o=e.dom.childNodes;return M.from(o[t]).map(Ie)},st=(e,t)=>({element:e,offset:t}),at=(e,t)=>{const o=nt(e);return o.length>0&&t<o.length?st(o[t],0):st(e,t)},it=e=>je(e)&&g(e.dom.host),lt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),ct=y(lt),dt=lt?e=>Ie(e.dom.getRootNode()):Je,ut=e=>it(e)?e:Ie(Je(e).dom.body),mt=e=>{const t=dt(e);return it(t)?M.some(t):M.none()},gt=e=>Ie(e.dom.host),pt=e=>{const t=Ue(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return mt(Ie(t)).fold((()=>o.body.contains(t)),(n=pt,r=gt,e=>n(r(e))));var n,r},ht=()=>ft(Ie(document)),ft=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Ie(t)},bt=(e,t,o)=>{if(!(s(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},vt=(e,t,o)=>{bt(e.dom,t,o)},yt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{bt(o,t,e)}))},xt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},wt=(e,t)=>M.from(xt(e,t)),St=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},kt=(e,t)=>{e.dom.removeAttribute(t)},Ct=(e,t,o)=>{if(!s(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Me(e)&&e.style.setProperty(t,o)},Ot=(e,t)=>{Me(e)&&e.style.removeProperty(t)},_t=(e,t,o)=>{const n=e.dom;Ct(n,t,o)},Tt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{Ct(o,t,e)}))},Et=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Ot(o,t)}),(e=>{Ct(o,t,e)}))}))},Mt=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||pt(e)?n:At(o,t)},At=(e,t)=>Me(e)?e.style.getPropertyValue(t):"",Dt=(e,t)=>{const o=e.dom,n=At(o,t);return M.from(n).filter((e=>e.length>0))},Bt=e=>{const t={},o=e.dom;if(Me(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Ft=(e,t,o)=>{const n=Be(e);return _t(n,t,o),Dt(n,t).isSome()},It=(e,t)=>{const o=e.dom;Ot(o,t),xe(wt(e,"style").map(Te),"")&&kt(e,"style")},Rt=e=>e.dom.offsetWidth,Nt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Mt(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Mt(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Me(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Vt=Nt("height",(e=>{const t=e.dom;return pt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Ht=e=>Vt.get(e),zt=e=>Vt.getOuter(e),Lt=(e,t)=>({left:e,top:t,translate:(o,n)=>Lt(e+o,t+n)}),Pt=Lt,Ut=(e,t)=>void 0!==e?e:void 0!==t?t:0,Wt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return Pt(o.offsetLeft,o.offsetTop);const s=Ut(null==n?void 0:n.pageYOffset,r.scrollTop),a=Ut(null==n?void 0:n.pageXOffset,r.scrollLeft),i=Ut(r.clientTop,o.clientTop),l=Ut(r.clientLeft,o.clientLeft);return jt(e).translate(a-l,s-i)},jt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?Pt(o.offsetLeft,o.offsetTop):pt(e)?(e=>{const t=e.getBoundingClientRect();return Pt(t.left,t.top)})(t):Pt(0,0)},Gt=Nt("width",(e=>e.dom.offsetWidth)),$t=e=>Gt.get(e),qt=e=>Gt.getOuter(e),Xt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Kt=()=>Yt(0,0),Yt=(e,t)=>({major:e,minor:t}),Jt={nu:Yt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Kt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Yt(n(1),n(2))})(e,o)},unknown:Kt},Zt=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},Qt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,eo=e=>t=>Oe(t,e),to=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Oe(e,"edge/")&&Oe(e,"chrome")&&Oe(e,"safari")&&Oe(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qt],search:e=>Oe(e,"chrome")&&!Oe(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Oe(e,"msie")||Oe(e,"trident")},{name:"Opera",versionRegexes:[Qt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:eo("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:eo("firefox")},{name:"Safari",versionRegexes:[Qt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Oe(e,"safari")||Oe(e,"mobile/"))&&Oe(e,"applewebkit")}],oo=[{name:"Windows",search:eo("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Oe(e,"iphone")||Oe(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:eo("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:eo("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:eo("linux"),versionRegexes:[]},{name:"Solaris",search:eo("sunos"),versionRegexes:[]},{name:"FreeBSD",search:eo("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:eo("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],no={browsers:y(to),oses:y(oo)},ro="Edge",so="Chromium",ao="Opera",io="Firefox",lo="Safari",co=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(ro),isChromium:n(so),isIE:n("IE"),isOpera:n(ao),isFirefox:n(io),isSafari:n(lo)}},uo=()=>co({current:void 0,version:Jt.unknown()}),mo=co,go=(y(ro),y(so),y("IE"),y(ao),y(io),y(lo),"Windows"),po="Android",ho="Linux",fo="macOS",bo="Solaris",vo="FreeBSD",yo="ChromeOS",xo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(go),isiOS:n("iOS"),isAndroid:n(po),isMacOS:n(fo),isLinux:n(ho),isSolaris:n(bo),isFreeBSD:n(vo),isChromeOS:n(yo)}},wo=()=>xo({current:void 0,version:Jt.unknown()}),So=xo,ko=(y(go),y("iOS"),y(po),y(ho),y(fo),y(bo),y(vo),y(yo),e=>window.matchMedia(e).matches);var Co;let Oo=Xt((()=>((e,t,o)=>{const n=no.browsers(),r=no.oses(),s=t.bind((e=>((e,t)=>se(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Jt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Zt(e,t).map((e=>{const o=Jt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(uo,mo),a=((e,t)=>Zt(e,t).map((e=>{const o=Jt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(wo,So),i=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=r||!s&&a&&n("(min-device-width:768px)"),c=s||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(r),isiPhone:y(s),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,s,e,o);return{browser:s,os:a,deviceType:i}})(navigator.userAgent,M.from(navigator.userAgentData),ko)));const _o=()=>Oo(),To=e=>{const t=Ie((e=>{if(ct()&&g(e.target)){const t=Ie(e.target);if(Pe(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return M.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=v(n,o);return((e,t,o,n,r,s,a)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:a}))(t,e.clientX,e.clientY,o,n,r,e)},Eo=(e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(To(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:S(Mo,e,t,s,r)}},Mo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Ao=(e,t)=>{et(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Do=(e,t)=>{const o=(e=>M.from(e.dom.nextSibling).map(Ie))(e);o.fold((()=>{et(e).each((e=>{Fo(e,t)}))}),(e=>{Ao(e,t)}))},Bo=(e,t)=>{const o=(e=>rt(e,0))(e);o.fold((()=>{Fo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Fo=(e,t)=>{e.dom.appendChild(t.dom)},Io=(e,t)=>{L(t,(t=>{Fo(e,t)}))},Ro=e=>{e.dom.textContent="",L(nt(e),(e=>{No(e)}))},No=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Vo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return Pt(o,n)},Ho=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},zo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Lo=e=>{const t=void 0===e?window:e,o=t.document,n=Vo(Ie(o));return(e=>{const t=void 0===e?window:e;return _o().browser.isFirefox()?M.none():M.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,r=e.clientHeight;return zo(n.left,n.top,o,r)}),(e=>zo(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Po=()=>Ie(document),Uo=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=Uo(e,o);return[t].concat(n)}));var Wo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?M.none():M.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Ie)},owner:e=>Ye(e)});const jo=e=>{const t=Po(),o=Vo(t),n=((e,t)=>{const o=t.owner(e),n=Uo(t,o);return M.some(n)})(e,Wo);return n.fold(S(Wt,e),(t=>{const n=jt(e),r=W(t,((e,t)=>{const o=jt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return Pt(r.left+n.left+o.left,r.top+n.top+o.top)}))},Go=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),$o=e=>{const t=Wt(e),o=qt(e),n=zt(e);return Go(t.left,t.top,o,n)},qo=e=>{const t=jo(e),o=qt(e),n=zt(e);return Go(t.left,t.top,o,n)},Xo=()=>Lo(window),Ko=e=>{const t=t=>t(e),o=y(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:T,isError:_,map:t=>Jo.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>M.some(e)};return r},Yo=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:_,isError:T,map:t,mapError:t=>Jo.error(t(e)),bind:t,exists:_,forall:T,getOr:x,or:x,getOrThunk:O,orThunk:O,getOrDie:C(String(e)),each:b,toOptional:M.none};return o},Jo={value:Ko,error:Yo,fromOption:(e,t)=>e.fold((()=>Yo(t)),Ko)};var Zo;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(Zo||(Zo={}));const Qo=(e,t,o)=>e.stype===Zo.Error?t(e.serror):o(e.svalue),en=e=>({stype:Zo.Value,svalue:e}),tn=e=>({stype:Zo.Error,serror:e}),on=en,nn=tn,rn=Qo,sn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),an=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},ln=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const r=t[n];for(const t in r)ve(r,t)&&(o[t]=e(o[t],r[t]))}return o},cn=ln(((e,t)=>i(e)&&i(t)?cn(e,t):t)),dn=ln(((e,t)=>t)),un=e=>({tag:"defaultedThunk",process:e}),mn=e=>un(y(e)),gn=e=>({tag:"mergeWithThunk",process:e}),pn=e=>{const t=(e=>{const t=[],o=[];return L(e,(e=>{Qo(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,v(nn,q)(o)):on(t.values);var o},hn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),fn=(e,t)=>nn([{path:e,getErrorInfo:t}]),bn=e=>({extract:(t,o)=>{return n=e(o),r=e=>((e,t)=>fn(e,y(t)))(t,e),n.stype===Zo.Error?r(n.serror):n;var n,r},toString:y("val")}),vn=bn(on),yn=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),xn=(e,t,o,n,r)=>{const s=e=>r.extract(t.concat([n]),e),a=e=>e.fold((()=>on(M.none())),(e=>{const o=r.extract(t.concat([n]),e);return s=o,a=M.some,s.stype===Zo.Value?{stype:Zo.Value,svalue:a(s.svalue)}:s;var s,a}));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>fn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+hn(o))))(e,o,t)),n))(t,o,n,s);case"defaultedThunk":return yn(o,n,e.process,s);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return yn(o,n,y({}),(t=>{const n=cn(e.process(o),t);return s(n)}))}},wn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Sn=e=>ae(ge(e,g)),kn=e=>{const t=Cn(e),o=W(e,((e,t)=>an(t,(t=>cn(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const r=d(n)?[]:Sn(n),s=U(r,(e=>!ye(o,e)));return 0===s.length?t.extract(e,n):((e,t)=>fn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,s)},toString:t.toString}},Cn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},r=[];for(const s of o)an(s,((o,s,a,i)=>{const l=xn(a,e,t,o,i);rn(l,(e=>{r.push(...e)}),(e=>{n[s]=e}))}),((e,o)=>{n[e]=o(t)}));return r.length>0?nn(r):on(n)})(t,o,e),toString:()=>{const t=z(e,(e=>an(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),On=e=>({extract:(t,o)=>{const n=z(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return pn(n)},toString:()=>"array("+e.toString()+")"}),_n=(e,t)=>{const o=void 0!==t?t:x;return{extract:(t,n)=>{const r=[];for(const s of e){const e=s.extract(t,n);if(e.stype===Zo.Value)return{stype:Zo.Value,svalue:o(e.svalue)};r.push(e)}return pn(r)},toString:()=>"oneOf("+z(e,(e=>e.toString())).join(", ")+")"}},Tn=(e,t)=>({extract:(o,n)=>{const r=ae(n),s=((t,o)=>On(bn(e)).extract(t,o))(o,r);return i=e=>{const r=z(e,(e=>sn(e,e,{tag:"required",process:{}},t)));return Cn(r).extract(o,n)},(a=s).stype===Zo.Value?i(a.svalue):a;var a,i},toString:()=>"setOf("+t.toString()+")"}),En=v(On,Cn),Mn=y(vn),An=(e,t)=>bn((o=>{const n=typeof o;return e(o)?on(o):nn(`Expected type: ${t} but got: ${n}`)})),Dn=An(h,"number"),Bn=An(s,"string"),Fn=An(d,"boolean"),In=An(p,"function"),Rn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Rn(e[t])));default:return!1}},Nn=bn((e=>Rn(e)?on(e):nn("Expected value to be acceptable for sending via postMessage"))),Vn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>fn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>fn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+hn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Hn=e=>bn((t=>e(t).fold(nn,on))),zn=(e,t)=>Tn((t=>e(t).fold(tn,en)),t),Ln=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===Zo.Error?{stype:Zo.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Qo(n,Jo.error,Jo.value);var n},Pn=e=>e.fold((e=>{throw new Error(Wn(e))}),x),Un=(e,t,o)=>Pn(Ln(e,t,o)),Wn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return z(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+hn(e.input),jn=(e,t)=>Vn(e,ce(t,Cn)),Gn=sn,$n=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),qn=e=>Hn((t=>R(e,t)?Jo.value(t):Jo.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),Xn=e=>Gn(e,e,{tag:"required",process:{}},Mn()),Kn=(e,t)=>Gn(e,e,{tag:"required",process:{}},t),Yn=e=>Kn(e,Dn),Jn=e=>Kn(e,Bn),Zn=(e,t)=>Gn(e,e,{tag:"required",process:{}},qn(t)),Qn=e=>Kn(e,In),er=(e,t)=>Gn(e,e,{tag:"required",process:{}},Cn(t)),tr=(e,t)=>Gn(e,e,{tag:"required",process:{}},En(t)),or=(e,t)=>Gn(e,e,{tag:"required",process:{}},On(t)),nr=e=>Gn(e,e,{tag:"option",process:{}},Mn()),rr=(e,t)=>Gn(e,e,{tag:"option",process:{}},t),sr=e=>rr(e,Dn),ar=e=>rr(e,Bn),ir=(e,t)=>rr(e,qn(t)),lr=e=>rr(e,In),cr=(e,t)=>rr(e,On(t)),dr=(e,t)=>rr(e,Cn(t)),ur=(e,t)=>Gn(e,e,mn(t),Mn()),mr=(e,t,o)=>Gn(e,e,mn(t),o),gr=(e,t)=>mr(e,t,Dn),pr=(e,t)=>mr(e,t,Bn),hr=(e,t,o)=>mr(e,t,qn(o)),fr=(e,t)=>mr(e,t,Fn),br=(e,t)=>mr(e,t,In),vr=(e,t,o)=>mr(e,t,On(o)),yr=(e,t,o)=>mr(e,t,Cn(o)),xr=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},wr=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return L(e,((n,r)=>{const s=ae(n);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!K(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};wr([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Sr=(e,t)=>((e,t)=>({[e]:t}))(e,t),kr=e=>(e=>{const t={};return L(e,(e=>{t[e.key]=e.value})),t})(e),Cr=e=>p(e)?e:_,Or=(e,t,o)=>{let n=e.dom;const r=Cr(o);for(;n.parentNode;){n=n.parentNode;const e=Ie(n),o=t(e);if(o.isSome())return o;if(r(e))break}return M.none()},_r=(e,t,o)=>{const n=t(e),r=Cr(o);return n.orThunk((()=>r(e)?M.none():Or(e,t,r)))},Tr=(e,t)=>Xe(e.element,t.event.target),Er={can:T,abort:_,run:b},Mr=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Er,...e}},Ar=y,Dr=Ar("touchstart"),Br=Ar("touchmove"),Fr=Ar("touchend"),Ir=Ar("touchcancel"),Rr=Ar("mousedown"),Nr=Ar("mousemove"),Vr=Ar("mouseout"),Hr=Ar("mouseup"),zr=Ar("mouseover"),Lr=Ar("focusin"),Pr=Ar("focusout"),Ur=Ar("keydown"),Wr=Ar("keyup"),jr=Ar("input"),Gr=Ar("change"),$r=Ar("click"),qr=Ar("transitioncancel"),Xr=Ar("transitionend"),Kr=Ar("transitionstart"),Yr=Ar("selectstart"),Jr=e=>y("alloy."+e),Zr={tap:Jr("tap")},Qr=Jr("focus"),es=Jr("blur.post"),ts=Jr("paste.post"),os=Jr("receive"),ns=Jr("execute"),rs=Jr("focus.item"),ss=Zr.tap,as=Jr("longpress"),is=Jr("sandbox.close"),ls=Jr("typeahead.cancel"),cs=Jr("system.init"),ds=Jr("system.touchmove"),us=Jr("system.touchend"),ms=Jr("system.scroll"),gs=Jr("system.resize"),ps=Jr("system.attached"),hs=Jr("system.detached"),fs=Jr("system.dismissRequested"),bs=Jr("system.repositionRequested"),vs=Jr("focusmanager.shifted"),ys=Jr("slotcontainer.visibility"),xs=Jr("change.tab"),ws=Jr("dismiss.tab"),Ss=Jr("highlight"),ks=Jr("dehighlight"),Cs=(e,t)=>{Es(e,e.element,t,{})},Os=(e,t,o)=>{Es(e,e.element,t,o)},_s=e=>{Cs(e,ns())},Ts=(e,t,o)=>{Es(e,t,o,{})},Es=(e,t,o,n)=>{const r={target:t,...n};e.getSystem().triggerEvent(o,t,r)},Ms=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},As=e=>kr(e),Ds=(e,t)=>({key:e,value:Mr({abort:t})}),Bs=e=>({key:e,value:Mr({run:(e,t)=>{t.event.prevent()}})}),Fs=(e,t)=>({key:e,value:Mr({run:t})}),Is=(e,t,o)=>({key:e,value:Mr({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Rs=e=>t=>({key:e,value:Mr({run:(e,o)=>{Tr(e,o)&&t(e,o)}})}),Ns=(e,t,o)=>((e,t)=>Fs(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Ms(t,t.element,e,n)}))})))(e,t.partUids[o]),Vs=(e,t)=>Fs(e,((e,o)=>{const n=o.event,r=e.getSystem().getByDom(n.target).getOrThunk((()=>_r(n.target,(t=>e.getSystem().getByDom(t).toOptional()),_).getOr(e)));t(e,r,o)})),Hs=e=>Fs(e,((e,t)=>{t.cut()})),zs=e=>Fs(e,((e,t)=>{t.stop()})),Ls=(e,t)=>Rs(e)(t),Ps=Rs(ps()),Us=Rs(hs()),Ws=Rs(cs()),js=(Ys=ns(),e=>Fs(Ys,e)),Gs=e=>e.dom.innerHTML,$s=(e,t)=>{const o=Ye(e).dom,n=Ie(o.createDocumentFragment()),r=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,nt(Ie(o))})(t,o);Io(n,r),Ro(e),Fo(e,n)},qs=e=>it(e)?"#shadow-root":(e=>{const t=Be("div"),o=Ie(e.dom.cloneNode(!0));return Fo(t,o),Gs(t)})((e=>((e,t)=>Ie(e.dom.cloneNode(!1)))(e))(e)),Xs=e=>qs(e),Ks=As([((e,t)=>({key:e,value:Mr({can:(e,t)=>{const o=t.event,n=o.originator,r=o.target;return!((e,t,o)=>Xe(t,e.element)&&!Xe(t,o))(e,n,r)||(console.warn(Qr()+" did not get interpreted by the desired target. \nOriginator: "+Xs(n)+"\nTarget: "+Xs(r)+"\nCheck the "+Qr()+" event handlers"),!1)}})}))(Qr())]);var Ys,Js=Object.freeze({__proto__:null,events:Ks});let Zs=0;const Qs=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return Zs++,e+"_"+o+Zs+String(t)},ea=y("alloy-id-"),ta=y("data-alloy-id"),oa=ea(),na=ta(),ra=(e,t)=>{Object.defineProperty(e.dom,na,{value:t,writable:!0})},sa=e=>{const t=Pe(e)?e.dom[na]:null;return M.from(t)},aa=e=>Qs(e),ia=x,la=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+Xs(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:_}},ca=la(),da=e=>z(e,(e=>_e(e,"/*")?e.substring(0,e.length-"/*".length):e)),ua=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,r=o.indexOf("("),s=o.substring(r+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:da(s)}),e},ma=Qs("alloy-premade"),ga=e=>(Object.defineProperty(e.element.dom,ma,{value:e.uid,writable:!0}),Sr(ma,e)),pa=e=>be(e,ma),ha=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,r=o.indexOf("("),s=o.substring(r+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:da(s.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),fa={init:()=>ba({readState:y("No State required")})},ba=e=>e,va=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,r)=>{const s=be(o,r).getOr([]);o[r]=s.concat([t(n,e)])}))})),o},ya=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),xa=e=>e.cHandler,wa=(e,t)=>({name:e,handler:t}),Sa=(e,t)=>{const o={};return L(e,(e=>{o[e.name()]=e.handlers(t)})),o},ka=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const t=ee(o,((t,o)=>{const r=t.name,s=o.name,a=n.indexOf(r),i=n.indexOf(s);if(-1===a)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+s+".\nOrder specified: "+JSON.stringify(n,null,2));return a<i?-1:i<a?1:0}));return Jo.value(t)}catch(e){return Jo.error([e])}})("Event: "+o,0,e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{L(e,(e=>{e.run.apply(void 0,t)}))}}})(z(e,(e=>e.handler))))):((e,t)=>Jo.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(z(t,(e=>e.name)),null,2)]))(o,e)},Ca=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return L(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,Jo.error(q(n))):((e,t)=>0===e.length?Jo.value(t):Jo.value(cn(t,dn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?Jo.value(e[0].handler):ka(e,t,o)).map((n=>{const r=(e=>{const t=(e=>p(e)?{can:T,abort:_,run:e}:e)(e);return(e,o,...n)=>{const r=[e,o].concat(n);t.abort.apply(void 0,r)?o.stop():t.can.apply(void 0,r)&&t.run.apply(void 0,r)}})(n),s=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Sr(o,((e,t)=>({handler:e,purpose:t}))(r,s))})))),{}),Oa="alloy.base.behaviour",_a=Cn([Gn("dom","dom",{tag:"required",process:{}},Cn([Xn("tag"),ur("styles",{}),ur("classes",[]),ur("attributes",{}),nr("value"),nr("innerHtml")])),Xn("components"),Xn("uid"),ur("events",{}),ur("apis",{}),Gn("eventOrder","eventOrder",(Ja={[ns()]:["disabling",Oa,"toggling","typeaheadevents"],[Qr()]:[Oa,"focusing","keying"],[cs()]:[Oa,"disabling","toggling","representing"],[jr()]:[Oa,"representing","streaming","invalidating"],[hs()]:[Oa,"representing","item-events","tooltipping"],[Rr()]:["focusing",Oa,"item-type-events"],[Dr()]:["focusing",Oa,"item-type-events"],[zr()]:["item-type-events","tooltipping"],[os()]:["receiving","reflecting","tooltipping"]},gn(y(Ja))),Mn()),nr("domModification")]),Ta=e=>e.events,Ea=(e,t)=>{const o=xt(e,t);return void 0===o||""===o?[]:o.split(" ")},Ma=e=>void 0!==e.dom.classList,Aa=e=>Ea(e,"class"),Da=(e,t)=>{Ma(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Ea(e,t).concat([o]);vt(e,t,n.join(" "))})(e,"class",t)})(e,t)},Ba=(e,t)=>{Ma(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=U(Ea(e,t),(e=>e!==o));n.length>0?vt(e,t,n.join(" ")):kt(e,t)})(e,"class",t)})(e,t),(e=>{0===(Ma(e)?e.dom.classList:Aa(e)).length&&kt(e,"class")})(e)},Fa=(e,t)=>Ma(e)&&e.dom.classList.contains(t),Ia=(e,t)=>{L(t,(t=>{Da(e,t)}))},Ra=(e,t)=>{L(t,(t=>{Ba(e,t)}))},Na=e=>e.dom.value,Va=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Ha=(e,t,o)=>{o.fold((()=>Fo(e,t)),(e=>{Xe(e,t)||(Ao(e,t),No(e))}))},za=(e,t,o)=>{const n=z(t,o),r=nt(e);return L(r.slice(n.length),No),n},La=(e,t,o,n)=>{const r=rt(e,t),s=n(o,r),a=((e,t,o)=>rt(e,t).map((e=>{if(o.exists((t=>!Xe(t,e)))){const t=o.map(ze).getOr("span"),n=Be(t);return Ao(e,n),n}return e})))(e,t,r);return Ha(e,s.element,a),s},Pa=(e,t)=>{const o=ae(e),n=ae(t);return{toRemove:J(n,o),toSet:((e,o)=>{const n={},r={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(r)),{t:n,f:r}})(e).t}},Ua=(e,t)=>{const{class:o,style:n,...r}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:s,toRemove:a}=Pa(e.attributes,r),i=Bt(t),{toSet:l,toRemove:c}=Pa(e.styles,i),d=(e=>Ma(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Aa(e))(t),u=J(d,e.classes),m=J(e.classes,d);return L(a,(e=>kt(t,e))),yt(t,s),Ia(t,m),Ra(t,u),L(c,(e=>It(t,e))),Tt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{za(e,t,((t,o)=>{const n=rt(e,o);return Ha(e,t,n),t}))})(t,o)}),(e=>{$s(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==Na(o)&&Va(o,null!=n?n:"")})(),t},Wa=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=z(t,(e=>dr(e.name(),[Xn("config"),ur("state",fa)]))),n=Ln("component.behaviours",Cn(o),e.behaviours).fold((t=>{throw new Error(Wn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),x);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},ja=(e,t)=>{const o=()=>m,n=xr(ca),r=Pn((e=>Ln("custom.definition",_a,e))(e)),s=Wa(e),a=(e=>e.list)(s),i=(e=>e.data)(s),l=((e,t,o)=>{const n={...(r=e).dom,uid:r.uid,domChildren:z(r.components,(e=>e.element))};var r;const s=(e=>e.domModification.fold((()=>ya({})),ya))(e),a={"alloy.base.modification":s},i=t.length>0?((e,t,o,n)=>{const r={...t};L(o,(t=>{r[t.name()]=t.exhibit(e,n)}));const s=va(r,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(s.classes,((e,t)=>t.modification.concat(e)),[]),l=a(s.attributes),c=a(s.styles);return ya({classes:i,attributes:l,styles:c})})(o,a,t,n):s;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(r,a,i),c=((e,t)=>{const o=t.filter((t=>ze(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,ma))(t))).bind((t=>((e,t)=>{try{const o=Ua(e,t);return M.some(o)}catch(e){return M.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Be(e.tag);yt(t,e.attributes),Ia(t,e.classes),Tt(t,e.styles),e.innerHtml.each((e=>$s(t,e)));const o=e.domChildren;return Io(t,o),e.value.each((e=>{Va(t,e)})),t})(e)));return ra(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Ta(e)};return((e,t,o,n)=>{const r=((e,t,o)=>{const n={...o,...Sa(t,e)};return va(n,wa)})(e,o,n);return Ca(r,t)})(o,e.eventOrder,t,n).getOrDie()})(r,a,i),u=xr(r.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>r.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(la(o))},element:c,syncComponents:()=>{const e=nt(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},Ga=e=>{const t=Fe(e);return $a({element:t})},$a=e=>{const t=Un("external.component",kn([Xn("element"),nr("uid")]),e),o=xr(la()),n=t.uid.getOrThunk((()=>aa("external")));ra(t.element,n);const r={uid:n,getSystem:o.get,config:M.none,hasConfigured:_,connect:e=>{o.set(e)},disconnect:()=>{o.set(la((()=>r)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return ga(r)},qa=aa,Xa=(e,t)=>pa(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=ia(e),r=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>z(o,Ka)),(e=>z(o,((t,o)=>Xa(t,rt(e,o))))))})(n,t),s={...n,events:{...Js,...o},components:r};return Jo.value(ja(s,t))})((e=>ve(e,"uid"))(e)?e:{uid:qa(""),...e},t).getOrDie())),Ka=e=>Xa(e,M.none()),Ya=ga;var Ja,Za=(e,t,o,n,r)=>e(o,n)?M.some(o):p(r)&&r(o)?M.none():t(o,n,r);const Qa=(e,t,o)=>{let n=e.dom;const r=p(o)?o:_;for(;n.parentNode;){n=n.parentNode;const e=Ie(n);if(t(e))return M.some(e);if(r(e))break}return M.none()},ei=(e,t,o)=>Za(((e,t)=>t(e)),Qa,e,t,o),ti=(e,t,o)=>ei(e,t,o).isSome(),oi=(e,t,o)=>Qa(e,(e=>$e(e,t)),o),ni=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Ie(e),$e(o,t);var o})).map(Ie))(e),ri=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return qe(o)?M.none():M.from(o.querySelector(e)).map(Ie)})(t,e),si=(e,t,o)=>Za(((e,t)=>$e(e,t)),oi,e,t,o),ai="aria-controls",ii=()=>{const e=Qs(ai);return{id:e,link:t=>{vt(t,ai,e)},unlink:e=>{kt(e,ai)}}},li=(e,t)=>ti(t,(t=>Xe(t,e.element)),_)||((e,t)=>(e=>ei(e,(e=>{if(!Pe(e))return!1;const t=xt(e,"id");return void 0!==t&&t.indexOf(ai)>-1})).bind((e=>{const t=xt(e,"id"),o=dt(e);return ri(o,`[${ai}="${t}"]`)})))(t).exists((t=>li(e,t))))(e,t);var ci;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ci||(ci={}));const di=xr({}),ui=["alloy/data/Fields","alloy/debugging/Debugging"],mi=(e,t,o)=>((e,t,o)=>{switch(be(di.get(),e).orThunk((()=>{const t=ae(di.get());return se(t,(t=>e.indexOf(t)>-1?M.some(di.get()[t]):M.none()))})).getOr(ci.NORMAL)){case ci.NORMAL:return o(gi());case ci.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const r=(new Date).getTime();R(["mousemove","mouseover","mouseout",cs()],e)||console.log(e,{event:e,time:r-n,target:t.dom,sequence:z(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+Xs(e.target)+")":e.outcome))})}}})(e,t),r=o(n);return n.write(),r}case ci.STOP:return!0}})(e,t,o),gi=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),pi=y([Xn("menu"),Xn("selectedMenu")]),hi=y([Xn("item"),Xn("selectedItem")]);y(Cn(hi().concat(pi())));const fi=y(Cn(hi())),bi=er("initSize",[Xn("numColumns"),Xn("numRows")]),vi=()=>er("markers",[Xn("backgroundMenu")].concat(pi()).concat(hi())),yi=e=>er("markers",z(e,Xn)),xi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(ui,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Gn(t,t,o,Hn((e=>Jo.value(((...t)=>e.apply(void 0,t))))))),wi=e=>xi(0,e,mn(b)),Si=e=>xi(0,e,mn(M.none)),ki=e=>xi(0,e,{tag:"required",process:{}}),Ci=e=>xi(0,e,{tag:"required",process:{}}),Oi=(e,t)=>$n(e,y(t)),_i=e=>$n(e,x),Ti=y(bi),Ei=(e,t,o,n,r,s,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:r,restriction:s,label:`${a}-${r}`,alwaysFit:i}),Mi=wr([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ai=Mi.southeast,Di=Mi.southwest,Bi=Mi.northeast,Fi=Mi.northwest,Ii=Mi.south,Ri=Mi.north,Ni=Mi.east,Vi=Mi.west,Hi=(e,t,o,n)=>{const r=e+t;return r>n?o:r<o?n:r},zi=(e,t,o)=>Math.min(Math.max(e,t),o),Li=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Pi="layout",Ui=e=>e.x,Wi=(e,t)=>e.x+e.width/2-t.width/2,ji=(e,t)=>e.x+e.width-t.width,Gi=(e,t)=>e.y-t.height,$i=e=>e.y+e.height,qi=(e,t)=>e.y+e.height/2-t.height/2,Xi=(e,t,o)=>Ei(Ui(e),$i(e),o.southeast(),Ai(),"southeast",Li(e,{left:1,top:3}),Pi),Ki=(e,t,o)=>Ei(ji(e,t),$i(e),o.southwest(),Di(),"southwest",Li(e,{right:0,top:3}),Pi),Yi=(e,t,o)=>Ei(Ui(e),Gi(e,t),o.northeast(),Bi(),"northeast",Li(e,{left:1,bottom:2}),Pi),Ji=(e,t,o)=>Ei(ji(e,t),Gi(e,t),o.northwest(),Fi(),"northwest",Li(e,{right:0,bottom:2}),Pi),Zi=(e,t,o)=>Ei(Wi(e,t),Gi(e,t),o.north(),Ri(),"north",Li(e,{bottom:2}),Pi),Qi=(e,t,o)=>Ei(Wi(e,t),$i(e),o.south(),Ii(),"south",Li(e,{top:3}),Pi),el=(e,t,o)=>Ei((e=>e.x+e.width)(e),qi(e,t),o.east(),Ni(),"east",Li(e,{left:0}),Pi),tl=(e,t,o)=>Ei(((e,t)=>e.x-t.width)(e,t),qi(e,t),o.west(),Vi(),"west",Li(e,{right:1}),Pi),ol=()=>[Xi,Ki,Yi,Ji,Qi,Zi,el,tl],nl=()=>[Ki,Xi,Ji,Yi,Qi,Zi,el,tl],rl=()=>[Yi,Ji,Xi,Ki,Zi,Qi],sl=()=>[Ji,Yi,Ki,Xi,Zi,Qi],al=()=>[Xi,Ki,Yi,Ji,Qi,Zi],il=()=>[Ki,Xi,Ji,Yi,Qi,Zi];var ll=Object.freeze({__proto__:null,events:e=>As([Fs(os(),((t,o)=>{const n=e.channels,r=ae(n),s=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(r,s);L(a,(e=>{const o=n[e],r=o.schema,a=Un("channel["+e+"] data\nReceiver: "+Xs(t.element),r,s.data);o.onReceive(t,a)}))}))])}),cl=[Kn("channels",zn(Jo.value,kn([ki("onReceive"),ur("schema",Mn())])))];const dl=(e,t,o)=>Ws(((n,r)=>{o(n,e,t)})),ul=e=>({key:e,value:void 0}),ml=(e,t,o,n,r,s,a)=>{const i=e=>ye(e,o)?e[o]():M.none(),l=ce(r,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),r=n.indexOf(")")+1,s=n.indexOf("("),a=n.substring(s+1,r-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:da(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...r)=>{const s=[n].concat(r);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(s,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(s,((e,t)=>ua(e,t))),...l,revoke:S(ul,o),config:t=>{const n=Un(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Xt((()=>Un(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>ya({}))),name:y(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},gl=e=>kr(e),pl=kn([Xn("fields"),Xn("name"),ur("active",{}),ur("apis",{}),ur("state",fa),ur("extra",{})]),hl=e=>{const t=Un("Creating behaviour: "+e.name,pl,e);return((e,t,o,n,r,s)=>{const a=kn(e),i=dr(t,[("config",l=e,rr("config",kn(l)))]);var l;return ml(a,i,t,o,n,r,s)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},fl=kn([Xn("branchKey"),Xn("branches"),Xn("name"),ur("active",{}),ur("apis",{}),ur("state",fa),ur("extra",{})]),bl=e=>{const t=Un("Creating behaviour: "+e.name,fl,e);return((e,t,o,n,r,s)=>{const a=e,i=dr(t,[rr("config",e)]);return ml(a,i,t,o,n,r,s)})(jn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},vl=y(void 0),yl=hl({fields:cl,name:"receiving",active:ll});var xl=Object.freeze({__proto__:null,exhibit:(e,t)=>ya({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const wl=e=>e.dom.focus(),Sl=e=>{const t=dt(e).dom;return e.dom===t.activeElement},kl=(e=Po())=>M.from(e.dom.activeElement).map(Ie),Cl=e=>kl(dt(e)).filter((t=>e.dom.contains(t.dom))),Ol=(e,t)=>{const o=dt(t),n=kl(o).bind((e=>{const o=t=>Xe(e,t);return o(t)?M.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=Ie(e.childNodes[n]);if(t(r))return M.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return M.none()};return o(e.dom)})(t,o)})),r=e(t);return n.each((e=>{kl(o).filter((t=>Xe(t,e))).fold((()=>{wl(e)}),b)})),r},_l=(e,t,o,n,r)=>{const s=e=>e+"px";return{position:e,left:t.map(s),top:o.map(s),right:n.map(s),bottom:r.map(s)}},Tl=(e,t)=>{Et(e,(e=>({...e,position:M.some(e.position)}))(t))},El=wr([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Ml=(e,t,o,n,r,s)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=r-(i+a.width),d=s-(l+a.height),u=M.some(i),m=M.some(l),g=M.some(c),p=M.some(d),h=M.none();return t.direction.fold((()=>_l(e,u,m,h,h)),(()=>_l(e,h,m,g,h)),(()=>_l(e,u,h,h,p)),(()=>_l(e,h,h,g,p)),(()=>_l(e,u,m,h,h)),(()=>_l(e,u,h,h,p)),(()=>_l(e,u,m,h,h)),(()=>_l(e,h,m,g,h)))},Al=(e,t)=>e.fold((()=>{const e=t.rect;return _l("absolute",M.some(e.x),M.some(e.y),M.none(),M.none())}),((e,o,n,r)=>Ml("absolute",t,e,o,n,r)),((e,o,n,r)=>Ml("fixed",t,e,o,n,r))),Dl=(e,t)=>{const o=S(jo,t),n=e.fold(o,o,(()=>{const e=Vo();return jo(t).translate(-e.left,-e.top)})),r=qt(t),s=zt(t);return Go(n.left,n.top,r,s)},Bl=(e,t)=>t.fold((()=>e.fold(Xo,Xo,Go)),(t=>e.fold(t,t,(()=>{const o=t(),n=Fl(e,o.x,o.y);return Go(n.left,n.top,o.width,o.height)})))),Fl=(e,t,o)=>{const n=Pt(t,o);return e.fold(y(n),y(n),(()=>{const e=Vo();return n.translate(-e.left,-e.top)}))};El.none;const Il=El.relative,Rl=El.fixed,Nl="data-alloy-placement",Vl=e=>wt(e,Nl),Hl=wr([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),zl=(e,t,o,n)=>{const r=e.bubble,s=r.offset,a=((e,t,o)=>{const n=(n,r)=>t[n].map((t=>{const s="top"===n||"bottom"===n,a=s?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,r)+a;return s?zi(i,e.y,e.bottom):zi(i,e.x,e.right)})).getOr(r),r=n("left",e.x),s=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Go(r,s,a-r,i-s)})(n,e.restriction,s),i=e.x+s.left,l=e.y+s.top,c=Go(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:r,bottom:s}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=r&&i>=n&&i<=s,sizeInBounds:l<=r&&l>=o&&c<=s&&c>=n,visibleW:Math.min(d,a>=o?r-a:l-o),visibleH:Math.min(u,i>=n?s-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:r,bottom:s}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,r-l),u=Math.max(n,s-c),m=zi(a,o,d),g=zi(i,n,u),p=Math.min(m+l,r)-m,h=Math.min(g+c,s)-g;return Go(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),r=y(o.bottom-t.y),s=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,r,r,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:s}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:r.classesOn,off:r.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Hl.fit(x):Hl.nofit(x,m,g,f)},Ll=e=>{const t=xr(M.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(M.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(M.some(e))}}},Pl=()=>Ll((e=>e.unbind())),Ul=()=>{const e=Ll(b);return{...e,on:t=>e.get().each(t)}},Wl=T,jl=(e,t,o)=>((e,t,o,n)=>Eo(e,t,o,n,!1))(e,t,Wl,o),Gl=(e,t,o)=>((e,t,o,n)=>Eo(e,t,o,n,!0))(e,t,Wl,o),$l=To,ql=["top","bottom","right","left"],Xl="data-alloy-transition-timer",Kl=(e,t,o,n,r,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,r,a);if(i||((e,t)=>((e,t)=>K(t,(t=>Fa(e,t))))(e,t.classes))(e,n)){_t(e,"position",o.position);const a=Dl(t,e),l=Al(t,{...r,rect:a}),c=Z(ql,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=w)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Et(e,c),i&&((e,t)=>{Ia(e,t.classes),wt(e,Xl).each((t=>{clearTimeout(parseInt(t,10)),kt(e,Xl)})),((e,t)=>{const o=Pl(),n=Pl();let r;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Xe(t.target,e)&&!Ee(n)&&R(ql,t.raw.propertyName)},i=s=>{if(m(s)||a(s)){o.clear(),n.clear();const a=null==s?void 0:s.raw.type;(m(a)||a===Xr())&&(clearTimeout(r),kt(e,Xl),Ra(e,t.classes))}},l=jl(e,Kr(),(t=>{a(t)&&(l.unbind(),o.set(jl(e,Xr(),i)),n.set(jl(e,qr(),i)))})),c=(e=>{const t=t=>{const o=Mt(e,t).split(/\s*,\s*/);return U(o,Ee)},o=e=>{if(s(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return _e(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),r=t("transition-duration");return j(r,((e,t,r)=>{const s=o(n[r])+o(t);return Math.max(e,s)}),0)})(e);requestAnimationFrame((()=>{r=setTimeout(i,c+17),vt(e,Xl,r)}))})(e,t)})(e,n),Rt(e))}else Ra(e,n.classes)},Yl=(e,t)=>{((e,t)=>{const o=Vt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);_t(e,"max-height",o+"px")})(e,Math.floor(t))},Jl=y(((e,t)=>{Yl(e,t),Tt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),Zl=y(((e,t)=>{Yl(e,t)})),Ql=(e,t,o)=>void 0===e[t]?o:e[t],ec=(e,t,o,n)=>{const r=((e,t,o,n)=>{It(t,"max-height"),It(t,"max-width");const r={width:qt(s=t),height:zt(s)};var s;return((e,t,o,n,r,s)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,r,e,s),g=zl(m,a,i,s);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Hl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=S(l,t);return e.fold(y(e),o)}),Hl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Ai(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(x,x)})(t,n.preference,e,r,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Al(o.origin,t);o.transition.each((r=>{Kl(e,o.origin,n,r,t,o.lastPlacement)})),Tl(e,n)})(t,r,n),((e,t)=>{((e,t)=>{vt(e,Nl,t)})(e,t.placement)})(t,r),((e,t)=>{const o=t.classes;Ra(e,o.off),Ia(e,o.on)})(t,r),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,r,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,r,n),{layout:r.layout,placement:r.placement}},tc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],oc=(e,t,o,n=1)=>{const r=e*n,s=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(tc,o);return{offset:Pt(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(r,s,["top","alignLeft","inset"]),insetNorthwest:()=>i(-r,s,["top","alignRight","inset"]),insetNorth:()=>i(-r/2,s,["top","alignCentre","inset"]),insetSoutheast:()=>i(r,-s,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-r,-s,["bottom","alignRight","inset"]),insetSouth:()=>i(-r/2,-s,["bottom","alignCentre","inset"]),insetEast:()=>i(-r,-s/2,["valignCentre","right","inset"]),insetWest:()=>i(r,-s/2,["valignCentre","left","inset"])}},nc=()=>oc(0,0,{}),rc=x,sc=(e,t)=>o=>"rtl"===ac(o)?t:e,ac=e=>"rtl"===Mt(e,"direction")?"rtl":"ltr";var ic;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(ic||(ic={}));const lc="data-alloy-vertical-dir",cc=e=>ti(e,(e=>Pe(e)&&xt(e,"data-alloy-vertical-dir")===ic.BottomToTop)),dc=()=>dr("layouts",[Xn("onLtr"),Xn("onRtl"),nr("onBottomLtr"),nr("onBottomRtl")]),uc=(e,t,o,n,r,s,a)=>{const i=a.map(cc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(r):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(s):c.getOr(n);return sc(d,u)(e)};var mc=[Xn("hotspot"),nr("bubble"),ur("overrides",{}),dc(),Oi("placement",((e,t,o)=>{const n=t.hotspot,r=Dl(o,n.element),s=uc(e.element,t,al(),il(),rl(),sl(),M.some(t.hotspot.element));return M.some(rc({anchorBox:r,bubble:t.bubble.getOr(nc()),overrides:t.overrides,layouts:s,placer:M.none()}))}))],gc=[Xn("x"),Xn("y"),ur("height",0),ur("width",0),ur("bubble",nc()),ur("overrides",{}),dc(),Oi("placement",((e,t,o)=>{const n=Fl(o,t.x,t.y),r=Go(n.left,n.top,t.width,t.height),s=uc(e.element,t,ol(),nl(),ol(),nl(),M.none());return M.some(rc({anchorBox:r,bubble:t.bubble,overrides:t.overrides,layouts:s,placer:M.none()}))}))];const pc=wr([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),hc=e=>e.fold(x,((e,t,o)=>e.translate(-t,-o))),fc=e=>e.fold(x,x),bc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),Pt(0,0)),vc=e=>{const t=z(e,fc);return bc(t)},yc=pc.screen,xc=pc.absolute,wc=(e,t,o)=>{const n=Ye(e.element),r=Vo(n),s=((e,t,o)=>{const n=Qe(o.root).dom;return M.from(n.frameElement).map(Ie).filter((t=>{const o=Ye(t),n=Ye(e.element);return Xe(o,n)})).map(Wt)})(e,0,o).getOr(r);return xc(s,r.left,r.top)},Sc=(e,t,o,n)=>{const r=yc(Pt(e,t));return M.some(((e,t,o)=>({point:e,width:t,height:o}))(r,o,n))},kc=(e,t,o,n,r)=>e.map((e=>{const s=[t,e.point],a=(i=()=>vc(s),l=()=>vc(s),c=()=>(e=>{const t=z(e,hc);return bc(t)})(s),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?rl():al(),m=o.showAbove?sl():il(),g=uc(r,o,u,m,u,m,M.none());var p,h,f,b;return rc({anchorBox:d,bubble:o.bubble.getOr(nc()),overrides:o.overrides,layouts:g,placer:M.none()})}));var Cc=[Xn("node"),Xn("root"),nr("bubble"),dc(),ur("overrides",{}),ur("showAbove",!1),Oi("placement",((e,t,o)=>{const n=wc(e,0,t);return t.node.filter(pt).bind((r=>{const s=r.dom.getBoundingClientRect(),a=Sc(s.left,s.top,s.width,s.height),i=t.node.getOr(e.element);return kc(a,n,t,o,i)}))}))];const Oc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),_c=wr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Tc=(_c.before,_c.on,_c.after,e=>e.fold(x,x,x)),Ec=wr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Mc={domRange:Ec.domRange,relative:Ec.relative,exact:Ec.exact,exactFromRange:e=>Ec.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Ie(e.startContainer),relative:(e,t)=>Tc(e),exact:(e,t,o,n)=>e}))(e);return Qe(t)},range:Oc},Ac=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Dc=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},Bc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Fc=wr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ic=(e,t,o)=>t(Ie(o.startContainer),o.startOffset,Ie(o.endContainer),o.endOffset),Rc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:M.none}),relative:(t,o)=>({ltr:Xt((()=>Ac(e,t,o))),rtl:Xt((()=>M.some(Ac(e,o,t))))}),exact:(t,o,n,r)=>({ltr:Xt((()=>Dc(e,t,o,n,r))),rtl:Xt((()=>M.some(Dc(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Fc.rtl(Ie(e.endContainer),e.endOffset,Ie(e.startContainer),e.startOffset))).getOrThunk((()=>Ic(0,Fc.ltr,o))):Ic(0,Fc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});Fc.ltr,Fc.rtl;const Nc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return qe(o)?[]:z(o.querySelectorAll(e),Ie)})(t,e),Vc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return M.some(Oc(Ie(t.startContainer),t.startOffset,Ie(o.endContainer),o.endOffset))}return M.none()},Hc=e=>{if(null===e.anchorNode||null===e.focusNode)return Vc(e);{const t=Ie(e.anchorNode),o=Ie(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=Ye(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=Xe(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?M.some(Oc(t,e.anchorOffset,o,e.focusOffset)):Vc(e)}},zc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?M.some(o).map(Bc):M.none()})(Rc(e,t)),Lc=((e,t)=>{const o=t=>e(t)?M.from(t.dom.nodeValue):M.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(Ue),Pc=(e,t)=>({element:e,offset:t}),Uc=(e,t)=>Ue(e)?Pc(e,t):((e,t)=>{const o=nt(e);if(0===o.length)return Pc(e,t);if(t<o.length)return Pc(o[t],0);{const e=o[o.length-1],t=Ue(e)?(e=>Lc.get(e))(e).length:nt(e).length;return Pc(e,t)}})(e,t),Wc=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>M.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Hc))(e)))().map((e=>{const t=Uc(e.start,e.soffset),o=Uc(e.finish,e.foffset);return Mc.range(t.element,t.offset,o.element,o.offset)}));var jc=[nr("getSelection"),Xn("root"),nr("bubble"),dc(),ur("overrides",{}),ur("showAbove",!1),Oi("placement",((e,t,o)=>{const n=Qe(t.root).dom,r=wc(e,0,t),s=Wc(n,t).bind((e=>{const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?M.some(t).map(Bc):M.none()})(Rc(e,t)))(n,Mc.exactFromRange(e)).orThunk((()=>{const t=Fe("\ufeff");Ao(e.start,t);const o=zc(n,Mc.exact(t,0,t,1));return No(t),o}));return t.bind((e=>Sc(e.left,e.top,e.width,e.height)))})),a=Wc(n,t).bind((e=>Pe(e.start)?M.some(e.start):tt(e.start))).getOr(e.element);return kc(s,r,t,o,a)}))];const Gc="link-layout",$c=e=>e.x+e.width,qc=(e,t)=>e.x-t.width,Xc=(e,t)=>e.y-t.height+e.height,Kc=e=>e.y,Yc=(e,t,o)=>Ei($c(e),Kc(e),o.southeast(),Ai(),"southeast",Li(e,{left:0,top:2}),Gc),Jc=(e,t,o)=>Ei(qc(e,t),Kc(e),o.southwest(),Di(),"southwest",Li(e,{right:1,top:2}),Gc),Zc=(e,t,o)=>Ei($c(e),Xc(e,t),o.northeast(),Bi(),"northeast",Li(e,{left:0,bottom:3}),Gc),Qc=(e,t,o)=>Ei(qc(e,t),Xc(e,t),o.northwest(),Fi(),"northwest",Li(e,{right:1,bottom:3}),Gc),ed=()=>[Yc,Jc,Zc,Qc],td=()=>[Jc,Yc,Qc,Zc];var od=[Xn("item"),dc(),ur("overrides",{}),Oi("placement",((e,t,o)=>{const n=Dl(o,t.item.element),r=uc(e.element,t,ed(),td(),ed(),td(),M.none());return M.some(rc({anchorBox:n,bubble:nc(),overrides:t.overrides,layouts:r,placer:M.none()}))}))],nd=jn("type",{selection:jc,node:Cc,hotspot:mc,submenu:od,makeshift:gc});const rd=[or("classes",Bn),hr("mode","all",["all","layout","placement"])],sd=[ur("useFixed",_),nr("getBounds")],ad=[Kn("anchor",nd),dr("transition",rd)],id=(e,t,o,n,r,s,a)=>((e,t,o,n,r,s,a,i)=>{const l=Ql(a,"maxHeightFunction",Jl()),c=Ql(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Bl(u,s),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:r,transition:i};return ec(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(o.anchorBox,t),r.element,o.bubble,o.layouts,s,n,o.overrides,a),ld=(e,t,o,n,r,s)=>{const a=s.map($o);return cd(e,t,o,n,r,a)},cd=(e,t,o,n,r,s)=>{const a=Un("placement.info",Cn(ad),r),i=a.anchor,l=n.element,c=o.get(n.uid);Ol((()=>{_t(l,"position","fixed");const r=Dt(l,"visibility");_t(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Rl(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Wt(e.element),o=e.element.dom.getBoundingClientRect();return Il(t.left,t.top,o.width,o.height)})(e),u=i.placement,m=s.map(y).or(t.getBounds);u(e,i,d).each((t=>{const r=t.placer.getOr(id)(e,d,t,m,n,c,a.transition);o.set(n.uid,r)})),r.fold((()=>{It(l,"visibility")}),(e=>{_t(l,"visibility",e)})),Dt(l,"left").isNone()&&Dt(l,"top").isNone()&&Dt(l,"right").isNone()&&Dt(l,"bottom").isNone()&&xe(Dt(l,"position"),"fixed")&&It(l,"position")}),l)};var dd=Object.freeze({__proto__:null,position:(e,t,o,n,r)=>{ld(e,t,o,n,r,M.none())},positionWithin:ld,positionWithinBounds:cd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const r=n.element;L(["position","left","right","top","bottom"],(e=>It(r,e))),(e=>{kt(e,Nl)})(r),o.clear(n.uid)}});const ud=hl({fields:sd,name:"positioning",active:xl,apis:dd,state:Object.freeze({__proto__:null,init:()=>{let e={};return ba({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),md=e=>e.getSystem().isConnected(),gd=e=>{Cs(e,hs());const t=e.components();L(t,gd)},pd=e=>{const t=e.components();L(t,pd),Cs(e,ps())},hd=(e,t)=>{e.getSystem().addToWorld(t),pt(e.element)&&pd(t)},fd=e=>{gd(e),e.getSystem().removeFromWorld(e)},bd=(e,t)=>{Fo(e.element,t.element)},vd=(e,t)=>{yd(e,t,Fo)},yd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),pt(e.element)&&pd(t),e.syncComponents()},xd=e=>{gd(e),No(e.element),e.getSystem().removeFromWorld(e)},wd=e=>{const t=et(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));xd(e),t.each((e=>{e.syncComponents()}))},Sd=e=>{const t=e.components();L(t,xd),Ro(e.element),e.syncComponents()},kd=(e,t)=>{Cd(e,t,Fo)},Cd=(e,t,o)=>{o(e,t.element);const n=nt(t.element);L(n,(e=>{t.getByDom(e).each(pd)}))},Od=e=>{const t=nt(e.element);L(t,(t=>{e.getByDom(t).each(gd)})),No(e.element)},_d=(e,t,o,n)=>{o.get().each((t=>{Sd(e)}));const r=t.getAttachPoint(e);vd(r,e);const s=e.getSystem().build(n);return vd(e,s),o.set(s),s},Td=(e,t,o,n)=>{const r=_d(e,t,o,n);return t.onOpen(e,r),r},Ed=(e,t,o)=>{o.get().each((n=>{Sd(e),wd(e),t.onClose(e,n),o.clear()}))},Md=(e,t,o)=>o.isOpen(),Ad=(e,t,o)=>{const n=t.getAttachPoint(e);_t(e.element,"position",ud.getMode(n)),((e,t,o,n)=>{Dt(e.element,t).fold((()=>{kt(e.element,o)}),(t=>{vt(e.element,o,t)})),_t(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Dd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>Dt(e,t).isSome())))(e.element)||It(e.element,"position"),((e,t,o)=>{wt(e.element,o).fold((()=>It(e.element,t)),(o=>_t(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Bd=Object.freeze({__proto__:null,cloak:Ad,decloak:Dd,open:Td,openWhileCloaked:(e,t,o,n,r)=>{Ad(e,t),Td(e,t,o,n),r(),Dd(e,t)},close:Ed,isOpen:Md,isPartOf:(e,t,o,n)=>Md(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>_d(e,t,o,n)))}),Fd=Object.freeze({__proto__:null,events:(e,t)=>As([Fs(is(),((o,n)=>{Ed(o,e,t)}))])}),Id=[wi("onOpen"),wi("onClose"),Xn("isPartOf"),Xn("getAttachPoint"),ur("cloakVisibilityAttr","data-precloak-visibility")],Rd=Object.freeze({__proto__:null,init:()=>{const e=Ul(),t=y("not-implemented");return ba({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Nd=hl({fields:Id,name:"sandboxing",active:Fd,apis:Bd,state:Rd}),Vd=y("dismiss.popups"),Hd=y("reposition.popups"),zd=y("mouse.released"),Ld=kn([ur("isExtraPart",_),dr("fireEventInstead",[ur("event",fs())])]),Pd=e=>{const t=Un("Dismissal",Ld,e);return{[Vd()]:{schema:kn([Xn("target")]),onReceive:(e,o)=>{Nd.isOpen(e)&&(Nd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Nd.close(e)),(t=>Cs(e,t.event))))}}}},Ud=kn([dr("fireEventInstead",[ur("event",bs())]),Qn("doReposition")]),Wd=e=>{const t=Un("Reposition",Ud,e);return{[Hd()]:{onReceive:e=>{Nd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Cs(e,t.event)))}}}},jd=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Gd=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var $d=Object.freeze({__proto__:null,onLoad:jd,onUnload:Gd,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),qd=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Ps(((o,n)=>{jd(o,e,t)})),Us(((o,n)=>{Gd(o,e,t)}))]:[dl(e,t,jd)];return As(o)}});const Xd=()=>{const e=xr(null);return ba({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Kd=()=>{const e=xr({}),t=xr({});return ba({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),r=t.get(),s={},a={};L(o,(e=>{s[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...s}),t.set({...r,...a})},clear:()=>{e.set({}),t.set({})}})};var Yd=Object.freeze({__proto__:null,memory:Xd,dataset:Kd,manual:()=>ba({readState:b}),init:e=>e.store.manager.state(e)});const Jd=(e,t,o,n)=>{const r=t.store;o.update([n]),r.setValue(e,n),t.onSetValue(e,n)};var Zd=[nr("initialValue"),Xn("getFallbackEntry"),Xn("getDataKey"),Xn("setValue"),Oi("manager",{setValue:Jd,getValue:(e,t,o)=>{const n=t.store,r=n.getDataKey(e);return o.lookup(r).getOrThunk((()=>n.getFallbackEntry(r)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Jd(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Kd})],Qd=[Xn("getValue"),ur("setValue",b),nr("initialValue"),Oi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:fa.init})],eu=[nr("initialValue"),Oi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Xd})],tu=[mr("store",{mode:"memory"},jn("mode",{memory:eu,manual:Qd,dataset:Zd})),wi("onSetValue"),ur("resetOnDom",!1)];const ou=hl({fields:tu,name:"representing",active:qd,apis:$d,extra:{setValueFrom:(e,t)=>{const o=ou.getValue(t);ou.setValue(e,o)}},state:Yd}),nu=(e,t)=>yr(e,{},z(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Gn(o,o,{tag:"option",process:{}},bn((e=>nn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([$n("dump",x)])),ru=e=>e.dump,su=(e,t)=>({...gl(t),...e.dump}),au=nu,iu=su,lu="placeholder",cu=wr([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),du=e=>ve(e,"uiType"),uu=(e,t,o,n)=>((e,t,o,n)=>du(o)&&o.uiType===lu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?cu.single(!0,y(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):cu.single(!1,y(o)))(e,0,o,n).fold(((r,s)=>{const a=du(o)?s(t,o.config,o.validated):s(t),i=be(a,"components").getOr([]),l=X(i,(o=>uu(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(du(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(x)(e)}return n(t)})),mu=cu.single,gu=cu.multiple,pu=y(lu),hu=wr([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),fu=ur("factory",{sketch:x}),bu=ur("schema",[]),vu=Xn("name"),yu=Gn("pname","pname",un((e=>"<alloy."+Qs(e.name)+">")),Mn()),xu=$n("schema",(()=>[nr("preprocess")])),wu=ur("defaults",y({})),Su=ur("overrides",y({})),ku=Cn([fu,bu,vu,yu,wu,Su]),Cu=Cn([fu,bu,vu,wu,Su]),Ou=Cn([fu,bu,vu,yu,wu,Su]),_u=Cn([fu,xu,vu,Xn("unit"),yu,wu,Su]),Tu=e=>e.fold(M.some,M.none,M.some,M.some),Eu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Mu=(e,t)=>o=>{const n=Un("Converting part type",t,o);return e(n)},Au=Mu(hu.required,ku),Du=Mu(hu.external,Cu),Bu=Mu(hu.optional,Ou),Fu=Mu(hu.group,_u),Iu=y("entirety");var Ru=Object.freeze({__proto__:null,required:Au,external:Du,optional:Bu,group:Fu,asNamedPart:Tu,name:Eu,asCommon:e=>e.fold(x,x,x,x),original:Iu});const Nu=(e,t,o,n)=>cn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Vu=(e,t)=>{const o={};return L(t,(t=>{Tu(t).each((t=>{const n=Hu(e,t.pname);o[t.name]=o=>{const r=Un("Part: "+t.name+" in "+e,Cn(t.schema),o);return{...n,config:o,validated:r}}}))})),o},Hu=(e,t)=>({uiType:pu(),owner:e,name:t}),zu=(e,t,o)=>({uiType:pu(),owner:e,name:t,config:o,validated:{}}),Lu=e=>X(e,(e=>e.fold(M.none,M.some,M.none,M.none).map((e=>er(e.name,e.schema.concat([_i(Iu())])))).toArray())),Pu=e=>z(e,Eu),Uu=(e,t,o)=>((e,t,o)=>{const n={},r={};return L(o,(e=>{e.fold((e=>{n[e.pname]=mu(!0,((t,o,n)=>e.factory.sketch(Nu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];r[e.name]=y(e.factory.sketch(Nu(t,e,o[Iu()]),o))}),(e=>{n[e.pname]=mu(!1,((t,o,n)=>e.factory.sketch(Nu(t,e,o,n))))}),(e=>{n[e.pname]=gu(!0,((t,o,n)=>{const r=t[e.name];return z(r,(o=>e.factory.sketch(cn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(r)}})(0,t,o),Wu=(e,t,o)=>((e,t,o,n)=>{const r=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),s=((e,t,o,n)=>X(o,(o=>uu(e,t,o,n))))(e,t,o,r);return le(r,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),s})(M.some(e),t,t.components,o),ju=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Gu=(e,t,o)=>ju(e,t,o).getOrDie("Could not find part: "+o),$u=(e,t,o)=>{const n={},r=t.partUids,s=e.getSystem();return L(o,(e=>{n[e]=y(s.getByUid(r[e]))})),n},qu=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>y(o.getByUid(e))))},Xu=e=>ae(e.partUids),Ku=(e,t,o)=>{const n={},r=t.partUids,s=e.getSystem();return L(o,(e=>{n[e]=y(s.getByUid(r[e]).getOrDie())})),n},Yu=(e,t)=>{const o=Pu(t);return kr(z(o,(t=>({key:t,value:e+"-"+t}))))},Ju=e=>Gn("partUids","partUids",gn((t=>Yu(t.uid,e))),Mn());var Zu=Object.freeze({__proto__:null,generate:Vu,generateOne:zu,schemas:Lu,names:Pu,substitutes:Uu,components:Wu,defaultUids:Yu,defaultUidsSchema:Ju,getAllParts:qu,getAllPartNames:Xu,getPart:ju,getPartOrDie:Gu,getParts:$u,getPartsOrDie:Ku});const Qu=(e,t,o,n,r)=>{const s=((e,t)=>(e.length>0?[er("parts",e)]:[]).concat([Xn("uid"),ur("dom",{}),ur("components",[]),_i("originalSpec"),ur("debug.sketcher",{})]).concat(t))(n,r);return Un(e+" [SpecSchema]",kn(s.concat(t)),o)},em=(e,t,o,n,r)=>{const s=tm(r),a=Lu(o),i=Ju(o),l=Qu(e,t,s,a,[i]),c=Uu(0,l,o);return n(l,Wu(e,l,c.internals()),s,c.externals())},tm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:aa("uid")},om=kn([Xn("name"),Xn("factory"),Xn("configFields"),ur("apis",{}),ur("extraApis",{})]),nm=kn([Xn("name"),Xn("factory"),Xn("configFields"),Xn("partFields"),ur("apis",{}),ur("extraApis",{})]),rm=e=>{const t=Un("Sketcher for "+e.name,om,e),o=ce(t.apis,ha),n=ce(t.extraApis,((e,t)=>ua(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const r=tm(n);return o(Qu(e,t,r,[],[]),r)})(t.name,t.configFields,t.factory,e),...o,...n}},sm=e=>{const t=Un("Sketcher for "+e.name,nm,e),o=Vu(t.name,t.partFields),n=ce(t.apis,ha),r=ce(t.extraApis,((e,t)=>ua(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>em(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...r}},am=e=>Ge("input")(e)&&"radio"!==xt(e,"type")||Ge("textarea")(e);var im=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const lm=[Xn("find")],cm=hl({fields:lm,name:"composing",apis:im}),dm=["input","button","textarea","select"],um=(e,t,o)=>{(t.disabled()?bm:vm)(e,t)},mm=(e,t)=>!0===t.useNative&&R(dm,ze(e.element)),gm=e=>{vt(e.element,"disabled","disabled")},pm=e=>{kt(e.element,"disabled")},hm=e=>{vt(e.element,"aria-disabled","true")},fm=e=>{vt(e.element,"aria-disabled","false")},bm=(e,t,o)=>{t.disableClass.each((t=>{Da(e.element,t)})),(mm(e,t)?gm:hm)(e),t.onDisabled(e)},vm=(e,t,o)=>{t.disableClass.each((t=>{Ba(e.element,t)})),(mm(e,t)?pm:fm)(e),t.onEnabled(e)},ym=(e,t)=>mm(e,t)?(e=>St(e.element,"disabled"))(e):(e=>"true"===xt(e.element,"aria-disabled"))(e);var xm=Object.freeze({__proto__:null,enable:vm,disable:bm,isDisabled:ym,onLoad:um,set:(e,t,o,n)=>{(n?bm:vm)(e,t)}}),wm=Object.freeze({__proto__:null,exhibit:(e,t)=>ya({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>As([Ds(ns(),((t,o)=>ym(t,e))),dl(e,t,um)])}),Sm=[br("disabled",_),ur("useNative",!0),nr("disableClass"),wi("onDisabled"),wi("onEnabled")];const km=hl({fields:Sm,name:"disabling",active:wm,apis:xm}),Cm=(e,t,o,n)=>{const r=Nc(e.element,"."+t.highlightClass);L(r,(o=>{N(n,(e=>Xe(e.element,o)))||(Ba(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Cs(o,ks())})))}))},Om=(e,t,o,n)=>{Cm(e,t,0,[n]),_m(e,t,o,n)||(Da(n.element,t.highlightClass),t.onHighlight(e,n),Cs(n,Ss()))},_m=(e,t,o,n)=>Fa(n.element,t.highlightClass),Tm=(e,t,o)=>ri(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Em=(e,t,o)=>{const n=Nc(e.element,"."+t.itemClass);return(n.length>0?M.some(n[n.length-1]):M.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Mm=(e,t,o,n)=>{const r=Nc(e.element,"."+t.itemClass);return $(r,(e=>Fa(e,t.highlightClass))).bind((t=>{const o=Hi(t,n,0,r.length-1);return e.getSystem().getByDom(r[o]).toOptional()}))},Am=(e,t,o)=>{const n=Nc(e.element,"."+t.itemClass);return we(z(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Dm=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Cm(e,t,0,[]),dehighlight:(e,t,o,n)=>{_m(e,t,o,n)&&(Ba(n.element,t.highlightClass),t.onDehighlight(e,n),Cs(n,ks()))},highlight:Om,highlightFirst:(e,t,o)=>{Tm(e,t).each((n=>{Om(e,t,o,n)}))},highlightLast:(e,t,o)=>{Em(e,t).each((n=>{Om(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const r=Nc(e.element,"."+t.itemClass);return M.from(r[n]).fold((()=>Jo.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Om(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const r=Am(e,t);G(r,n).each((n=>{Om(e,t,o,n)}))},isHighlighted:_m,getHighlighted:(e,t,o)=>ri(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Tm,getLast:Em,getPrevious:(e,t,o)=>Mm(e,t,0,-1),getNext:(e,t,o)=>Mm(e,t,0,1),getCandidates:Am}),Bm=[Xn("highlightClass"),Xn("itemClass"),wi("onHighlight"),wi("onDehighlight")];const Fm=hl({fields:Bm,name:"highlighting",apis:Dm}),Im=[8],Rm=[9],Nm=[13],Vm=[27],Hm=[32],zm=[37],Lm=[38],Pm=[39],Um=[40],Wm=(e,t,o)=>{const n=Y(e.slice(0,t)),r=Y(e.slice(t+1));return G(n.concat(r),o)},jm=(e,t,o)=>{const n=Y(e.slice(0,t));return G(n,o)},Gm=(e,t,o)=>{const n=e.slice(0,t),r=e.slice(t+1);return G(r.concat(n),o)},$m=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},qm=e=>t=>{const o=t.raw;return R(e,o.which)},Xm=e=>t=>K(e,(e=>e(t))),Km=e=>!0===e.raw.shiftKey,Ym=e=>!0===e.raw.ctrlKey,Jm=k(Km),Zm=(e,t)=>({matches:e,classification:t}),Qm=(e,t,o)=>{t.exists((e=>o.exists((t=>Xe(t,e)))))||Os(e,vs(),{prevFocus:t,newFocus:o})},eg=()=>{const e=e=>Cl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const r=e(t);Qm(t,n,r)}}},tg=()=>{const e=e=>Fm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Fm.highlight(t,e)}));const r=e(t);Qm(t,n,r)}}};var og;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(og||(og={}));const ng=(e,t,o,n,r)=>{const s=(e,t,o,n,r)=>{return(s=o(e,t,n,r),a=t.event,G(s,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,r)));var s,a},a={schema:()=>e.concat([ur("focusManager",eg()),mr("focusInside","onFocus",Hn((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?Jo.value(e):Jo.error("Invalid value for focusInside")))),Oi("handler",a),Oi("state",t),Oi("sendFocusIn",r)]),processKey:s,toEvents:(e,t)=>{const a=e.focusInside!==og.OnFocusMode?M.none():r(e).map((o=>Fs(Qr(),((n,r)=>{o(n,e,t),r.stop()})))),i=[Fs(Ur(),((n,a)=>{s(n,a,o,e,t).fold((()=>{((o,n)=>{const s=qm(Hm.concat(Nm))(n.event);e.focusInside===og.OnEnterOrSpaceMode&&s&&Tr(o,n)&&r(e).each((r=>{r(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Fs(Wr(),((o,r)=>{s(o,r,n,e,t).each((e=>{r.stop()}))}))];return As(a.toArray().concat(i))}};return a},rg=e=>{const t=[nr("onEscape"),nr("onEnter"),ur("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ur("firstTabstop",0),ur("useTabstopAt",T),nr("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>si(t,e))).getOr(t);return Ht(o)>0},n=(e,t,n)=>{((e,t)=>{const n=Nc(e.element,t.selector),r=U(n,(e=>o(t,e)));return M.from(r[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,n,r)=>{const s=Nc(e.element,n.selector);return((e,t)=>t.focusManager.get(e).bind((e=>si(e,t.selector))))(e,n).bind((t=>$(s,S(Xe,t)).bind((t=>((e,t,n,r,s)=>s(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(r,e))).fold((()=>r.cyclic?M.some(!0):M.none()),(t=>(r.focusManager.set(e,t),M.some(!0)))))(e,s,t,n,r)))))},s=y([Zm(Xm([Km,qm(Rm)]),((e,t,o)=>{const n=o.cyclic?Wm:jm;return r(e,0,o,n)})),Zm(qm(Rm),((e,t,o)=>{const n=o.cyclic?Gm:$m;return r(e,0,o,n)})),Zm(Xm([Jm,qm(Nm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),a=y([Zm(qm(Vm),((e,t,o)=>o.onEscape.bind((o=>o(e,t)))))]);return ng(t,fa.init,s,a,(()=>M.some(n)))};var sg=rg($n("cyclic",_)),ag=rg($n("cyclic",T));const ig=(e,t,o)=>am(o)&&qm(Hm)(t.event)?M.none():((e,t,o)=>(Ts(e,o,ns()),M.some(!0)))(e,0,o),lg=(e,t)=>M.some(!0),cg=[ur("execute",ig),ur("useSpace",!1),ur("useEnter",!0),ur("useControlEnter",!1),ur("useDown",!1)],dg=(e,t,o)=>o.execute(e,t,e.element);var ug=ng(cg,fa.init,((e,t,o,n)=>{const r=o.useSpace&&!am(e.element)?Hm:[],s=o.useEnter?Nm:[],a=o.useDown?Um:[],i=r.concat(s).concat(a);return[Zm(qm(i),dg)].concat(o.useControlEnter?[Zm(Xm([Ym,qm(Nm)]),dg)]:[])}),((e,t,o,n)=>o.useSpace&&!am(e.element)?[Zm(qm(Hm),lg)]:[]),(()=>M.none()));const mg=()=>{const e=Ul();return ba({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var gg=Object.freeze({__proto__:null,flatgrid:mg,init:e=>e.state(e)});const pg=e=>(t,o,n,r)=>{const s=e(t.element);return vg(s,t,o,n,r)},hg=(e,t)=>{const o=sc(e,t);return pg(o)},fg=(e,t)=>{const o=sc(t,e);return pg(o)},bg=e=>(t,o,n,r)=>vg(e,t,o,n,r),vg=(e,t,o,n,r)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,r))).map((e=>(n.focusManager.set(t,e),!0))),yg=bg,xg=bg,wg=bg,Sg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),kg=(e,t,o)=>{const n=Nc(e,o);return((e,o)=>$(e,(e=>Xe(e,t))).map((t=>({index:t,candidates:e}))))(U(n,Sg))},Cg=(e,t)=>$(e,(e=>Xe(t,e))),Og=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?M.some(e[n]):M.none()})),_g=(e,t,o,n,r)=>Og(e,t,n,((t,s)=>{const a=t===o-1?e.length-t*n:n,i=Hi(s,r,0,a-1);return M.some({row:t,column:i})})),Tg=(e,t,o,n,r)=>Og(e,t,n,((t,s)=>{const a=Hi(t,r,0,o-1),i=a===o-1?e.length-a*n:n,l=zi(s,0,i-1);return M.some({row:a,column:l})})),Eg=[Xn("selector"),ur("execute",ig),Si("onEscape"),ur("captureTab",!1),Ti()],Mg=(e,t,o)=>{ri(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Ag=e=>(t,o,n,r)=>kg(t,o,n.selector).bind((t=>e(t.candidates,t.index,r.getNumRows().getOr(n.initSize.numRows),r.getNumColumns().getOr(n.initSize.numColumns)))),Dg=(e,t,o)=>o.captureTab?M.some(!0):M.none(),Bg=Ag(((e,t,o,n)=>_g(e,t,o,n,-1))),Fg=Ag(((e,t,o,n)=>_g(e,t,o,n,1))),Ig=Ag(((e,t,o,n)=>Tg(e,t,o,n,-1))),Rg=Ag(((e,t,o,n)=>Tg(e,t,o,n,1))),Ng=y([Zm(qm(zm),hg(Bg,Fg)),Zm(qm(Pm),fg(Bg,Fg)),Zm(qm(Lm),yg(Ig)),Zm(qm(Um),xg(Rg)),Zm(Xm([Km,qm(Rm)]),Dg),Zm(Xm([Jm,qm(Rm)]),Dg),Zm(qm(Hm.concat(Nm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>si(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Vg=y([Zm(qm(Vm),((e,t,o)=>o.onEscape(e,t))),Zm(qm(Hm),lg)]);var Hg=ng(Eg,mg,Ng,Vg,(()=>M.some(Mg)));const zg=(e,t,o,n)=>{const r=(e,t,o)=>{const s=Hi(t,n,0,o.length-1);return s===e?M.none():(a=o[s],"button"===ze(a)&&"disabled"===xt(a,"disabled")?r(e,s,o):M.from(o[s]));var a};return kg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Lg=[Xn("selector"),ur("getInitial",M.none),ur("execute",ig),Si("onEscape"),ur("executeOnMove",!1),ur("allowVertical",!0)],Pg=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>si(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Ug=(e,t,o)=>{t.getInitial(e).orThunk((()=>ri(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Wg=(e,t,o)=>zg(e,o.selector,t,-1),jg=(e,t,o)=>zg(e,o.selector,t,1),Gg=e=>(t,o,n,r)=>e(t,o,n,r).bind((()=>n.executeOnMove?Pg(t,o,n):M.some(!0))),$g=y([Zm(qm(Hm),lg),Zm(qm(Vm),((e,t,o)=>o.onEscape(e,t)))]);var qg=ng(Lg,fa.init,((e,t,o,n)=>{const r=zm.concat(o.allowVertical?Lm:[]),s=Pm.concat(o.allowVertical?Um:[]);return[Zm(qm(r),Gg(hg(Wg,jg))),Zm(qm(s),Gg(fg(Wg,jg))),Zm(qm(Nm),Pg),Zm(qm(Hm),Pg)]}),$g,(()=>M.some(Ug)));const Xg=(e,t,o)=>M.from(e[t]).bind((e=>M.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Kg=(e,t,o,n)=>{const r=e[t].length,s=Hi(o,n,0,r-1);return Xg(e,t,s)},Yg=(e,t,o,n)=>{const r=Hi(o,n,0,e.length-1),s=e[r].length,a=zi(t,0,s-1);return Xg(e,r,a)},Jg=(e,t,o,n)=>{const r=e[t].length,s=zi(o+n,0,r-1);return Xg(e,t,s)},Zg=(e,t,o,n)=>{const r=zi(o+n,0,e.length-1),s=e[r].length,a=zi(t,0,s-1);return Xg(e,r,a)},Qg=[er("selectors",[Xn("row"),Xn("cell")]),ur("cycles",!0),ur("previousSelector",M.none),ur("execute",ig)],ep=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return ri(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},tp=(e,t)=>(o,n,r)=>{const s=r.cycles?e:t;return si(n,r.selectors.row).bind((e=>{const t=Nc(e,r.selectors.cell);return Cg(t,n).bind((t=>{const n=Nc(o,r.selectors.row);return Cg(n,e).bind((e=>{const o=((e,t)=>z(e,(e=>Nc(e,t.selectors.cell))))(n,r);return s(o,e,t).map((e=>e.cell))}))}))}))},op=tp(((e,t,o)=>Kg(e,t,o,-1)),((e,t,o)=>Jg(e,t,o,-1))),np=tp(((e,t,o)=>Kg(e,t,o,1)),((e,t,o)=>Jg(e,t,o,1))),rp=tp(((e,t,o)=>Yg(e,o,t,-1)),((e,t,o)=>Zg(e,o,t,-1))),sp=tp(((e,t,o)=>Yg(e,o,t,1)),((e,t,o)=>Zg(e,o,t,1))),ap=y([Zm(qm(zm),hg(op,np)),Zm(qm(Pm),fg(op,np)),Zm(qm(Lm),yg(rp)),Zm(qm(Um),xg(sp)),Zm(qm(Hm.concat(Nm)),((e,t,o)=>Cl(e.element).bind((n=>o.execute(e,t,n)))))]),ip=y([Zm(qm(Hm),lg)]);var lp=ng(Qg,fa.init,ap,ip,(()=>M.some(ep)));const cp=[Xn("selector"),ur("execute",ig),ur("moveOnTab",!1)],dp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),up=(e,t,o)=>{ri(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},mp=(e,t,o)=>zg(e,o.selector,t,-1),gp=(e,t,o)=>zg(e,o.selector,t,1),pp=y([Zm(qm(Lm),wg(mp)),Zm(qm(Um),wg(gp)),Zm(Xm([Km,qm(Rm)]),((e,t,o,n)=>o.moveOnTab?wg(mp)(e,t,o,n):M.none())),Zm(Xm([Jm,qm(Rm)]),((e,t,o,n)=>o.moveOnTab?wg(gp)(e,t,o,n):M.none())),Zm(qm(Nm),dp),Zm(qm(Hm),dp)]),hp=y([Zm(qm(Hm),lg)]);var fp=ng(cp,fa.init,pp,hp,(()=>M.some(up)));const bp=[Si("onSpace"),Si("onEnter"),Si("onShiftEnter"),Si("onLeft"),Si("onRight"),Si("onTab"),Si("onShiftTab"),Si("onUp"),Si("onDown"),Si("onEscape"),ur("stopSpaceKeyup",!1),nr("focusIn")];var vp=ng(bp,fa.init,((e,t,o)=>[Zm(qm(Hm),o.onSpace),Zm(Xm([Jm,qm(Nm)]),o.onEnter),Zm(Xm([Km,qm(Nm)]),o.onShiftEnter),Zm(Xm([Km,qm(Rm)]),o.onShiftTab),Zm(Xm([Jm,qm(Rm)]),o.onTab),Zm(qm(Lm),o.onUp),Zm(qm(Um),o.onDown),Zm(qm(zm),o.onLeft),Zm(qm(Pm),o.onRight),Zm(qm(Hm),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Zm(qm(Hm),lg)]:[],Zm(qm(Vm),o.onEscape)]),(e=>e.focusIn));const yp=sg.schema(),xp=ag.schema(),wp=qg.schema(),Sp=Hg.schema(),kp=lp.schema(),Cp=ug.schema(),Op=fp.schema(),_p=vp.schema(),Tp=bl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:yp,cyclic:xp,flow:wp,flatgrid:Sp,matrix:kp,execution:Cp,menu:Op,special:_p}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,r)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,r):console.error("Layout does not support setGridSize")}},state:gg}),Ep=(e,t)=>{Ol((()=>{((e,t,o)=>{const n=e.components();(e=>{L(e.components(),(e=>No(e.element))),Ro(e.element),e.syncComponents()})(e);const r=o(t),s=J(n,r);L(s,(t=>{gd(t),e.getSystem().removeFromWorld(t)})),L(r,(t=>{md(t)?bd(e,t):(e.getSystem().addToWorld(t),bd(e,t),pt(e.element)&&pd(t))})),e.syncComponents()})(e,t,(()=>z(t,e.getSystem().build)))}),e.element)},Mp=(e,t)=>{Ol((()=>{((o,n,r)=>{const s=o.components(),a=X(n,(e=>pa(e).toArray()));L(s,(e=>{R(a,e)||fd(e)}));const i=((e,t,o)=>za(e,t,((t,n)=>La(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(s,i);L(l,(e=>{md(e)&&fd(e)})),L(i,(e=>{md(e)||hd(o,e)})),o.syncComponents()})(e,t)}),e.element)},Ap=(e,t,o,n)=>{fd(t);const r=La(e.element,o,n,e.getSystem().buildOrPatch);hd(e,r),e.syncComponents()},Dp=(e,t,o)=>{const n=e.getSystem().build(o);yd(e,n,t)},Bp=(e,t,o,n)=>{wd(t),Dp(e,((e,t)=>((e,t,o)=>{rt(e,o).fold((()=>{Fo(e,t)}),(e=>{Ao(e,t)}))})(e,t,o)),n)},Fp=(e,t)=>e.components(),Ip=(e,t,o,n,r)=>{const s=Fp(e);return M.from(s[n]).map((o=>(r.fold((()=>wd(o)),(r=>{(t.reuseDom?Ap:Bp)(e,o,n,r)})),o)))};var Rp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Dp(e,Fo,n)},prepend:(e,t,o,n)=>{Dp(e,Bo,n)},remove:(e,t,o,n)=>{const r=Fp(e),s=G(r,(e=>Xe(n.element,e.element)));s.each(wd)},replaceAt:Ip,replaceBy:(e,t,o,n,r)=>{const s=Fp(e);return $(s,n).bind((o=>Ip(e,t,0,o,r)))},set:(e,t,o,n)=>(t.reuseDom?Mp:Ep)(e,n),contents:Fp});const Np=hl({fields:[fr("reuseDom",!0)],name:"replacing",apis:Rp}),Vp=(e,t)=>{const o=((e,t)=>{const o=As(t);return hl({fields:[Xn("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:fa}}},Hp=(e,t)=>{t.ignore||(wl(e.element),t.onFocus(e))};var zp=Object.freeze({__proto__:null,focus:Hp,blur:(e,t)=>{t.ignore||(e=>{e.dom.blur()})(e.element)},isFocused:e=>Sl(e.element)}),Lp=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return ya(o)},events:e=>As([Fs(Qr(),((t,o)=>{Hp(t,e),o.stop()}))].concat(e.stopMousedown?[Fs(Rr(),((e,t)=>{t.event.prevent()}))]:[]))}),Pp=[wi("onFocus"),ur("stopMousedown",!1),ur("ignore",!1)];const Up=hl({fields:Pp,name:"focusing",active:Lp,apis:zp}),Wp=(e,t,o,n)=>{const r=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?Da(e.element,t):Ba(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),r!==n&&t.onToggled(e,n)},jp=(e,t,o)=>{Wp(e,t,o,!o.get())},Gp=(e,t,o)=>{Wp(e,t,o,t.selected)};var $p=Object.freeze({__proto__:null,onLoad:Gp,toggle:jp,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Wp(e,t,o,!0)},off:(e,t,o)=>{Wp(e,t,o,!1)},set:Wp}),qp=Object.freeze({__proto__:null,exhibit:()=>ya({}),events:(e,t)=>{const o=(n=e,r=t,s=jp,js((e=>{s(e,n,r)})));var n,r,s;const a=dl(e,t,Gp);return As(q([e.toggleOnExecute?[o]:[],[a]]))}});const Xp=(e,t,o)=>{vt(e.element,"aria-expanded",o)};var Kp=[ur("selected",!1),nr("toggleClass"),ur("toggleOnExecute",!0),wi("onToggled"),mr("aria",{mode:"none"},jn("mode",{pressed:[ur("syncWithExpanded",!1),Oi("update",((e,t,o)=>{vt(e.element,"aria-pressed",o),t.syncWithExpanded&&Xp(e,0,o)}))],checked:[Oi("update",((e,t,o)=>{vt(e.element,"aria-checked",o)}))],expanded:[Oi("update",Xp)],selected:[Oi("update",((e,t,o)=>{vt(e.element,"aria-selected",o)}))],none:[Oi("update",b)]}))];const Yp=hl({fields:Kp,name:"toggling",active:qp,apis:$p,state:(!1,{init:()=>{const e=xr(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const Jp=()=>{const e=(e,t)=>{t.stop(),_s(e)};return[Fs($r(),e),Fs(ss(),e),Hs(Dr()),Hs(Rr())]},Zp=e=>As(q([e.map((e=>js(((t,o)=>{e(t),o.stop()})))).toArray(),Jp()])),Qp="alloy.item-hover",eh="alloy.item-focus",th="alloy.item-toggled",oh=e=>{(Cl(e.element).isNone()||Up.isFocused(e))&&(Up.isFocused(e)||Up.focus(e),Os(e,Qp,{item:e}))},nh=e=>{Os(e,eh,{item:e})},rh=y(Qp),sh=y(eh),ah=y(th),ih=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),lh=[Xn("data"),Xn("components"),Xn("dom"),ur("hasSubmenu",!1),nr("toggling"),au("itemBehaviours",[Yp,Up,Tp,ou]),ur("ignoreFocus",!1),ur("domModification",{}),Oi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:ih(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:iu(e.itemBehaviours,[e.toggling.fold(Yp.revoke,(e=>Yp.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Os(e,th,{item:e,state:t})})(t,o)}}))(e)))),Up.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{nh(e)}}),Tp.config({mode:"execution"}),ou.config({store:{mode:"memory",initialValue:e.data}}),Vp("item-type-events",[...Jp(),Fs(zr(),oh),Fs(rs(),Up.focus)])]),components:e.components,eventOrder:e.eventOrder}))),ur("eventOrder",{})],ch=[Xn("dom"),Xn("components"),Oi("builder",(e=>({dom:e.dom,components:e.components,events:As([zs(rs())])})))],dh=y("item-widget"),uh=y([Au({name:"widget",overrides:e=>({behaviours:gl([ou.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),mh=[Xn("uid"),Xn("data"),Xn("components"),Xn("dom"),ur("autofocus",!1),ur("ignoreFocus",!1),au("widgetBehaviours",[ou,Up,Tp]),ur("domModification",{}),Ju(uh()),Oi("builder",(e=>{const t=Uu(dh(),e,uh()),o=Wu(dh(),e,t.internals()),n=t=>ju(t,e,"widget").map((e=>(Tp.focusIn(e),e))),r=(t,o)=>am(o.event.target)?M.none():e.autofocus?(o.setSource(t.element),M.none()):M.none();return{dom:e.dom,components:o,domModification:e.domModification,events:As([js(((e,t)=>{n(e).each((e=>{t.stop()}))})),Fs(zr(),oh),Fs(rs(),((t,o)=>{e.autofocus?n(t):Up.focus(t)}))]),behaviours:iu(e.widgetBehaviours,[ou.config({store:{mode:"memory",initialValue:e.data}}),Up.config({ignore:e.ignoreFocus,onFocus:e=>{nh(e)}}),Tp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:vl(),onLeft:r,onRight:r,onEscape:(t,o)=>Up.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),M.none()):M.none():(Up.focus(t),M.some(!0))})])}}))],gh=jn("type",{widget:mh,item:lh,separator:ch}),ph=y([Fu({factory:{sketch:e=>{const t=Un("menu.spec item",gh,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:aa("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),hh=y([Xn("value"),Xn("items"),Xn("dom"),Xn("components"),ur("eventOrder",{}),nu("menuBehaviours",[Fm,ou,cm,Tp]),mr("movement",{mode:"menu",moveOnTab:!0},jn("mode",{grid:[Ti(),Oi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Oi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),Xn("rowSelector"),ur("previousSelector",M.none)],menu:[ur("moveOnTab",!0),Oi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),Kn("markers",fi()),ur("fakeFocus",!1),ur("focusManager",eg()),wi("onHighlight"),wi("onDehighlight")]),fh=y("alloy.menu-focus"),bh=sm({name:"Menu",configFields:hh(),partFields:ph(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:su(e.menuBehaviours,[Fm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),ou.config({store:{mode:"memory",initialValue:e.value}}),cm.config({find:M.some}),Tp.config(e.movement.config(e,e.movement))]),events:As([Fs(sh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Fm.highlight(e,o),t.stop(),Os(e,fh(),{menu:e,item:o})}))})),Fs(rh(),((e,t)=>{const o=t.event.item;Fm.highlight(e,o)})),Fs(ah(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===xt(o.element,"role")&&((e,t)=>{const o=Nc(e.element,'[role="menuitemradio"][aria-checked="true"]');L(o,(o=>{Xe(o,t.element)||e.getSystem().getByDom(o).each((e=>{Yp.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),vh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const r=vh(e,t,o,n);return M.some([n].concat(r))})))).getOr([]),yh=e=>"prepared"===e.type?M.some(e.menu):M.none(),xh=()=>{const e=xr({}),t=xr({}),o=xr({}),n=Ul(),r=xr({}),s=e=>a(e).bind(yh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(s,a,i,l)=>{n.set(s),e.set(i),t.set(a),r.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{L(e,(e=>{o[e]=t}))}));const n=t,r=de(t,((e,t)=>({k:e,v:t}))),s=ce(r,((e,t)=>[t].concat(vh(o,n,r,t))));return ce(o,(e=>be(s,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?M.some(e.slice(1)):M.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=r.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(s),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,r)=>{const a=U(i(t).toArray(),(e=>s(e).isSome()));return be(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return M.none();t.push(n.getOrDie())}return M.some(t)})(X(o,((t,a)=>((t,o,n)=>s(t).bind((r=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:r,triggeringItem:e,triggeringPath:n}))))))))(t,r,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[M.none()]),(e=>[M.some(e)])))))}))}}},wh=yh,Sh=Qs("tiered-menu-item-highlight"),kh=Qs("tiered-menu-item-dehighlight");var Ch;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(Ch||(Ch={}));const Oh=y("collapse-item"),_h=rm({name:"TieredMenu",configFields:[Ci("onExecute"),Ci("onEscape"),ki("onOpenMenu"),ki("onOpenSubmenu"),wi("onRepositionMenu"),wi("onCollapseMenu"),ur("highlightOnOpen",Ch.HighlightMenuAndItem),er("data",[Xn("primary"),Xn("menus"),Xn("expansions")]),ur("fakeFocus",!1),wi("onHighlightItem"),wi("onDehighlightItem"),wi("onHover"),vi(),Xn("dom"),ur("navigateOnHover",!0),ur("stayInDom",!1),nu("tmenuBehaviours",[Tp,Fm,cm,Np]),ur("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Ul(),n=xh(),r=e=>ou.getValue(e).value,s=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Fm.highlight,i=(t,o)=>{a(t,o),Fm.getHighlighted(o).orThunk((()=>Fm.getFirst(o))).each((n=>{e.fakeFocus?Fm.highlight(o,n):Ts(t,n.element,rs())}))},l=(e,t)=>we(z(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?M.some(e.menu):M.none()))))),c=(t,o,n)=>{const r=l(o,o.otherMenus(n));L(r,(o=>{Ra(o.element,[e.markers.backgroundMenu]),e.stayInDom||Np.remove(t,o)}))},d=(t,n)=>{const s=(t=>o.get().getOrThunk((()=>{const n={},s=Nc(t.element,`.${e.markers.item}`),a=U(s,(e=>"true"===xt(e,"aria-haspopup")));return L(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=r(e);n[t]=e}))})),o.set(n),n})))(t);le(s,((e,t)=>{const o=R(n,t);vt(e.element,"aria-expanded",o)}))},u=(t,o,n)=>M.from(n[0]).bind((r=>o.lookupMenu(r).bind((r=>{if("notbuilt"===r.type)return M.none();{const s=r.menu,a=l(o,n.slice(1));return L(a,(t=>{Da(t.element,e.markers.backgroundMenu)})),pt(s.element)||Np.append(t,Ya(s)),Ra(s.element,[e.markers.backgroundMenu]),i(t,s),c(t,o,n),M.some(s)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,s=m.HighlightSubmenu)=>{if(o.hasConfigured(km)&&km.isDisabled(o))return M.some(o);{const a=r(o);return n.expand(a).bind((r=>(d(t,r),M.from(r[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const r=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,r),r}return o.menu})(t,a,i);return pt(l.element)||Np.append(t,Ya(l)),e.onOpenSubmenu(t,o,l,Y(r)),s===m.HighlightSubmenu?(Fm.highlightFirst(l),u(t,n,r)):(Fm.dehighlightAll(l),M.some(o))})))))))}},p=(t,o)=>{const s=r(o);return n.collapse(s).bind((r=>(d(t,r),u(t,n,r).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>si(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(T))))),f=As([Fs(fh(),((e,t)=>{const o=t.event.item;n.lookupItem(r(o)).each((()=>{const o=t.event.menu;Fm.highlight(e,o);const s=r(t.event.item);n.refresh(s).each((t=>c(e,n,t)))}))})),js(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===r(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Ps(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,r)=>{const s=()=>bh.sketch({...n,value:r,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Os(e,Sh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Os(e,kh,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?tg():eg()});return r===o?{type:"prepared",menu:t.getSystem().build(s())}:{type:"notbuilt",nbMenu:s}})))(t,e.data.primary,e.data.menus),r=s();return n.setContents(e.data.primary,o,e.data.expansions,r),n.getPrimary()})(t).each((o=>{Np.append(t,Ya(o)),e.onOpenMenu(t,o),e.highlightOnOpen===Ch.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===Ch.HighlightJustMenu&&a(t,o)}))})),Fs(Sh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Fs(kh,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Fs(rh(),((t,o)=>{const s=o.event.item;((e,t)=>{const o=r(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,s),g(t,s,m.HighlightParent),e.onHover(t,s)}))]:[]]),v=e=>Fm.getHighlighted(e).bind(Fm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=r(e),o=fe(n.getMenus()),s=we(z(o,wh));return n.getTriggeringPath(t,(e=>((e,t,o)=>se(t,(e=>{if(!e.getSystem().isConnected())return M.none();const t=Fm.getCandidates(e);return G(t,(e=>r(e)===o))})))(0,s,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>M.from(e.components()[0]).filter((e=>"menu"===xt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:su(e.tmenuBehaviours,[Tp.config({mode:"special",onRight:h(((e,t)=>am(t.element)?M.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>am(t.element)?M.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Ts(e,t.element,rs())}))}}),Fm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),cm.config({find:e=>Fm.getHighlighted(e)}),Np.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Sr(e,t),expansions:{}}),collapseItem:e=>({value:Qs(Oh()),meta:{text:e}})}}),Th=rm({name:"InlineView",configFields:[Xn("lazySink"),wi("onShow"),wi("onHide"),lr("onEscape"),nu("inlineBehaviours",[Nd,ou,yl]),dr("fireDismissalEventInstead",[ur("event",fs())]),dr("fireRepositionEventInstead",[ur("event",bs())]),ur("getRelated",M.none),ur("isExtraPart",_),ur("eventOrder",M.none)],factory:(e,t)=>{const o=(e,t,o,r)=>{n(e,t,o,(()=>r.map((e=>$o(e)))))},n=(t,o,n,r)=>{const s=e.lazySink(t).getOrDie();Nd.openWhileCloaked(t,o,(()=>ud.positionWithinBounds(s,t,n,r()))),ou.setValue(t,M.some({mode:"position",config:n,getBounds:r}))},r=(t,o,n,r)=>{const s=((e,t,o,n,r)=>{const s=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>al(),onRtl:()=>il()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return _h.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Nd.close(t),e.onEscape.map((e=>e(t))),M.some(!0)),onExecute:()=>M.some(!0),onOpenMenu:(e,t)=>{ud.positionWithinBounds(s().getOrDie(),t,o,r())},onOpenSubmenu:(e,t,o,n)=>{const r=s().getOrDie();ud.position(r,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=s().getOrDie();ud.positionWithinBounds(a,t,o,r()),L(n,(e=>{const t=i(e.triggeringPath);ud.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,r);Nd.open(t,s),ou.setValue(t,M.some({mode:"menu",menu:s}))},s=t=>{Nd.isOpen(t)&&ou.getValue(t).each((o=>{switch(o.mode){case"menu":Nd.getState(t).each(_h.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();ud.positionWithinBounds(n,t,o.config,o.getBounds())}}))},a={setContent:(e,t)=>{Nd.setContent(e,t)},showAt:(e,t,n)=>{o(e,t,n,M.none())},showWithin:o,showWithinBounds:n,showMenuAt:(e,t,o)=>{r(e,t,o,M.none)},showMenuWithinBounds:r,hide:e=>{Nd.isOpen(e)&&(ou.setValue(e,M.none()),Nd.close(e))},getContent:e=>Nd.getState(e),reposition:s,isOpen:Nd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:su(e.inlineBehaviours,[Nd.config({isPartOf:(t,o,n)=>li(o,n)||((t,o)=>e.getRelated(t).exists((e=>li(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),ou.config({store:{mode:"memory",initialValue:M.none()}}),yl.config({channels:{...Pd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Wd({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:a}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithin:(e,t,o,n,r)=>{e.showWithin(t,o,n,r)},showWithinBounds:(e,t,o,n,r)=>{e.showWithinBounds(t,o,n,r)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,r)=>{e.showMenuWithinBounds(t,o,n,r)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Eh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Mh=rm({name:"Button",factory:e=>{const t=Zp(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:iu(e.buttonBehaviours,[Up.config({}),Tp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:n("role").getOr("button")}},eventOrder:e.eventOrder}},configFields:[ur("uid",void 0),Xn("dom"),ur("components",[]),au("buttonBehaviours",[Up,Tp]),nr("action"),nr("role"),ur("eventOrder",{})]}),Ah=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:aa("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Dh=tinymce.util.Tools.resolve("tinymce.util.I18n");const Bh={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Fh="temporary-placeholder",Ih=e=>()=>be(e,Fh).getOr("!not found!"),Rh=(e,t)=>{const o=e.toLowerCase();if(Dh.isRtl()){const e=((e,t)=>_e(e,t)?e:((e,t)=>e+"-rtl")(e))(o,"-rtl");return ve(t,e)?e:o}return o},Nh=(e,t)=>be(t,Rh(e,t)),Vh=(e,t)=>{const o=t();return Nh(e,o).getOrThunk(Ih(o))},Hh=()=>Vp("add-focusable",[Ps((e=>{ni(e.element,"svg").each((e=>vt(e,"focusable","false")))}))]),zh=(e,t,o,n)=>{var r,s;const a=(e=>!!Dh.isRtl()&&ve(Bh,e))(t)?["tox-icon--flip"]:[],i=be(o,Rh(t,o)).or(n).getOrThunk(Ih(o));return{dom:{tag:e.tag,attributes:null!==(r=e.attributes)&&void 0!==r?r:{},classes:e.classes.concat(a),innerHtml:i},behaviours:gl([...null!==(s=e.behaviours)&&void 0!==s?s:[],Hh()])}},Lh=(e,t,o,n=M.none())=>zh(t,e,o(),n),Ph={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Uh=rm({name:"Notification",factory:e=>{const t=Ah({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:gl([Np.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),r=Ah({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:gl([Np.config({})])}),s={updateProgress:(e,t)=>{e.getSystem().isConnected()&&r.getOpt(e).each((e=>{Np.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Np.set(n,[Ga(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>M.from(Ph[e]))).toArray()]),i=Ah(Mh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Lh("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),r=G(e,(e=>ve(n,Rh(e,n))));return zh({tag:"div",classes:["tox-notification__icon"]},r.getOr(Fh),n,M.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:gl([Np.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:gl([Up.config({}),Vp("notification-events",[Fs(Lr(),(e=>{i.getOpt(e).each(Up.focus)}))])]),components:c.concat(e.progress?[r.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:s}},configFields:[nr("level"),Xn("progress"),nr("icon"),Xn("onAction"),Xn("text"),Xn("iconProvider"),Xn("translationProvider"),fr("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var Wh,jh,Gh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),$h=tinymce.util.Tools.resolve("tinymce.EditorManager"),qh=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(Wh||(Wh={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(jh||(jh={}));const Xh=e=>t=>t.options.get(e),Kh=e=>t=>M.from(e(t)),Yh=e=>{const t=qh.deviceType.isPhone(),o=qh.deviceType.isTablet()||t,n=e.options.register,r=e=>s(e)||!1===e,a=e=>s(e)||h(e);n("skin",{processor:e=>s(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Gh.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>s(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||s(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),V(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:jh.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:r,default:"#top"}),n("anchor_bottom",{processor:r,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!qh.deviceType.isTouch()}),n("sidebar_show",{processor:"string"})},Jh=Xh("readonly"),Zh=Xh("height"),Qh=Xh("width"),ef=Kh(Xh("min_width")),tf=Kh(Xh("min_height")),of=Kh(Xh("max_width")),nf=Kh(Xh("max_height")),rf=Kh(Xh("style_formats")),sf=Xh("style_formats_merge"),af=Xh("style_formats_autohide"),lf=Xh("content_langs"),cf=Xh("removed_menuitems"),df=Xh("toolbar_mode"),uf=Xh("toolbar_groups"),mf=Xh("toolbar_location"),gf=Xh("fixed_toolbar_container"),pf=Xh("fixed_toolbar_container_target"),hf=Xh("toolbar_persist"),ff=Xh("toolbar_sticky_offset"),bf=Xh("menubar"),vf=Xh("toolbar"),yf=Xh("file_picker_callback"),xf=Xh("file_picker_validator_handler"),wf=Xh("file_picker_types"),Sf=Xh("typeahead_urls"),kf=Xh("anchor_top"),Cf=Xh("anchor_bottom"),Of=Xh("draggable_modal"),_f=Xh("statusbar"),Tf=Xh("elementpath"),Ef=Xh("branding"),Mf=Xh("resize"),Af=Xh("paste_as_text"),Df=Xh("sidebar_show"),Bf=Xh("promotion"),Ff=e=>!1===e.options.get("skin"),If=e=>!1!==e.options.get("menubar"),Rf=e=>{const t=e.options.get("skin_url");if(Ff(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return $h.baseURL+"/skins/ui/"+t}},Nf=e=>e.options.get("line_height_formats").split(" "),Vf=e=>{const t=vf(e),o=s(t),n=l(t)&&t.length>0;return!zf(e)&&(n||o||!0===t)},Hf=e=>{const t=V(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,s);return ke(o.length>0,o)},zf=e=>Hf(e).fold((()=>{const t=vf(e);return f(t,s)&&t.length>0}),T),Lf=e=>mf(e)===jh.bottom,Pf=e=>{var t;if(!e.inline)return M.none();const o=null!==(t=gf(e))&&void 0!==t?t:"";if(o.length>0)return ri(ht(),o);const n=pf(e);return g(n)?M.some(Ie(n)):M.none()},Uf=e=>e.inline&&Pf(e).isSome(),Wf=e=>Pf(e).getOrThunk((()=>ut(dt(Ie(e.getElement()))))),jf=e=>e.inline&&!If(e)&&!Vf(e)&&!zf(e),Gf=e=>(e.options.get("toolbar_sticky")||e.inline)&&!Uf(e)&&!jf(e),$f=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var qf=Object.freeze({__proto__:null,get ToolbarMode(){return Wh},get ToolbarLocation(){return jh},register:Yh,getSkinUrl:Rf,isReadOnly:Jh,isSkinDisabled:Ff,getHeightOption:Zh,getWidthOption:Qh,getMinWidthOption:ef,getMinHeightOption:tf,getMaxWidthOption:of,getMaxHeightOption:nf,getUserStyleFormats:rf,shouldMergeStyleFormats:sf,shouldAutoHideStyleFormats:af,getLineHeightFormats:Nf,getContentLanguages:lf,getRemovedMenuItems:cf,isMenubarEnabled:If,isMultipleToolbars:zf,isToolbarEnabled:Vf,isToolbarPersist:hf,getMultipleToolbarsOption:Hf,getUiContainer:Wf,useFixedContainer:Uf,getToolbarMode:df,isDraggableModal:Of,isDistractionFree:jf,isStickyToolbar:Gf,getStickyToolbarOffset:ff,getToolbarLocation:mf,isToolbarLocationBottom:Lf,getToolbarGroups:uf,getMenus:$f,getMenubar:bf,getToolbar:vf,getFilePickerCallback:yf,getFilePickerTypes:wf,useTypeaheadUrls:Sf,getAnchorTop:kf,getAnchorBottom:Cf,getFilePickerValidatorHandler:xf,useStatusBar:_f,useElementPath:Tf,promotionEnabled:Bf,useBranding:Ef,getResize:Mf,getPasteAsText:Af,getSidebarShow:Df});const Xf="[data-mce-autocompleter]",Kf=e=>si(e,Xf);var Yf;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(Yf||(Yf={}));var Jf=Yf;const Zf="tox-menu-nav__js",Qf="tox-collection__item",eb={normal:Zf,color:"tox-swatch"},tb="tox-collection__item--enabled",ob="tox-collection__item-icon",nb="tox-collection__item-label",rb="tox-collection__item-caret",sb="tox-collection__item--active",ab="tox-collection__item-container",ib="tox-collection__item-container--row",lb=e=>be(eb,e).getOr(Zf),cb=e=>"color"===e?"tox-swatches":"tox-menu",db=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:cb(e),tieredMenu:"tox-tiered-menu"}),ub=e=>{const t=db(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:lb(e)}},mb=(e,t,o)=>{const n=db(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},gb=[bh.parts.items({})],pb=(e,t,o)=>{const n=db(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:ub(o)}},hb=y([nr("data"),ur("inputAttributes",{}),ur("inputStyles",{}),ur("tag","input"),ur("inputClasses",[]),wi("onSetValue"),ur("styles",{}),ur("eventOrder",{}),nu("inputBehaviours",[ou,Up]),ur("selectOnFocus",!0)]),fb=e=>gl([Up.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Na(t);t.dom.setSelectionRange(0,o.length)}:b})]),bb=e=>({...fb(e),...su(e.inputBehaviours,[ou.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Na(e.element),setValue:(e,t)=>{Na(e.element)!==t&&Va(e.element,t)}},onSetValue:e.onSetValue})])}),vb=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),yb=rm({name:"Input",configFields:hb(),factory:(e,t)=>({uid:e.uid,dom:vb(e),components:[],behaviours:bb(e),eventOrder:e.eventOrder})}),xb=Qs("refetch-trigger-event"),wb=Qs("redirect-menu-item-interaction"),Sb=e=>ri(e.element,".tox-menu__searcher").bind((t=>e.getSystem().getByDom(t).toOptional())),kb=Sb,Cb=e=>({fetchPattern:ou.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),Ob=e=>{const t=(e,t)=>(t.cut(),M.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Os(e,wb,o),M.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[Qf]},components:[yb.sketch({inputClasses:["tox-menu__searcher","tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:gl([Vp(n,[Fs(jr(),(e=>{Cs(e,xb)})),Fs(Ur(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Tp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Tp.name()]}})]}},_b="tox-collection--results__js",Tb=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Qs("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Eb=(e,t)=>o=>{const n=H(o,t);return z(n,(t=>({dom:e,components:t})))},Mb=(e,t)=>{const o=[];let n=[];return L(e,((e,r)=>{t(e,r)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),z(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Ab=(e,t,o)=>bh.parts.items({preprocess:n=>{const r=z(n,o);return"auto"!==e&&e>1?Eb({tag:"div",classes:["tox-collection__group"]},e)(r):Mb(r,((e,o)=>"separator"===t[o].type))}}),Db=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Ab(e,t,x)]}),Bb=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),Fb=e=>(console.error(Wn(e)),console.log(e),M.none()),Ib=(e,t,o,n,r)=>{const s=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[bh.parts.items({preprocess:e=>Mb(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:s.dom,components:s.components,items:o}},Rb=(e,t,o,n,r)=>{if("color"===r.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[bh.parts.items({preprocess:"auto"!==e?Eb({tag:"div",classes:["tox-swatches__row"]},e):x})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===r.menuType&&"auto"===n){const t=Db(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===r.menuType||"searchable"===r.menuType){const t="searchable"!==r.menuType?Db(n,o):"search-with-field"===r.searchMode.searchMode?((e,t,o)=>{const n=Qs("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Ob({i18n:Dh.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],_b],attributes:{id:n}},components:[Ab(e,t,Tb)]}]}})(n,o,r.searchMode):((e,t,o=!0)=>{const n=Qs("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",_b].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Ab(e,t,Tb)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===r.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[bh.parts.items({preprocess:Eb({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:mb(t,n,r.menuType),components:gb,items:o}},Nb=Jn("type"),Vb=Jn("name"),Hb=Jn("label"),zb=Jn("text"),Lb=Jn("title"),Pb=Jn("icon"),Ub=Jn("value"),Wb=Qn("fetch"),jb=Qn("getSubmenuItems"),Gb=Qn("onAction"),$b=Qn("onItemAction"),qb=br("onSetup",(()=>b)),Xb=ar("name"),Kb=ar("text"),Yb=ar("icon"),Jb=ar("tooltip"),Zb=ar("label"),Qb=ar("shortcut"),ev=lr("select"),tv=fr("active",!1),ov=fr("borderless",!1),nv=fr("enabled",!0),rv=fr("primary",!1),sv=e=>ur("columns",e),av=ur("meta",{}),iv=br("onAction",b),lv=e=>pr("type",e),cv=e=>Gn("name","name",un((()=>Qs(`${e}-name`))),Bn),dv=Cn([Nb,Kb]),uv=Cn([lv("autocompleteitem"),tv,nv,av,Ub,Kb,Yb]),mv=[nv,Jb,Yb,Kb,qb],gv=Cn([Nb,Gb].concat(mv)),pv=e=>Ln("toolbarbutton",gv,e),hv=[tv].concat(mv),fv=Cn(hv.concat([Nb,Gb])),bv=e=>Ln("ToggleButton",fv,e),vv=[br("predicate",_),hr("scope","node",["node","editor"]),hr("position","selection",["node","selection","line"])],yv=mv.concat([lv("contextformbutton"),rv,Gb,$n("original",x)]),xv=hv.concat([lv("contextformbutton"),rv,Gb,$n("original",x)]),wv=mv.concat([lv("contextformbutton")]),Sv=hv.concat([lv("contextformtogglebutton")]),kv=jn("type",{contextformbutton:yv,contextformtogglebutton:xv}),Cv=Cn([lv("contextform"),br("initValue",y("")),Zb,or("commands",kv),rr("launch",jn("type",{contextformbutton:wv,contextformtogglebutton:Sv}))].concat(vv)),Ov=Cn([lv("contexttoolbar"),Jn("items")].concat(vv)),_v=[Nb,Jn("src"),ar("alt"),vr("classes",[],Bn)],Tv=Cn(_v),Ev=[Nb,zb,Xb,vr("classes",["tox-collection__item-label"],Bn)],Mv=Cn(Ev),Av=wn((()=>Vn("type",{cardimage:Tv,cardtext:Mv,cardcontainer:Dv}))),Dv=Cn([Nb,pr("direction","horizontal"),pr("align","left"),pr("valign","middle"),or("items",Av)]),Bv=[nv,Kb,Qb,("menuitem",Gn("value","value",un((()=>Qs("menuitem-value"))),Mn())),av];const Fv=Cn([Nb,Zb,or("items",Av),qb,iv].concat(Bv)),Iv=Cn([Nb,tv,Yb].concat(Bv)),Rv=[Nb,Jn("fancytype"),iv],Nv=[ur("initData",{})].concat(Rv),Vv=[yr("initData",{},[fr("allowCustomColors",!0),pr("storageKey","default"),cr("colors",Mn())])].concat(Rv),Hv=jn("fancytype",{inserttable:Nv,colorswatch:Vv}),zv=Cn([Nb,qb,iv,Yb].concat(Bv)),Lv=Cn([Nb,jb,qb,Yb].concat(Bv)),Pv=Cn([Nb,Yb,tv,qb,Gb].concat(Bv)),Uv=(e,t,o)=>{const n=Nc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,r=n[0].dom.getBoundingClientRect().top;return Math.abs(o-r)>t})).getOr(n.length);return M.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return M.none()},Wv=e=>((e,t)=>gl([Vp(e,t)]))(Qs("unnamed-events"),e),jv=Qs("tooltip.exclusive"),Gv=Qs("tooltip.show"),$v=Qs("tooltip.hide"),qv=(e,t,o)=>{e.getSystem().broadcastOn([jv],{})};var Xv=Object.freeze({__proto__:null,hideAllExclusive:qv,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Np.set(e,n)}))}}),Kv=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{wd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return As(q([[Fs(Gv,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){qv(o);const n=e.lazySink(o).getOrDie(),r=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:As("normal"===e.mode?[Fs(zr(),(e=>{Cs(o,Gv)})),Fs(Vr(),(e=>{Cs(o,$v)}))]:[]),behaviours:gl([Np.config({})])});t.setTooltip(r),vd(n,r),e.onShow(o,r),ud.position(n,r,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Fs($v,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Fs(os(),((e,t)=>{const n=t;n.universal||R(n.channels,jv)&&o(e)})),Us((e=>{o(e)}))],"normal"===e.mode?[Fs(Lr(),(e=>{Cs(e,Gv)})),Fs(es(),(e=>{Cs(e,$v)})),Fs(zr(),(e=>{Cs(e,Gv)})),Fs(Vr(),(e=>{Cs(e,$v)}))]:[Fs(Ss(),((e,t)=>{Cs(e,Gv)})),Fs(ks(),(e=>{Cs(e,$v)}))]]))}}),Yv=[Xn("lazySink"),Xn("tooltipDom"),ur("exclusive",!0),ur("tooltipComponents",[]),ur("delay",300),hr("mode","normal",["normal","follow-highlight"]),ur("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([Qi,Zi,Xi,Yi,Ki,Ji]),onRtl:y([Qi,Zi,Xi,Yi,Ki,Ji])}}))),wi("onHide"),wi("onShow")];const Jv=hl({fields:Yv,name:"tooltipping",active:Kv,state:Object.freeze({__proto__:null,init:()=>{const e=Ul(),t=Ul(),o=()=>{e.on(clearTimeout)},n=y("not-implemented");return ba({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}}),apis:Xv}),Zv="silver.readonly",Qv=Cn([("readonly",Kn("readonly",Fn))]);const ey=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&L(n,(e=>{e.broadcastOn([Vd()],{target:o})})),L(n,(e=>{e.broadcastOn([Zv],{readonly:t})}))},ty=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&ey(t,!0)})),e.on("SwitchMode",(()=>ey(t,e.mode.isReadOnly()))),Jh(e)&&e.mode.set("readonly")},oy=()=>yl.config({channels:{[Zv]:{schema:Qv,onReceive:(e,t)=>{km.set(e,t.readonly)}}}}),ny=e=>km.config({disabled:e}),ry=e=>km.config({disabled:e,disableClass:"tox-tbtn--disabled"}),sy=e=>km.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),ay=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},iy=(e,t)=>Ps((o=>{ay(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),ly=(e,t)=>Us((o=>ay(e,o)(t.get()))),cy=(e,t)=>js(((o,n)=>{ay(e,o)(e.onAction),e.triggersSubmenu||t!==Jf.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Cs(o,is()),n.stop())})),dy={[ns()]:["disabling","alloy.base.behaviour","toggling","item-events"]},uy=we,my=(e,t,o,n)=>{const r=xr(b);return{type:"item",dom:t.dom,components:uy(t.optComponents),data:e.data,eventOrder:dy,hasSubmenu:e.triggersSubmenu,itemBehaviours:gl([Vp("item-events",[cy(e,o),iy(e,r),ly(e,r)]),(s=()=>!e.enabled||n.isDisabled(),km.config({disabled:s,disableClass:"tox-collection__item--state-disabled"})),oy(),Np.config({})].concat(e.itemBehaviours))};var s},gy=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),py=e=>{const t=qh.os.isMacOS()||qh.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),r=z(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?r.join(""):r.join("+")},hy=(e,t,o=[ob])=>Lh(e,{tag:"div",classes:o},t),fy=e=>({dom:{tag:"div",classes:[nb]},components:[Ga(Dh.translate(e))]}),by=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),vy=(e,t)=>({dom:{tag:"div",classes:[nb]},components:[{dom:{tag:e.tag,styles:e.styles},components:[Ga(Dh.translate(t))]}]}),yy=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[Ga(py(e))]}),xy=e=>hy("checkmark",e,["tox-collection__item-checkmark"]),wy=e=>{const t=e.map((e=>({attributes:{title:Dh.translate(e)}}))).getOr({});return{tag:"div",classes:[Zf,Qf],...t}},Sy=(e,t,o,n=M.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,r=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Nh(e,n).or(o).getOrThunk(Ih(n))})(e,t.icons,o)));return{dom:(()=>{const e=s.getOr(""),o=n.map((e=>({title:t.translate(e)}))).getOr({}),a={tag:"div",attributes:o,classes:["tox-swatch"]};return"custom"===r?{...a,tag:"button",classes:[...a.classes,"tox-swatches__picker-btn"],innerHtml:e}:"remove"===r?{...a,classes:[...a.classes,"tox-swatch--remove"],innerHtml:e}:g(r)?{...a,attributes:{...a.attributes,"data-mce-color":r},styles:{"background-color":r},innerHtml:e}:a})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const r={tag:"div",classes:[ob]},s=o?e.iconContent.map((e=>Lh(e,r,t.icons,n))).orThunk((()=>M.some({dom:r}))):M.none(),a=e.checkMark,i=M.from(e.meta).fold((()=>fy),(e=>ve(e,"style")?S(vy,e.style):fy)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>M.some(by(e,[nb]))));return{dom:wy(e.ariaLabel),optComponents:[s,l,e.shortcutContent.map(yy),a,e.caret]}})(e,t,o,n),ky=(e,t)=>be(e,"tooltipWorker").map((e=>[Jv.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:Zl}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Jv.setComponents(t,[$a({element:Ie(e)})])}))}})])).getOr([]),Cy=(e,t)=>{const o=(e=>Gh.DOM.encode(e))(Dh.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Oy=(e,t)=>z(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":ib,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[ab,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Oy(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?M.from(t.cardText.matchText).getOr(""):"";return by(Cy(e.text,n),e.classes)}})),_y=Vu(dh(),uh()),Ty=e=>({value:e}),Ey=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,My=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Ay=e=>Ey.test(e)||My.test(e),Dy=e=>{return(t=e,((e,t)=>Ce(e,t,0))(t,"#")?((e,t)=>e.substring(t))(t,"#".length):t).toUpperCase();var t},By=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Fy=e=>{const t=By(e.red)+By(e.green)+By(e.blue);return Ty(t)},Iy=Math.min,Ry=Math.max,Ny=Math.round,Vy=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,Hy=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,zy=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),Ly=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},Py=e=>{let t,o,n;const r=(e.hue||0)%360;let s=e.saturation/100,a=e.value/100;if(s=Ry(0,Iy(s,1)),a=Ry(0,Iy(a,1)),0===s)return t=o=n=Ny(255*a),zy(t,o,n,1);const i=r/60,l=a*s,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=Ny(255*(t+d)),o=Ny(255*(o+d)),n=Ny(255*(n+d)),zy(t,o,n,1)},Uy=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Ey,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=My.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),r=parseInt(t[3],16);return zy(o,n,r,1)},Wy=(e,t,o,n)=>{const r=parseInt(e,10),s=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return zy(r,s,a,i)},jy=e=>{if("transparent"===e)return M.some(zy(0,0,0,0));const t=Vy.exec(e);if(null!==t)return M.some(Wy(t[1],t[2],t[3],"1"));const o=Hy.exec(e);return null!==o?M.some(Wy(o[1],o[2],o[3],o[4])):M.none()},Gy=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,$y=zy(255,0,0,1),qy=(e,t)=>{e.dispatch("ResizeContent",t)},Xy=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t});var Ky=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const Yy={},Jy=e=>be(Yy,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=Ky.getItem(t);if(m(o)){const e=Ky.getItem("tinymce-custom-colors");Ky.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=Ky.getItem(e),n=s(o)?JSON.parse(o):[],r=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{r.splice(e,1)};return{add:o=>{I(r,o).each(i),r.unshift(o),r.length>t&&r.pop(),Ky.setItem(e,JSON.stringify(r))},state:()=>r.slice(0)}})(t,10);return Yy[e]=n,n})),Zy=(e,t)=>{Jy(e).add(t)},Qy=(e,t,o)=>({hue:e,saturation:t,value:o}),ex=e=>{let t=0,o=0,n=0;const r=e.red/255,s=e.green/255,a=e.blue/255,i=Math.min(r,Math.min(s,a)),l=Math.max(r,Math.max(s,a));return i===l?(n=i,Qy(0,0,100*n)):(t=r===i?3:a===i?1:5,t=60*(t-(r===i?s-a:a===i?r-s:a-r)/(l-i)),o=(l-i)/l,n=l,Qy(Math.round(t),Math.round(100*o),Math.round(100*n)))},tx=e=>Fy(Py(e)),ox=e=>{return(t=e,Ay(t)?M.some({value:Dy(t)}):M.none()).orThunk((()=>jy(e).map(Fy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,r=n[0],s=n[1],a=n[2],i=n[3];return Fy(zy(r,s,a,i))}));var t},nx="forecolor",rx="hilitecolor",sx=e=>Math.max(5,Math.ceil(Math.sqrt(e))),ax=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+ox(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},ix=e=>t=>t.options.get(e),lx="#000000",cx=(e,t)=>t===nx?ix("color_cols_foreground")(e):t===rx?ix("color_cols_background")(e):ix("color_cols")(e),dx=ix("custom_colors"),ux=(e,t)=>t===nx&&e.options.isSet("color_map_foreground")?ix("color_map_foreground")(e):t===rx&&e.options.isSet("color_map_background")?ix("color_map_background")(e):ix("color_map")(e),mx=ix("color_default_foreground"),gx=ix("color_default_background"),px=(e,t)=>{const o=Mt(Ie(e.selection.getStart()),"hilitecolor"===t?"background-color":"color");return jy(o).map((e=>"#"+Fy(e).value))},hx=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},fx=(e,t,o,n)=>{"custom"===o?Sx(e)((o=>{o.each((o=>{Zy(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),px(e,t).getOr(lx)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},bx=(e,t,o)=>e.concat((e=>z(Jy(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(hx(o))),vx=(e,t,o)=>n=>{n(bx(e,t,o))},yx=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},xx=(e,t,o,n,r)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:t=>{const n=px(e,o);return xe(n,t.toUpperCase())},columns:cx(e,o),fetch:vx(ux(e,o),o,dx(e)),onAction:t=>{fx(e,o,r.get(),b)},onItemAction:(n,s)=>{fx(e,o,s,(o=>{r.set(o),((e,t)=>{e.dispatch("TextColorChange",t)})(e,{name:t,color:o})}))},onSetup:o=>{yx(o,t,r.get());const n=e=>{e.name===t&&yx(o,e.name,e.color)};return e.on("TextColorChange",n),()=>{e.off("TextColorChange",n)}}})},wx=(e,t,o,n)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",initData:{storageKey:o},onAction:t=>{fx(e,o,t.value,b)}}]})},Sx=e=>(t,o)=>{let n=!1;const r={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:r,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const r=o.getData().colorpicker;n?(t(M.from(r)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",r]))},onClose:b,onCancel:()=>{t(M.none())}})},kx=(e,t,o,n,r,s,a,i)=>{const l=Bb(t),c=Cx(t,o,n,"color"!==r?"normal":"color",s,a,i);return Rb(e,l,c,n,{menuType:r})},Cx=(e,t,o,n,r,s,a)=>we(z(e,(i=>{return"choiceitem"===i.type?(l=i,Ln("choicemenuitem",Iv,l)).fold(Fb,(i=>M.some(((e,t,o,n,r,s,a,i=!0)=>{const l=Sy({presets:o,textContent:t?e.text:M.none(),htmlContent:M.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:M.none(),checkMark:t?M.some(xy(a.icons)):M.none(),caret:M.none(),value:e.value},a,i);return cn(my({data:gy(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(r),b),triggersSubmenu:!1,itemBehaviours:[]},l,s,a),{toggling:{toggleClass:tb,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,s(i.value),r,a,Bb(e))))):M.none();var l}))),Ox=(e,t)=>{const o=ub(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?ri(e.element,"[aria-checked=true]"):M.none()}},_x=Qs("cell-over"),Tx=Qs("cell-execute"),Ex=(e,t,o)=>{const n=o=>Os(o,Tx,{row:e,col:t}),r=(e,t)=>{t.stop(),n(e)};return Ka({dom:{tag:"div",attributes:{role:"button","aria-labelledby":o}},behaviours:gl([Vp("insert-table-picker-cell",[Fs(zr(),Up.focus),Fs(ns(),n),Fs($r(),r),Fs(ss(),r)]),Yp.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Up.config({onFocus:o=>Os(o,_x,{row:e,col:t})})])})},Mx=e=>X(e,(e=>z(e,Ya))),Ax=(e,t)=>Ga(`${t}x${e}`),Dx={inserttable:e=>{const t=Qs("size-label"),o=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++)o.push(Ex(t,n,e));n.push(o)}return n})(t),n=Ax(0,0),r=Ah({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:gl([Np.config({})])});return{type:"widget",data:{value:Qs("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[_y.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:Mx(o).concat(r.asSpec()),behaviours:gl([Vp("insert-table-picker",[Ps((e=>{Np.set(r.get(e),[n])})),Vs(_x,((e,t,n)=>{const{row:s,col:a}=n.event;((e,t,o,n,r)=>{for(let n=0;n<10;n++)for(let r=0;r<10;r++)Yp.set(e[n][r],n<=t&&r<=o)})(o,s,a),Np.set(r.get(e),[Ax(s+1,a+1)])})),Vs(Tx,((t,o,n)=>{const{row:r,col:s}=n.event;e.onAction({numRows:r+1,numColumns:s+1}),Cs(t,is())}))]),Tp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>bx(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(hx(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),r="color",s={...kx(Qs("menu-value"),o,(t=>{e.onAction({value:t})}),n,r,Jf.CLOSE_ON_EXECUTE,_,t.shared.providers),markers:ub(r),movement:Ox(n,r)};return{type:"widget",data:{value:Qs("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[_y.widget(bh.sketch(s))]}}},Bx=e=>({type:"separator",dom:{tag:"div",classes:[Qf,"tox-collection__group-heading"]},components:e.text.map(Ga).toArray()});var Fx=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),Ix=[Kn("others",zn(Jo.value,Mn()))],Rx=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=y({});return ba({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),M.none()))),getOrCreate:(o,n,r)=>t(n,r).getOrThunk((()=>{const t=be(n.others,r).getOrDie("No information found for coupled component: "+r)(o),s=o.getSystem().build(t);return e[r]=s,s}))})}});const Nx=hl({fields:Ix,name:"coupling",apis:Fx,state:Rx}),Vx=e=>{let t=M.none(),o=[];const n=e=>{r()?s(e):o.push(e)},r=()=>t.isSome(),s=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{r()||(t=M.some(e),L(o,s),o=[])})),{get:n,map:e=>Vx((t=>{n((o=>{t(e(o))}))})),isReady:r}},Hx={nu:Vx,pure:e=>Vx((t=>{t(e)}))},zx=e=>{setTimeout((()=>{throw e}),0)},Lx=e=>{const t=t=>{e().then(t,zx)};return{map:t=>Lx((()=>e().then(t))),bind:t=>Lx((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>Lx((()=>e().then((()=>t.toPromise())))),toLazy:()=>Hx.nu(t),toCached:()=>{let t=null;return Lx((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Px=e=>Lx((()=>new Promise(e))),Ux=e=>Lx((()=>Promise.resolve(e))),Wx=y("sink"),jx=y(Bu({name:Wx(),overrides:y({dom:{tag:"div"},behaviours:gl([ud.config({useFixed:T})]),events:As([Hs(Ur()),Hs(Rr()),Hs($r())])})})),Gx=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",r=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:r})),(e=>({type:n,hotspot:o,overrides:r,layouts:e})))},$x=(e,t,o,n,r,s,a)=>{const i=((e,t,o,n,r,s,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=Kx(n,e);return i.map((e=>e.bind((e=>M.from(_h.sketch({...s.menu(),uid:aa(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();ud.position(n,t,{anchor:o}),Nd.decloak(r)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();ud.position(n,o,{anchor:{type:"submenu",item:t}}),Nd.decloak(r)},onRepositionMenu:(e,t,n)=>{const r=l().getOrDie();ud.position(r,t,{anchor:o}),L(n,(e=>{ud.position(r,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Up.focus(n),Nd.close(r),M.some(!0))}))))))})(e,t,Gx(e,o),o,n,r,a);return i.map((e=>(e.fold((()=>{Nd.isOpen(n)&&Nd.close(n)}),(e=>{Nd.cloak(n),Nd.open(n,e),s(n)})),n)))},qx=(e,t,o,n,r,s,a)=>(Nd.close(n),Ux(n)),Xx=(e,t,o,n,r,s)=>{const a=Nx.getCoupled(o,"sandbox");return(Nd.isOpen(a)?qx:$x)(e,t,o,a,n,r,s)},Kx=(e,t)=>e.getSystem().getByUid(t.uid+"-"+Wx()).map((e=>()=>Jo.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>Jo.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),Yx=e=>{Nd.getState(e).each((e=>{_h.repositionMenus(e)}))},Jx=(e,t,o)=>{const n=ii(),r=Kx(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:iu(e.sandboxBehaviours,[ou.config({store:{mode:"memory",initialValue:t}}),Nd.config({onOpen:(r,s)=>{const a=Gx(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=cm.getCurrent(t).getOr(t),r=$t(e.element);o?_t(n.element,"min-width",r+"px"):((e,t)=>{Gt.set(e,t)})(n.element,r)})(a.hotspot,s,e.useMinWidth),e.onOpen(a,r,s),void 0!==o&&void 0!==o.onOpen&&o.onOpen(r,s)},onClose:(e,r)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>li(o,n)||li(t,n),getAttachPoint:()=>r().getOrDie()}),cm.config({find:e=>Nd.getState(e).bind((e=>cm.getCurrent(e)))}),yl.config({channels:{...Pd({isExtraPart:_}),...Wd({doReposition:Yx})}})])}},Zx=e=>{const t=Nx.getCoupled(e,"sandbox");Yx(t)},Qx=()=>[ur("sandboxClasses",[]),au("sandboxBehaviours",[cm,yl,Nd,ou])],ew=y([Xn("dom"),Xn("fetch"),wi("onOpen"),Si("onExecute"),ur("getHotspot",M.some),ur("getAnchorOverrides",y({})),dc(),nu("dropdownBehaviours",[Yp,Nx,Tp,Up]),Xn("toggleClass"),ur("eventOrder",{}),nr("lazySink"),ur("matchWidth",!1),ur("useMinWidth",!1),nr("role")].concat(Qx())),tw=y([Du({schema:[vi(),ur("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),jx()]),ow=sm({name:"Dropdown",configFields:ew(),partFields:tw(),factory:(e,t,o,n)=>{const r=e=>{Nd.getState(e).each((e=>{_h.highlightPrimary(e)}))},s=(t,o,r)=>Xx(e,x,t,n,o,r),a={expand:e=>{Yp.isOn(e)||s(e,b,Ch.HighlightNone).get(b)},open:e=>{Yp.isOn(e)||s(e,b,Ch.HighlightMenuAndItem).get(b)},refetch:t=>Nx.getExistingCoupled(t,"sandbox").fold((()=>s(t,b,Ch.HighlightMenuAndItem).map(b)),(o=>$x(e,x,t,o,n,b,Ch.HighlightMenuAndItem).map(b))),isOpen:Yp.isOn,close:e=>{Yp.isOn(e)&&s(e,b,Ch.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Yp.isOn(e)&&Zx(e)}},i=(e,t)=>(_s(e),M.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:su(e.dropdownBehaviours,[Yp.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),Nx.config({others:{sandbox:t=>Jx(e,t,{onOpen:()=>Yp.on(t),onClose:()=>Yp.off(t)})}}),Tp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(ow.isOpen(e)){const t=Nx.getCoupled(e,"sandbox");r(t)}else ow.open(e);return M.some(!0)},onEscape:(e,t)=>ow.isOpen(e)?(ow.close(e),M.some(!0)):M.none()}),Up.config({})]),events:Zp(M.some((e=>{s(e,r,Ch.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[ns()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),nw=(e,t,o)=>{kb(e).each((e=>{var n;((e,t)=>{wt(t.element,"id").each((t=>vt(e.element,"aria-activedescendant",t)))})(e,o),(Fa((n=t).element,_b)?M.some(n.element):ri(n.element,"."+_b)).each((t=>{wt(t,"id").each((t=>vt(e.element,"aria-controls",t)))}))})),vt(o.element,"aria-selected","true")},rw=(e,t,o)=>{vt(o.element,"aria-selected","false")},sw=e=>Nx.getExistingCoupled(e,"sandbox").bind(Sb).map(Cb).map((e=>e.fetchPattern)).getOr("");var aw;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(aw||(aw={}));const iw=(e,t,o,n,r)=>{const s=o.shared.providers,a=e=>r?{...e,shortcut:M.none(),icon:e.text.isSome()?M.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Ln("menuitem",zv,i)).fold(Fb,(e=>M.some(((e,t,o,n=!0)=>{const r=Sy({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:M.none(),ariaLabel:e.text,caret:M.none(),checkMark:M.none(),shortcutContent:e.shortcut},o,n);return my({data:gy(e),getApi:e=>({isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t,o)})(a(e),t,s,n))));case"nestedmenuitem":return(e=>Ln("nestedmenuitem",Lv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n=!0,r=!1)=>{const s=r?(a=o.icons,hy("chevron-down",a,[rb])):(e=>hy("chevron-right",e,[rb]))(o.icons);var a;const i=Sy({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:M.none(),ariaLabel:e.text,caret:M.some(s),checkMark:M.none(),shortcutContent:e.shortcut},o,n);return my({data:gy(e),getApi:e=>({isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t)}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,s,n,r))));case"togglemenuitem":return(e=>Ln("togglemenuitem",Pv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n=!0)=>{const r=Sy({iconContent:e.icon,textContent:e.text,htmlContent:M.none(),ariaLabel:e.text,checkMark:M.some(xy(o.icons)),caret:M.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return cn(my({data:gy(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t,o),{toggling:{toggleClass:tb,toggleOnExecute:!1,selected:e.active}})})(a(e),t,s,n))));case"separator":return(e=>Ln("separatormenuitem",dv,e))(e).fold(Fb,(e=>M.some(Bx(e))));case"fancymenuitem":return(e=>Ln("fancymenuitem",Hv,e))(e).fold(Fb,(e=>((e,t)=>be(Dx,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),M.none()}var i},lw=(e,t,o,n,r,s,a)=>{const i=1===n,l=!i||Bb(e);return we(z(e,(e=>{switch(e.type){case"separator":return(n=e,Ln("Autocompleter.Separator",dv,n)).fold(Fb,(e=>M.some(Bx(e))));case"cardmenuitem":return(e=>Ln("cardmenuitem",Fv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n)=>{const r={dom:wy(e.label),optComponents:[M.some({dom:{tag:"div",classes:[ab,ib]},components:Oy(e.items,n)})]};return my({data:gy({text:M.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!km.isDisabled(e),setEnabled:t=>{km.set(e,!t),L(Nc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(km)&&km.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:M.from(n.itemBehaviours).getOr([])},r,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},r,s,{itemBehaviours:ky(e.meta,s),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Ln("Autocompleter.Item",uv,e))(e).fold(Fb,(e=>M.some(((e,t,o,n,r,s,a,i=!0)=>{const l=Sy({presets:n,textContent:M.none(),htmlContent:o?e.text.map((e=>Cy(e,t))):M.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:M.none(),checkMark:M.none(),caret:M.none(),value:e.value},a.providers,i,e.icon);return my({data:gy(e),enabled:e.enabled,getApi:y({}),onAction:t=>r(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:ky(e.meta,a)},l,s,a.providers)})(e,t,i,"normal",o,r,s,l))))}var n})))},cw=(e,t,o,n,r,s)=>{const a=Bb(t),i=we(z(t,(e=>{const t=e=>iw(e,o,n,(e=>r?!ve(e,"text"):a)(e),r);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(s);return(r?Ib:Rb)(e,a,i,1,l)},dw=e=>_h.singleData(e.value,e),uw=(e,t)=>{const o=xr(!1),n=xr(!1),r=Ka(Th.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:gl([Vp("dismissAutocompleter",[Fs(fs(),(()=>c()))])]),lazySink:t.getSink})),s=()=>Th.isOpen(r),a=n.get,i=()=>{s()&&Th.hide(r)},l=()=>Th.getContent(r).bind((e=>te(e.components(),0))),c=()=>e.execCommand("mceAutocompleterClose"),d=n=>{const s=(n=>{const r=se(n,(e=>M.from(e.columns))).getOr(1);return X(n,(n=>{const s=n.items;return lw(s,n.matchText,((t,r)=>{const s=e.selection.getRng();((e,t)=>Kf(Ie(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,s).each((s=>{const a={hide:()=>c(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,s,t,r),o.set(!1)}))}),r,Jf.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);s.length>0?((t,o)=>{var n;(n=Ie(e.getBody()),ri(n,Xf)).each((n=>{const s=se(t,(e=>M.from(e.columns))).getOr(1);Th.showMenuAt(r,{anchor:{type:"node",root:Ie(e.getBody()),node:M.from(n)}},((e,t,o,n)=>{const r=Ox(t,n),s=ub(n);return{data:dw({...e,movement:r,menuBehaviours:Wv("auto"!==t?[]:[Ps(((e,t)=>{Uv(e,4,s.item).each((({numColumns:t,numRows:o})=>{Tp.setGridSize(e,o,t)}))}))])}),menu:{markers:ub(n),fakeFocus:o===aw.ContentFocus}}})(Rb("autocompleter-value",!0,o,s,{menuType:"normal"}),s,aw.ContentFocus,"normal"))})),l().each(Fm.highlightFirst)})(n,s):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),d(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>d(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{Os(e,Ur(),{raw:t})},n=()=>e.getMenu().bind(Fm.getHighlighted);t.on("keydown",(t=>{const r=t.which;e.isActive()&&(e.isMenuOpen()?13===r?(n().each(_s),t.preventDefault()):40===r?(n().fold((()=>{e.getMenu().each(Fm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==r&&38!==r&&39!==r||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==r&&38!==r&&40!==r||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&Kf(Ie(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:c,isMenuOpen:s,isActive:a,isProcessingAction:o.get,getMenu:l},e)},mw=(e,t,o)=>si(e,t,o).isSome(),gw=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},pw=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?M.none():M.some(t.touches[0])},hw=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Ul(),o=xr(!1),n=gw((t=>{e.triggerEvent(as(),t),o.set(!0)}),400),r=kr([{key:Dr(),value:e=>(pw(e).each((r=>{n.cancel();const s={x:r.clientX,y:r.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(s)})),M.none())},{key:Br(),value:e=>(n.cancel(),pw(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),M.none())},{key:Fr(),value:r=>(n.cancel(),t.get().filter((e=>Xe(e.target,r.target))).map((t=>o.get()?(r.prevent(),!1):e.triggerEvent(ss(),r))))}]);return{fireIfReady:(e,t)=>be(r,t).bind((t=>t(e)))}})(o),r=z(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>jl(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),s=Ul(),a=jl(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),s.set(setTimeout((()=>{o.triggerEvent(ts(),e)}),0))})),i=jl(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Im[0]&&!R(["input","textarea"],ze(e.target))&&!mw(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=jl(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Ul(),d=jl(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(es(),e)}),0))}));return{unbind:()=>{L(r,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),s.on(clearTimeout),c.on(clearTimeout)}}},fw=(e,t)=>{const o=be(e,"target").getOr(t);return xr(o)},bw=wr([{stopped:[]},{resume:["element"]},{complete:[]}]),vw=(e,t,o,n,r,s)=>{const a=e(t,n),i=((e,t)=>{const o=xr(!1),n=xr(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,r);return a.fold((()=>(s.logEventNoHandlers(t,n),bw.complete())),(e=>{const o=e.descHandler;return xa(o)(i),i.isStopped()?(s.logEventStopped(t,e.element,o.purpose),bw.stopped()):i.isCut()?(s.logEventCut(t,e.element,o.purpose),bw.complete()):et(e.element).fold((()=>(s.logNoParent(t,e.element,o.purpose),bw.complete())),(n=>(s.logEventResponse(t,e.element,o.purpose),bw.resume(n))))}))},yw=(e,t,o,n,r,s)=>vw(e,t,o,n,r,s).fold(T,(n=>yw(e,t,o,n,r,s)),_),xw=(e,t,o,n,r)=>{const s=fw(o,n);return yw(e,t,o,n,s,r)},ww=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,r)=>{const s=void 0!==e[r]?e[r]:{};s[o]=((e,t)=>({cHandler:S.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[r]=s}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>_r(n,(t=>((e,t)=>sa(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{sa(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const r=(e=>{const t=e.element;return sa(t).getOrThunk((()=>((e,t)=>{const o=Qs(oa+"uid-");return ra(t,o),o})(0,e.element)))})(n);ye(t,r)&&((e,n)=>{const r=t[n];if(r!==e)throw new Error('The tagId "'+n+'" is already used by: '+Xs(r.element)+"\nCannot use it for: "+Xs(e.element)+"\nThe conflicting element is"+(pt(r.element)?" ":" not ")+"already in the DOM");o(e)})(n,r);const s=[n];e.registerId(s,r,n.events),t[r]=n},unregister:o,getById:e=>be(t,e)}},Sw=rm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:ru(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[ur("components",[]),nu("containerBehaviours",[]),ur("events",{}),ur("domModification",{}),ur("eventOrder",{})]}),kw=e=>{const t=t=>et(e.element).fold(T,(e=>Xe(t,e))),o=ww(),n=(e,n)=>o.find(t,e,n),r=hw(e.element,{triggerEvent:(e,t)=>mi(e,t.target,(o=>((e,t,o,n)=>xw(e,t,o,o.target,n))(n,e,t,o)))}),s={debugInfo:y("real"),triggerEvent:(e,t,o)=>{mi(e,t,(r=>xw(n,e,o,t,r)))},triggerFocus:(e,t)=>{sa(e).fold((()=>{wl(e)}),(o=>{mi(Qr(),e,(o=>(((e,t,o,n,r)=>{const s=fw(o,n);vw(e,t,o,n,s,r)})(n,Qr(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{s.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:Ka,buildOrPatch:Xa,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:T},a=e=>{e.connect(s),Ue(e.element)||(o.register(e),L(e.components(),a),s.triggerEvent(cs(),e.element,{target:e.element}))},i=e=>{Ue(e.element)||(L(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{vd(e,t)},c=e=>{wd(e)},d=e=>{const t=o.filter(os());L(t,(t=>{const o=t.descHandler;xa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=xr(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:_,event:e,setSource:C("Cannot set source of a broadcasted event"),getSource:C("Cannot get source of a broadcasted event")}})(t);return L(e,(e=>{const t=e.descHandler;xa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>Jo.error(new Error('Could not find component with uid: "'+e+'" in system.'))),Jo.value),h=e=>{const t=sa(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{r.unbind(),No(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},Cw=y([ur("prefix","form-field"),nu("fieldBehaviours",[cm,ou])]),Ow=y([Bu({schema:[Xn("dom")],name:"label"}),Bu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Xn("text")],name:"aria-descriptor"}),Au({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[Xn("factory")],name:"field"})]),_w=sm({name:"FormField",configFields:Cw(),partFields:Ow(),factory:(e,t,o,n)=>{const r=su(e.fieldBehaviours,[cm.config({find:t=>ju(t,e,"field")}),ou.config({store:{mode:"manual",getValue:e=>cm.getCurrent(e).bind(ou.getValue),setValue:(e,t)=>{cm.getCurrent(e).each((e=>{ou.setValue(e,t)}))}}})]),s=As([Ps(((t,o)=>{const n=$u(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Qs(e.prefix);n.label().each((e=>{vt(e.element,"for",o),vt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Qs(e.prefix);vt(o.element,"id",n),vt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>ju(t,e,"field"),getLabel:t=>ju(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:r,events:s,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var Tw=Object.freeze({__proto__:null,exhibit:(e,t)=>ya({attributes:kr([{key:t.tabAttr,value:"true"}])})}),Ew=[ur("tabAttr","data-alloy-tabstop")];const Mw=hl({fields:Ew,name:"tabstopping",active:Tw});var Aw=tinymce.util.Tools.resolve("tinymce.html.Entities");const Dw=(e,t,o,n)=>{const r=Bw(e,t,o,n);return _w.sketch(r)},Bw=(e,t,o,n)=>({dom:Fw(o),components:e.toArray().concat([t]),fieldBehaviours:gl(n)}),Fw=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),Iw=(e,t)=>_w.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.translate(e))]}),Rw=Qs("form-component-change"),Nw=Qs("form-close"),Vw=Qs("form-cancel"),Hw=Qs("form-action"),zw=Qs("form-submit"),Lw=Qs("form-block"),Pw=Qs("form-unblock"),Uw=Qs("form-tabchange"),Ww=Qs("form-resize"),jw=["input","textarea"],Gw=e=>{const t=ze(e);return R(jw,t)},$w=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Ba(o,t.invalidClass),t.notify.each((t=>{Gw(e.element)&&vt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{$s(e,t.validHtml)})),t.onValid(e)}))},qw=(e,t,o,n)=>{const r=t.getRoot(e).getOr(e.element);Da(r,t.invalidClass),t.notify.each((t=>{Gw(e.element)&&vt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{$s(e,n)})),t.onInvalid(e,n)}))},Xw=(e,t,o)=>t.validator.fold((()=>Ux(Jo.value(!0))),(t=>t.validate(e))),Kw=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),Xw(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(qw(e,t,0,o),Jo.error(o))),(o=>($w(e,t),Jo.value(o)))):Jo.error("No longer in system"))));var Yw=Object.freeze({__proto__:null,markValid:$w,markInvalid:qw,query:Xw,run:Kw,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Fa(o,t.invalidClass)}}),Jw=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>As([Fs(t.onEvent,(t=>{Kw(t,e).get(x)}))].concat(t.validateOnLoad?[Ps((t=>{Kw(t,e).get(b)}))]:[])))).getOr({})}),Zw=[Xn("invalidClass"),ur("getRoot",M.none),dr("notify",[ur("aria","alert"),ur("getContainer",M.none),ur("validHtml",""),wi("onValid"),wi("onInvalid"),wi("onValidate")]),dr("validator",[Xn("validate"),ur("onEvent","input"),ur("validateOnLoad",!0)])];const Qw=hl({fields:Zw,name:"invalidating",active:Jw,apis:Yw,extra:{validation:e=>t=>{const o=ou.getValue(t);return Ux(e(o))}}}),eS=hl({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>As([Ds(Yr(),T)]),exhibit:()=>ya({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),tS=Qs("color-input-change"),oS=Qs("color-swatch-change"),nS=Qs("color-picker-cancel"),rS=Bu({schema:[Xn("dom")],name:"label"}),sS=e=>Bu({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:As([Is(Dr(),((t,o,n)=>e(t,n)),[t]),Is(Rr(),((t,o,n)=>e(t,n)),[t]),Is(Nr(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),aS=sS("top-left"),iS=sS("top"),lS=sS("top-right"),cS=sS("right"),dS=sS("bottom-right"),uS=sS("bottom"),mS=sS("bottom-left");var gS=[rS,sS("left"),cS,iS,uS,aS,lS,mS,dS,Au({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:As([Ns(Dr(),e,"spectrum"),Ns(Br(),e,"spectrum"),Ns(Fr(),e,"spectrum"),Ns(Rr(),e,"spectrum"),Ns(Nr(),e,"spectrum"),Ns(Hr(),e,"spectrum")])})}),Au({schema:[$n("mouseIsDown",(()=>xr(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:gl([Tp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),Up.config({})]),events:As([Fs(Dr(),o),Fs(Br(),o),Fs(Rr(),o),Fs(Nr(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const pS=y("slider.change.value"),hS=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?M.some(e.touches[0]).map((e=>Pt(e.clientX,e.clientY))):M.none()}{const e=t;return void 0!==e.clientX?M.some(e).map((e=>Pt(e.clientX,e.clientY))):M.none()}},fS=e=>e.model.minX,bS=e=>e.model.minY,vS=e=>e.model.minX-1,yS=e=>e.model.minY-1,xS=e=>e.model.maxX,wS=e=>e.model.maxY,SS=e=>e.model.maxX+1,kS=e=>e.model.maxY+1,CS=(e,t,o)=>t(e)-o(e),OS=e=>CS(e,xS,fS),_S=e=>CS(e,wS,bS),TS=e=>OS(e)/2,ES=e=>_S(e)/2,MS=e=>e.stepSize,AS=e=>e.snapToGrid,DS=e=>e.snapStart,BS=e=>e.rounded,FS=(e,t)=>void 0!==e[t+"-edge"],IS=e=>FS(e,"left"),RS=e=>FS(e,"right"),NS=e=>FS(e,"top"),VS=e=>FS(e,"bottom"),HS=e=>e.model.value.get(),zS=(e,t)=>({x:e,y:t}),LS=(e,t)=>{Os(e,pS(),{value:t})},PS=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),US=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),WS=(e,t,o)=>Math.max(t,Math.min(o,e)),jS=e=>{const{min:t,max:o,range:n,value:r,step:s,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(r<u)return p;if(r>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(r,u,m),c=WS(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,r)=>r.fold((()=>{const r=e-t,s=Math.round(r/n)*n;return WS(t+s,t-1,o+1)}),(t=>{const r=(e-t)%n,s=Math.round(r/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+s)*n;return Math.max(t,l)})))(c,t,o,s,i):l?Math.round(c):c}},GS=e=>{const{min:t,max:o,range:n,value:r,hasMinEdge:s,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return r<t?s?0:c:r>o?a?i:d:(r-t)/n*l},$S="top",qS="right",XS="bottom",KS="left",YS=e=>e.element.dom.getBoundingClientRect(),JS=(e,t)=>e[t],ZS=e=>{const t=YS(e);return JS(t,KS)},QS=e=>{const t=YS(e);return JS(t,qS)},ek=e=>{const t=YS(e);return JS(t,$S)},tk=e=>{const t=YS(e);return JS(t,XS)},ok=e=>{const t=YS(e);return JS(t,"width")},nk=e=>{const t=YS(e);return JS(t,"height")},rk=(e,t,o)=>(e+t)/2-o,sk=(e,t)=>{const o=YS(e),n=YS(t),r=JS(o,KS),s=JS(o,qS),a=JS(n,KS);return rk(r,s,a)},ak=(e,t)=>{const o=YS(e),n=YS(t),r=JS(o,$S),s=JS(o,XS),a=JS(n,$S);return rk(r,s,a)},ik=(e,t)=>{Os(e,pS(),{value:t})},lk=(e,t,o)=>{const n={min:fS(t),max:xS(t),range:OS(t),value:o,step:MS(t),snap:AS(t),snapStart:DS(t),rounded:BS(t),hasMinEdge:IS(t),hasMaxEdge:RS(t),minBound:ZS(e),maxBound:QS(e),screenRange:ok(e)};return jS(n)},ck=e=>(t,o)=>((e,t,o)=>{const n=(e>0?US:PS)(HS(o),fS(o),xS(o),MS(o));return ik(t,n),M.some(n)})(e,t,o).map(T),dk=(e,t,o,n,r,s)=>{const a=((e,t,o,n,r)=>{const s=ok(e),a=n.bind((t=>M.some(sk(t,e)))).getOr(0),i=r.bind((t=>M.some(sk(t,e)))).getOr(s),l={min:fS(t),max:xS(t),range:OS(t),value:o,hasMinEdge:IS(t),hasMaxEdge:RS(t),minBound:ZS(e),minOffset:0,maxBound:QS(e),maxOffset:s,centerMinEdge:a,centerMaxEdge:i};return GS(l)})(t,s,o,n,r);return ZS(t)-ZS(e)+a},uk=ck(-1),mk=ck(1),gk=M.none,pk=M.none,hk={"top-left":M.none(),top:M.none(),"top-right":M.none(),right:M.some(((e,t)=>{LS(e,SS(t))})),"bottom-right":M.none(),bottom:M.none(),"bottom-left":M.none(),left:M.some(((e,t)=>{LS(e,vS(t))}))};var fk=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=lk(e,t,o);return ik(e,n),n},setToMin:(e,t)=>{const o=fS(t);ik(e,o)},setToMax:(e,t)=>{const o=xS(t);ik(e,o)},findValueOfOffset:lk,getValueFromEvent:e=>hS(e).map((e=>e.left)),findPositionOfValue:dk,setPositionFromValue:(e,t,o,n)=>{const r=HS(o),s=dk(e,n.getSpectrum(e),r,n.getLeftEdge(e),n.getRightEdge(e),o),a=$t(t.element)/2;_t(t.element,"left",s-a+"px")},onLeft:uk,onRight:mk,onUp:gk,onDown:pk,edgeActions:hk});const bk=(e,t)=>{Os(e,pS(),{value:t})},vk=(e,t,o)=>{const n={min:bS(t),max:wS(t),range:_S(t),value:o,step:MS(t),snap:AS(t),snapStart:DS(t),rounded:BS(t),hasMinEdge:NS(t),hasMaxEdge:VS(t),minBound:ek(e),maxBound:tk(e),screenRange:nk(e)};return jS(n)},yk=e=>(t,o)=>((e,t,o)=>{const n=(e>0?US:PS)(HS(o),bS(o),wS(o),MS(o));return bk(t,n),M.some(n)})(e,t,o).map(T),xk=(e,t,o,n,r,s)=>{const a=((e,t,o,n,r)=>{const s=nk(e),a=n.bind((t=>M.some(ak(t,e)))).getOr(0),i=r.bind((t=>M.some(ak(t,e)))).getOr(s),l={min:bS(t),max:wS(t),range:_S(t),value:o,hasMinEdge:NS(t),hasMaxEdge:VS(t),minBound:ek(e),minOffset:0,maxBound:tk(e),maxOffset:s,centerMinEdge:a,centerMaxEdge:i};return GS(l)})(t,s,o,n,r);return ek(t)-ek(e)+a},wk=M.none,Sk=M.none,kk=yk(-1),Ck=yk(1),Ok={"top-left":M.none(),top:M.some(((e,t)=>{LS(e,yS(t))})),"top-right":M.none(),right:M.none(),"bottom-right":M.none(),bottom:M.some(((e,t)=>{LS(e,kS(t))})),"bottom-left":M.none(),left:M.none()};var _k=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=vk(e,t,o);return bk(e,n),n},setToMin:(e,t)=>{const o=bS(t);bk(e,o)},setToMax:(e,t)=>{const o=wS(t);bk(e,o)},findValueOfOffset:vk,getValueFromEvent:e=>hS(e).map((e=>e.top)),findPositionOfValue:xk,setPositionFromValue:(e,t,o,n)=>{const r=HS(o),s=xk(e,n.getSpectrum(e),r,n.getTopEdge(e),n.getBottomEdge(e),o),a=Ht(t.element)/2;_t(t.element,"top",s-a+"px")},onLeft:wk,onRight:Sk,onUp:kk,onDown:Ck,edgeActions:Ok});const Tk=(e,t)=>{Os(e,pS(),{value:t})},Ek=(e,t)=>({x:e,y:t}),Mk=(e,t)=>(o,n)=>((e,t,o,n)=>{const r=e>0?US:PS,s=t?HS(n).x:r(HS(n).x,fS(n),xS(n),MS(n)),a=t?r(HS(n).y,bS(n),wS(n),MS(n)):HS(n).y;return Tk(o,Ek(s,a)),M.some(s)})(e,t,o,n).map(T),Ak=Mk(-1,!1),Dk=Mk(1,!1),Bk=Mk(-1,!0),Fk=Mk(1,!0),Ik={"top-left":M.some(((e,t)=>{LS(e,zS(vS(t),yS(t)))})),top:M.some(((e,t)=>{LS(e,zS(TS(t),yS(t)))})),"top-right":M.some(((e,t)=>{LS(e,zS(SS(t),yS(t)))})),right:M.some(((e,t)=>{LS(e,zS(SS(t),ES(t)))})),"bottom-right":M.some(((e,t)=>{LS(e,zS(SS(t),kS(t)))})),bottom:M.some(((e,t)=>{LS(e,zS(TS(t),kS(t)))})),"bottom-left":M.some(((e,t)=>{LS(e,zS(vS(t),kS(t)))})),left:M.some(((e,t)=>{LS(e,zS(vS(t),ES(t)))}))};var Rk=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=lk(e,t,o.left),r=vk(e,t,o.top),s=Ek(n,r);return Tk(e,s),s},setToMin:(e,t)=>{const o=fS(t),n=bS(t);Tk(e,Ek(o,n))},setToMax:(e,t)=>{const o=xS(t),n=wS(t);Tk(e,Ek(o,n))},getValueFromEvent:e=>hS(e),setPositionFromValue:(e,t,o,n)=>{const r=HS(o),s=dk(e,n.getSpectrum(e),r.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=xk(e,n.getSpectrum(e),r.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=$t(t.element)/2,l=Ht(t.element)/2;_t(t.element,"left",s-i+"px"),_t(t.element,"top",a-l+"px")},onLeft:Ak,onRight:Dk,onUp:Bk,onDown:Fk,edgeActions:Ik});const Nk=sm({name:"Slider",configFields:[ur("stepSize",1),ur("onChange",b),ur("onChoose",b),ur("onInit",b),ur("onDragStart",b),ur("onDragEnd",b),ur("snapToGrid",!1),ur("rounded",!0),nr("snapStart"),Kn("model",jn("mode",{x:[ur("minX",0),ur("maxX",100),$n("value",(e=>xr(e.mode.minX))),Xn("getInitialValue"),Oi("manager",fk)],y:[ur("minY",0),ur("maxY",100),$n("value",(e=>xr(e.mode.minY))),Xn("getInitialValue"),Oi("manager",_k)],xy:[ur("minX",0),ur("maxX",100),ur("minY",0),ur("maxY",100),$n("value",(e=>xr({x:e.mode.minX,y:e.mode.minY}))),Xn("getInitialValue"),Oi("manager",Rk)]})),nu("sliderBehaviours",[Tp,ou]),$n("mouseIsDown",(()=>xr(!1)))],partFields:gS,factory:(e,t,o,n)=>{const r=t=>Gu(t,e,"thumb"),s=t=>Gu(t,e,"spectrum"),a=t=>ju(t,e,"left-edge"),i=t=>ju(t,e,"right-edge"),l=t=>ju(t,e,"top-edge"),c=t=>ju(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:s})},g=(e,t)=>{d.value.set(t);const o=r(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&ju(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,r(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,r(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:su(e.sliderBehaviours,[Tp.config({mode:"special",focusIn:t=>ju(t,e,"spectrum").map(Tp.focusIn).map(T)}),ou.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),yl.config({channels:{[zd()]:{onReceive:p}}})]),events:As([Fs(pS(),((t,o)=>{((t,o)=>{g(t,o);const n=r(t);e.onChange(t,n,o),M.some(!0)})(t,o.event.value)})),Ps(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=r(t);m(t,a);const i=s(t);e.onInit(t,a,i,d.value.get())})),Fs(Dr(),h),Fs(Fr(),f),Fs(Rr(),h),Fs(Hr(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),Vk=Qs("rgb-hex-update"),Hk=Qs("slider-update"),zk=Qs("palette-update"),Lk="form",Pk=[nu("formBehaviours",[ou])],Uk=e=>"<alloy.field."+e+">",Wk=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:su(e.formBehaviours,[ou.config({store:{mode:"manual",getValue:t=>{const o=qu(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=cm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+Xs(e.element)),o.fold((()=>Jo.error(n)),Jo.value);var o,n})).map(ou.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{ju(t,e,n).each((e=>{cm.getCurrent(e).each((e=>{ou.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>ju(t,e,o).bind(cm.getCurrent)}}),jk={getField:ha(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),zu(Lk,Uk(t),o)),record:y(e)}})(),o=e(t),n=t.record(),r=z(n,(e=>Au({name:e,pname:Uk(e)})));return em(Lk,Pk,r,Wk,o)}},Gk=Qs("valid-input"),$k=Qs("invalid-input"),qk=Qs("validating-input"),Xk="colorcustom.rgb.",Kk=(e,t,o,n)=>{const r=(o,n)=>Qw.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Os(e,qk,{type:o})},onValid:e=>{Os(e,Gk,{type:o,value:ou.getValue(e)})},onInvalid:e=>{Os(e,$k,{type:o,value:ou.getValue(e)})}},validator:{validate:t=>{const o=ou.getValue(t),r=n(o)?Jo.value(!0):Jo.error(e("aria.input.invalid"));return Ux(r)},validateOnLoad:!1}}),s=(o,n,s,a,i)=>{const l=e("colorcustom.rgb.range"),c=_w.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[Ga(s)]}),d=_w.parts.field({data:i,factory:yb,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:gl([r(n,o),Mw.config({})]),onSetValue:e=>{Qw.isInvalid(e)&&Qw.run(e).get(b)}}),u=[c,d],m="hex"!==n?[_w.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,r=t.blue;ou.setValue(e,{red:o,green:n,blue:r})},i=Ah({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{_t(e.element,"background-color","#"+t.value)}))},c=rm({factory:()=>{const r={red:xr(M.some(255)),green:xr(M.some(255)),blue:xr(M.some(255)),hex:xr(M.some("ffffff"))},c=e=>r[e].get(),d=(e,t)=>{r[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",M.some(t)),d("green",M.some(o)),d("blue",M.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,M.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Ty(t);d("hex",M.some(t));const r=Uy(n);a(e,r),u(r),Os(e,Vk,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,M.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>zy(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Fy(t);return jk.getField(e,"hex").each((t=>{Up.isFocused(t)||ou.setValue(e,{hex:o.value})})),o})(e,t);Os(e,Vk,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(Xk+t+".label"),description:e(Xk+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return cn(jk.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",_w.sketch(s(Ly,"red",h.label,h.description,255))),o.field("green",_w.sketch(s(Ly,"green",f.label,f.description,255))),o.field("blue",_w.sketch(s(Ly,"blue",b.label,b.description,255))),o.field("hex",_w.sketch(s(Ay,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:gl([Qw.config({invalidClass:t("form-invalid")}),Vp("rgb-form-events",[Fs(Gk,g),Fs($k,m),Fs(qk,m)])])}))),{apis:{updateHex:(e,t)=>{ou.setValue(e,{hex:t.value}),((e,t)=>{const o=Uy(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},Yk=(e,t)=>{const o=rm({name:"ColourPicker",configFields:[Xn("dom"),ur("onValidHex",b),ur("onInvalidHex",b)],factory:o=>{const n=Kk(e,t,o.onValidHex,o.onInvalidHex),r=((e,t)=>{const o=Nk.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=Nk.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),r=(e,t)=>{const{width:o,height:n}=e,r=e.getContext("2d");if(null===r)return;r.fillStyle=t,r.fillRect(0,0,o,n);const s=r.createLinearGradient(0,0,o,0);s.addColorStop(0,"rgba(255,255,255,1)"),s.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=s,r.fillRect(0,0,o,n);const a=r.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=a,r.fillRect(0,0,o,n)};return rm({factory:e=>{const s=y({x:0,y:0}),a=gl([cm.config({find:M.some}),Up.config({})]);return Nk.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:s},rounded:!1,components:[o,n],onChange:(e,t,o)=>{Os(e,zk,{value:o})},onInit:(e,t,o,n)=>{r(o.element.dom,Gy($y))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Qy(t,100,100),s=Py(n);r(o,Gy(s))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=ex(Uy(t));Nk.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),s={paletteRgba:xr($y),paletteHue:xr(0)},a=Ah(((e,t)=>{const o=Nk.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=Nk.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return Nk.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:gl([Up.config({})]),onChange:(e,t,o)=>{Os(e,Hk,{value:o})}})})(0,t)),i=Ah(r.sketch({})),l=Ah(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{r.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{Nk.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{r.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=Uy(e);s.paletteRgba.set(o),s.paletteHue.set(t)})(t,o),L(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:gl([Vp("colour-picker-events",[Fs(Vk,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,r=(e=>ex(Uy(e)))(n);g(t,n,r.hue,e)}})()),Fs(zk,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,r=s.paletteHue.get(),a=Qy(r,n.x,100-n.y),i=tx(a);g(t,i,r,e)}})()),Fs(Hk,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),r=s.paletteRgba.get(),a=ex(r),i=Qy(n,a.saturation,a.value),l=tx(i);g(t,l,n,e)}})())]),cm.config({find:e=>l.getOpt(e)}),Tp.config({mode:"acyclic"})])}}});return o},Jk=()=>cm.config({find:M.some}),Zk=e=>cm.config({find:t=>rt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),Qk=Cn([ur("preprocess",x),ur("postprocess",x)]),eC=(e,t,o)=>ou.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),tC=(e,t,o)=>eC(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),oC=(e,t)=>{const o=Un("RepresentingConfigs.memento processors",Qk,t);return ou.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),r=ou.getValue(n);return o.postprocess(r)},setValue:(t,n)=>{const r=o.preprocess(n),s=e.get(t);ou.setValue(s,r)}}})},nC=tC,rC=eC,sC=e=>ou.config({store:{mode:"memory",initialValue:e}}),aC={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var iC=tinymce.util.Tools.resolve("tinymce.Resource"),lC=tinymce.util.Tools.resolve("tinymce.util.Tools");const cC=Qs("alloy-fake-before-tabstop"),dC=Qs("alloy-fake-after-tabstop"),uC=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:gl([Up.config({ignore:!0}),Mw.config({})])}),mC=e=>({dom:{tag:"div",classes:["tox-navobj"]},components:[uC([cC]),e,uC([dC])],behaviours:gl([Zk(1)])}),gC=(e,t)=>{Os(e,Ur(),{raw:{which:9,shiftKey:t}})},pC=(e,t)=>{const o=t.element;Fa(o,cC)?gC(e,!0):Fa(o,dC)&&gC(e,!1)},hC=e=>mw(e,["."+cC,"."+dC].join(","),_),fC=Qs("toolbar.button.execute"),bC={[ns()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},vC=(e,t,o)=>Lh(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),yC=(e,t)=>vC(e,t,[]),xC=(e,t)=>vC(e,t,[Np.config({})]),wC=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[Ga(o.translate(e))],behaviours:gl([Np.config({})])}),SC=Qs("update-menu-text"),kC=Qs("update-menu-icon"),CC=(e,t,o)=>{const n=xr(b),r=e.text.map((e=>Ah(wC(e,t,o.providers)))),s=e.icon.map((e=>Ah(xC(e,o.providers.icons)))),a=(e,t)=>{const o=ou.getValue(e);return Up.focus(o),Os(o,"keydown",{raw:t.event.raw}),ow.close(o),M.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=Lh("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),d=Ah(ow.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(z(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:uy([s.map((e=>e.asSpec())),r.map((e=>e.asSpec())),M.some(c)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{kb(e).each((e=>Up.focus(e)))})(n)},dropdownBehaviours:gl([...e.dropdownBehaviours,ny((()=>e.disabled||o.providers.isDisabled())),oy(),eS.config({}),Np.config({}),Vp("dropdown-events",[iy(e,n),ly(e,n)]),Vp("menubutton-update-display-text",[Fs(SC,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Np.set(e,[Ga(o.providers.translate(t.event.text))])}))})),Fs(kC,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Np.set(e,[xC(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:cn(bC,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:gl([Tp.config({mode:"special",onLeft:a,onRight:a}),Vp("dropdown-sandbox-events",[Fs(xb,((e,t)=>{(e=>{const t=ou.getValue(e),o=Sb(e).map(Cb);ow.refetch(t).get((()=>{const e=Nx.getCoupled(t,"sandbox");o.each((t=>Sb(e).each((e=>((e,t)=>{ou.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Fs(wb,((e,t)=>{((e,t)=>{(e=>Nd.getState(e).bind(Fm.getHighlighted).bind(Fm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const r={...n,target:t};e.getSystem().triggerEvent(o,t,r)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...pb(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:nw,onCollapseMenu:(e,t,o)=>{Fm.getHighlighted(o).each((t=>{nw(e,o,t)}))},onDehighlightItem:rw}},fetch:t=>Px(S(e.fetch,t))}));return d.asSpec()},OC=e=>"separator"===e.type,_C={type:"separator"},TC=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>s(e))(o)?""===o?e:"|"===o?e.length>0&&!OC(e[e.length-1])?e.concat([_C]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&OC(o[o.length-1])&&o.pop(),o})(s(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>Qs("generated-menu-item")));return cn({value:t},e)})(o),r=((e,t)=>{const o=e.getSubmenuItems(),n=TC(o,t);return{item:e,menus:cn(n.menus,{[e.value]:n.items}),expansions:cn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:cn(e.menus,r.menus),items:[r.item,...e.items],expansions:cn(e.expansions,r.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},EC=(e,t,o,n)=>{const r=Qs("primary-menu"),s=TC(e,o.shared.providers.menuItems());if(0===s.items.length)return M.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=cw(r,s.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(s.menus,((e,n)=>cw(n,e,t,o,!1,l))),d=cn(c,Sr(r,i));return M.from(_h.tieredData(r,d,s.expansions))},MC=e=>!ve(e,"items"),AC="data-value",DC=(e,t,o,n)=>z(o,(o=>MC(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{ou.setValue(e,o.value),Os(e,Rw,{name:t}),Up.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>DC(e,t,o.items,n)})),BC=(e,t)=>se(e,(e=>MC(e)?ke(e.value===t,e):BC(e.items,t))),FC=rm({name:"HtmlSelect",configFields:[Xn("options"),nu("selectBehaviours",[Up,ou]),ur("selectClasses",[]),ur("selectAttributes",{}),nr("data")],factory:(e,t)=>{const o=z(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Sr("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:su(e.selectBehaviours,[Up.config({}),ou.config({store:{mode:"manual",getValue:e=>Na(e.element),setValue:(t,o)=>{G(e.options,(e=>e.value===o)).isSome()&&Va(t.element,o)},...n}})])}}}),IC=y([ur("field1Name","field1"),ur("field2Name","field2"),ki("onLockedChange"),yi(["lockClass"]),ur("locked",!1),au("coupledFieldBehaviours",[cm,ou])]),RC=(e,t)=>Au({factory:_w,name:e,overrides:e=>({fieldBehaviours:gl([Vp("coupled-input-behaviour",[Fs(jr(),(o=>{((e,t,o)=>ju(e,t,o).bind(cm.getCurrent))(o,e,t).each((t=>{ju(o,e,"lock").each((n=>{Yp.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),NC=y([RC("field1","field2"),RC("field2","field1"),Au({factory:Mh,schema:[Xn("dom")],name:"lock",overrides:e=>({buttonBehaviours:gl([Yp.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),VC=sm({name:"FormCoupledInputs",configFields:IC(),partFields:NC(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:iu(e.coupledFieldBehaviours,[cm.config({find:M.some}),ou.config({store:{mode:"manual",getValue:t=>{const o=Ku(t,e,["field1","field2"]);return{[e.field1Name]:ou.getValue(o.field1()),[e.field2Name]:ou.getValue(o.field2())}},setValue:(t,o)=>{const n=Ku(t,e,["field1","field2"]);ye(o,e.field1Name)&&ou.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&ou.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>ju(t,e,"field1"),getField2:t=>ju(t,e,"field2"),getLock:t=>ju(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),HC=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return Jo.value({value:e,unit:o})}return Jo.error(e)},zC=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?M.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?M.some(e.value):M.some(e.value/o[e.unit]*o[t]):M.none()},LC=e=>M.none(),PC=(e,t)=>{const o=e.label.map((e=>Iw(e,t))),n=[km.config({disabled:()=>e.disabled||t.isDisabled()}),oy(),Tp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Cs(e,zw),M.some(!0))}),Vp("textfield-change",[Fs(jr(),((t,o)=>{Os(t,Rw,{name:e.name})})),Fs(ts(),((t,o)=>{Os(t,Rw,{name:e.name})}))]),Mw.config({})],r=e.validation.map((e=>Qw.config({getRoot:e=>tt(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=ou.getValue(t),n=e.validator(o);return Ux(!0===n?Jo.value(o):Jo.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),s={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e})))},a=_w.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:s,inputClasses:[e.classname],inputBehaviours:gl(q([n,r])),selectOnFocus:!1,factory:yb}),i=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[km.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{_w.getField(e).each(km.disable)},onEnabled:e=>{_w.getField(e).each(km.enable)}}),oy()];return Dw(o,a,i,l)};var UC=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return As([Fs(e.event,o),Us((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Fs(e,(()=>t.cancel()))])).getOr([])))}});const WC=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...r)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,r)}),t)}}},jC=e=>{const t=xr(null);return ba({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var GC=Object.freeze({__proto__:null,throttle:jC,init:e=>e.stream.streams.state(e)}),$C=[Kn("stream",jn("mode",{throttle:[Xn("delay"),ur("stopEvent",!0),Oi("streams",{setup:(e,t)=>{const o=e.stream,n=WC(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:jC})]})),ur("event","input"),nr("cancelEvent"),ki("onStream")];const qC=hl({fields:$C,name:"streaming",active:UC,state:GC}),XC=(e,t,o)=>{const n=ou.getValue(o);ou.setValue(t,n),YC(t)},KC=(e,t)=>{const o=e.element,n=Na(o),r=o.dom;"number"!==xt(o,"type")&&t(r,n)},YC=e=>{KC(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},JC=y("alloy.typeahead.itemexecute"),ZC=y([nr("lazySink"),Xn("fetch"),ur("minChars",5),ur("responseTime",1e3),wi("onOpen"),ur("getHotspot",M.some),ur("getAnchorOverrides",y({})),ur("layouts",M.none()),ur("eventOrder",{}),yr("model",{},[ur("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),ur("selectsOver",!0),ur("populateFromBrowse",!0)]),wi("onSetValue"),Si("onExecute"),wi("onItemExecute"),ur("inputClasses",[]),ur("inputAttributes",{}),ur("inputStyles",{}),ur("matchWidth",!0),ur("useMinWidth",!1),ur("dismissOnBlur",!0),yi(["openClass"]),nr("initialData"),nu("typeaheadBehaviours",[Up,ou,qC,Tp,Yp,Nx]),$n("lazyTypeaheadComp",(()=>xr(M.none))),$n("previewing",(()=>xr(!0)))].concat(hb()).concat(Qx())),QC=y([Du({schema:[vi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=ou.getValue(t),r=e.getDisplayText(n),s=ou.getValue(o);return 0===e.getDisplayText(s).indexOf(r)?M.some((()=>{XC(0,t,o),((e,t)=>{KC(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,r.length)})):M.none()}return M.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Fm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&XC(e.model,t,n)}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Os(e,JC(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&XC(e.model,t,o)}))}})})]),eO=sm({name:"Typeahead",configFields:ZC(),partFields:QC(),factory:(e,t,o,n)=>{const r=(t,o,r)=>{e.previewing.set(!1);const s=Nx.getCoupled(t,"sandbox");if(Nd.isOpen(s))cm.getCurrent(s).each((e=>{Fm.getHighlighted(e).fold((()=>{r(e)}),(()=>{Ms(s,e.element,"keydown",o)}))}));else{const o=e=>{cm.getCurrent(e).each(r)};$x(e,a(t),t,s,n,o,Ch.HighlightMenuAndItem).get(b)}},s=fb(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=X(o,(e=>U(e.items,(e=>"item"===e.type))));return ou.getState(e).update(z(n,(e=>e.data))),t})),i=e=>cm.getCurrent(e),l="typeaheadevents",c=[Up.config({}),ou.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Na(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Va(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Sr("initialValue",e))).getOr({})}}),qC.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const r=Nx.getCoupled(t,"sandbox");if(Up.isFocused(t)&&Na(t.element).length>=e.minChars){const o=i(r).bind((e=>Fm.getHighlighted(e).map(ou.getValue)));e.previewing.set(!0);const s=t=>{i(r).each((t=>{o.fold((()=>{e.model.selectsOver&&Fm.highlightFirst(t)}),(e=>{Fm.highlightBy(t,(t=>ou.getValue(t).value===e.value)),Fm.getHighlighted(t).orThunk((()=>(Fm.highlightFirst(t),M.none())))}))}))};$x(e,a(t),t,r,n,s,Ch.HighlightJustMenu).get(b)}},cancelEvent:ls()}),Tp.config({mode:"special",onDown:(e,t)=>(r(e,t,Fm.highlightFirst),M.some(!0)),onEscape:e=>{const t=Nx.getCoupled(e,"sandbox");return Nd.isOpen(t)?(Nd.close(t),M.some(!0)):M.none()},onUp:(e,t)=>(r(e,t,Fm.highlightLast),M.some(!0)),onEnter:t=>{const o=Nx.getCoupled(t,"sandbox"),n=Nd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Fm.getHighlighted(e))).map((e=>(Os(t,JC(),{item:e}),!0)));{const r=ou.getValue(t);return Cs(t,ls()),e.onExecute(o,t,r),n&&Nd.close(o),M.some(!0)}}}),Yp.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),Nx.config({others:{sandbox:t=>Jx(e,t,{onOpen:()=>Yp.on(t),onClose:()=>Yp.off(t)})}}),Vp(l,[Ps((t=>{e.lazyTypeaheadComp.set(M.some(t))})),Us((t=>{e.lazyTypeaheadComp.set(M.none())})),js((t=>{const o=b;Xx(e,a(t),t,n,o,Ch.HighlightMenuAndItem).get(b)})),Fs(JC(),((t,o)=>{const n=Nx.getCoupled(t,"sandbox");XC(e.model,t,o.event.item),Cs(t,ls()),e.onItemExecute(t,n,o.event.item,ou.getValue(t)),Nd.close(n),YC(t)}))].concat(e.dismissOnBlur?[Fs(es(),(e=>{const t=Nx.getCoupled(e,"sandbox");Cl(t.element).isNone()&&Nd.close(t)}))]:[]))],d={[hs()]:[ou.name(),qC.name(),l],...e.eventOrder};return{uid:e.uid,dom:vb(cn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...s,...su(e.typeaheadBehaviours,c)},eventOrder:d}}}),tO=e=>({...e,toCached:()=>tO(e.toCached()),bindFuture:t=>tO(e.bind((e=>e.fold((e=>Ux(Jo.error(e))),(e=>t(e)))))),bindResult:t=>tO(e.map((e=>e.bind(t)))),mapResult:t=>tO(e.map((e=>e.map(t)))),mapError:t=>tO(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>tO(Px((n=>{let r=!1;const s=setTimeout((()=>{r=!0,n(Jo.error(o()))}),t);e.get((e=>{r||(clearTimeout(s),n(e))}))})))}),oO=e=>tO(Px(e)),nO=e=>({isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t),setActive:t=>{const o=e.element;t?(Da(o,"tox-tbtn--enabled"),vt(o,"aria-pressed",!0)):(Ba(o,"tox-tbtn--enabled"),kt(o,"aria-pressed"))},isActive:()=>Fa(e.element,"tox-tbtn--enabled")}),rO=(e,t,o,n)=>CC({text:e.text,icon:e.icon,tooltip:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const r={pattern:e.search.isSome()?sw(t):""};e.fetch((t=>{n(EC(t,Jf.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),r)},onSetup:e.onSetup,getApi:nO,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Mw.config({})]},t,o.shared),sO=(e,t,o)=>{const n=e=>n=>{const r=!n.isActive();n.setActive(r),e.storage.set(r),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{wl(t.element),Os(t,Hw,{name:e.name,value:e.storage.get()})}))}))},r=e=>t=>{t.setActive(e.storage.get())};return t=>{t(z(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:r(e)}})))}},aO=(e,t,o=[],n,r,s)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:gl([ny((()=>!e.enabled||s.isDisabled())),oy(),Mw.config({}),Vp("button press",[Bs("click"),Bs("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=cn(i,{dom:n});return cn(l,{components:r})},iO=(e,t,o,n=[])=>{const r={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},s=e.icon.map((e=>yC(e,o.icons))),a=uy([s]);return aO(e,t,n,r,a,o)},lO=(e,t,o,n=[],r=[])=>{const s=o.translate(e.text),a=e.icon.map((e=>yC(e,o.icons))),i=[a.getOrThunk((()=>Ga(s)))],l=[...(e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}})(e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary")),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...r];return aO(e,t,n,{tag:"button",classes:l,attributes:{title:s}},i,o)},cO=(e,t,o,n=[],r=[])=>{const s=lO(e,M.some(t),o,n,r);return Mh.sketch(s)},dO=(e,t)=>o=>{"custom"===t?Os(o,Hw,{name:e,value:{}}):"submit"===t?Cs(o,zw):"cancel"===t?Cs(o,Vw):console.error("Unknown button type: ",t)},uO=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>s,n=e,r={...e,type:"menubutton",search:M.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:sO(n.items,t,o)},s=Ah(rO(r,"tox-tbtn",o,M.none()));return s.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=dO(e.name,t),r={...e,borderless:!1};return cO(r,n,o.shared.providers,[])}throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},mO={type:"separator"},gO=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),pO=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),hO=(e,t)=>(e=>z(e,gO))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),fO=e=>hO("header",e.targets),bO=e=>hO("anchor",e.targets),vO=e=>M.from(e.anchorTop).map((e=>pO("<top>",e))).toArray(),yO=e=>M.from(e.anchorBottom).map((e=>pO("<bottom>",e))).toArray(),xO=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,r=null!==(t=e.value)&&void 0!==t?t:"";return Oe(n.toLowerCase(),o)||Oe(r.toLowerCase(),o)}))},wO=Qs("aria-invalid"),SO=(e,t)=>{e.dom.checked=t},kO=e=>e.dom.checked,CO=e=>(t,o,n,r)=>be(o,"name").fold((()=>e(o,r,M.none())),(s=>t.field(s,e(o,r,be(n,s))))),OO={bar:CO(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:z(e.items,t.interpreter)}))(e,t.shared))),collection:CO(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>Iw(e,t))),r=e=>(t,o)=>{si(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,xt(n,"data-collection-item-value"))}))},s=r(((o,n,r,s)=>{n.stop(),t.isDisabled()||Os(o,Hw,{name:e.name,value:s})})),a=[Fs(zr(),r(((e,t,o)=>{wl(o)}))),Fs($r(),s),Fs(ss(),s),Fs(Lr(),r(((e,t,o)=>{ri(e.element,"."+sb).each((e=>{Ba(e,sb)})),Da(o,sb)}))),Fs(Pr(),r((e=>{ri(e.element,"."+sb).each((e=>{Ba(e,sb)}))}))),js(r(((t,o,n,r)=>{Os(t,Hw,{name:e.name,value:r})})))],i=(e,t)=>z(Nc(e.element,".tox-collection__item"),t),l=_w.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:x},behaviours:gl([km.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{Da(e,"tox-collection__item--state-disabled"),vt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Ba(e,"tox-collection__item--state-disabled"),kt(e,"aria-disabled")}))}}),oy(),Np.config({}),ou.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=z(n,(o=>{const n=Dh.translate(o.text),r=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",s=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${Aw.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${s}${r}</div>`})),s="auto"!==e.columns&&e.columns>1?H(r,e.columns):[r],a=z(s,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));$s(o.element,a.join(""))})(o,n),"auto"===e.columns&&Uv(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Tp.setGridSize(o,e,t)})),Cs(o,Ww)}}),Mw.config({}),Tp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${Qf}`}})),Vp("collection-events",a)]),eventOrder:{[ns()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return Dw(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:CO(((e,t)=>((e,t)=>Sw.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Mh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Vh(e.icon,t.icons),attributes:{title:t.translate(e.iconTooltip)}},action:t=>{Os(t,Hw,{name:"alert-banner",value:e.url})},buttonBehaviours:gl([Hh()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]}))(e,t.shared.providers))),input:CO(((e,t,o)=>((e,t,o)=>PC({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:M.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:CO(((e,t,o)=>((e,t,o)=>PC({name:e.name,multiline:!0,label:e.label,inputMode:M.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:M.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:CO(((e,t)=>((e,t)=>{return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.providers.translate(e.label))]},...z(e.items,t.interpreter)],behaviours:gl([Jk(),Np.config({}),(o=M.none(),tC(o,Gs,$s)),Tp.config({mode:"acyclic"})])};var o})(e,t.shared))),iframe:(Y_=(e,t,o)=>((e,t,o)=>{const n=e.sandboxed,r=e.transparent,s="tox-dialog__iframe",a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...n?{sandbox:"allow-scripts allow-same-origin"}:{}},i=(e=>{const t=xr(e.getOr(""));return{getValue:e=>t.get(),setValue:(e,o)=>{t.get()!==o&&vt(e.element,"srcdoc",o),t.set(o)}}})(o),l=e.label.map((e=>Iw(e,t))),c=_w.parts.field({factory:{sketch:e=>mC({uid:e.uid,dom:{tag:"iframe",attributes:a,classes:r?[s]:[s,`${s}--opaque`]},behaviours:gl([Mw.config({}),Up.config({}),rC(o,i.getValue,i.setValue)])})}});return Dw(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const r=cn(t,{source:"dynamic"});return CO(Y_)(e,r,o,n)}),button:CO(((e,t)=>((e,t)=>{const o=dO(e.name,"custom");return n=M.none(),r=_w.parts.field({factory:Mh,...lO(e,M.some(o),t,[sC(""),Jk()])}),Dw(n,r,[],[]);var n,r})(e,t.shared.providers))),checkbox:CO(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),M.some(!0)),r=_w.parts.field({factory:{sketch:x},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:gl([Jk(),km.config({disabled:()=>!e.enabled||t.isDisabled()}),Mw.config({}),Up.config({}),nC(o,kO,SO),Tp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Vp("checkbox-events",[Fs(Gr(),((t,o)=>{Os(t,Rw,{name:e.name})}))])])}),s=_w.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[Ga(t.translate(e.label))],behaviours:gl([eS.config({})])}),a=e=>Lh("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Ah({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return _w.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[r,i.asSpec(),s],fieldBehaviours:gl([km.config({disabled:()=>!e.enabled||t.isDisabled(),disableClass:"tox-checkbox--disabled",onDisabled:e=>{_w.getField(e).each(km.disable)},onEnabled:e=>{_w.getField(e).each(km.enable)}}),oy()])})})(e,t.shared.providers,o))),colorinput:CO(((e,t,o)=>((e,t,o,n)=>{const r=_w.parts.field({factory:yb,inputClasses:["tox-textfield"],data:n,onSetValue:e=>Qw.run(e).get(b),inputBehaviours:gl([km.config({disabled:t.providers.isDisabled}),oy(),Mw.config({}),Qw.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>tt(e.element),notify:{onValid:e=>{const t=ou.getValue(e);Os(e,tS,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=ou.getValue(e);if(0===t.length)return Ux(Jo.value(!0));{const e=Be("span");_t(e,"background-color",t);const o=Dt(e,"background-color").fold((()=>Jo.error("blah")),(e=>Jo.value(t)));return Ux(o)}}}})]),selectOnFocus:!1}),s=e.label.map((e=>Iw(e,t.providers))),a=(e,t)=>{Os(e,oS,{value:t})},i=Ah(((e,t)=>ow.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:gl([ny(t.providers.isDisabled),oy(),eS.config({}),Mw.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Px((t=>e.fetch(t))).map((n=>M.from(dw(cn(kx(Qs("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,Jf.CLOSE_ON_EXECUTE,_,t.providers),{movement:Ox(e.columns,e.presets)}))))),parts:{menu:pb(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Ki,Xi,Qi],onLtr:()=>[Xi,Ki,Qi]},components:[],fetch:vx(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Cs(t,nS)),(o=>{a(t,o),Zy(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return _w.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:s.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[r,i.asSpec()]}]),fieldBehaviours:gl([Vp("form-field-events",[Fs(tS,((t,o)=>{i.getOpt(t).each((e=>{_t(e.element,"background-color",o.event.color)})),Os(t,Rw,{name:e.name})})),Fs(oS,((e,t)=>{_w.getField(e).each((o=>{ou.setValue(o,t.event.value),cm.getCurrent(e).each(Up.focus)}))})),Fs(nS,((e,t)=>{_w.getField(e).each((t=>{cm.getCurrent(e).each(Up.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:CO(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,r=Yk((e=>t=>e.translate(aC[t]))(t),n),s=Ah(r.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Os(e,Hw,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Os(e,Hw,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[s.asSpec()],behaviours:gl([rC(o,(e=>{const t=s.get(e);return cm.getCurrent(t).bind((e=>ou.getValue(e).hex)).map((e=>"#"+e)).getOr("")}),((e,t)=>{const o=M.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=s.get(e);cm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{ou.setValue(e,{hex:o.getOr("")}),jk.getField(e,"hex").each((e=>{Cs(e,jr())}))}))})),Jk()])}})(0,t.shared.providers,o))),dropzone:CO(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},r=e=>(t,o)=>{L(e,(e=>{e(t,o)}))},s=(e,t)=>{var o;if(!km.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(ou.setValue(o,((e,t)=>{const o=lC.explode(t.getOption("images_file_types"));return U(re(e),(e=>N(o,(t=>_e(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Os(o,Rw,{name:e.name}))},l=Ah({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:gl([Vp("input-file-events",[Hs($r()),Hs(ss())])])}),c=e.label.map((e=>Iw(e,t))),d=_w.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:gl([sC(o.getOr([])),Jk(),km.config({}),Yp.config({toggleClass:"dragenter",toggleOnExecute:!1}),Vp("dropzone-events",[Fs("dragenter",r([n,Yp.toggle])),Fs("dragleave",r([n,Yp.toggle])),Fs("dragover",n),Fs("drop",r([n,s])),Fs(Gr(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[Ga(t.translate("Drop an image here"))]},Mh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[Ga(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:gl([Mw.config({}),ny(t.isDisabled),oy()])})]}]})}});return Dw(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:CO(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:z(e.items,t.interpreter)}))(e,t.shared))),listbox:CO(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,r=o.bind((t=>BC(e.items,t))).orThunk((()=>oe(e.items).filter(MC))),s=e.label.map((e=>Iw(e,n))),a=_w.parts.field({dom:{},factory:{sketch:o=>CC({uid:o.uid,text:r.map((e=>e.text)),icon:M.none(),tooltip:e.label,role:M.none(),fetch:(o,n)=>{const r=DC(o,e.name,e.items,ou.getValue(o));n(EC(r,Jf.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:M.none()}))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Mw.config({}),rC(r.map((e=>e.value)),(e=>xt(e.element,AC)),((t,o)=>{BC(e.items,o).each((e=>{vt(t.element,AC,e.value),Os(t,SC,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return _w.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:gl([km.config({disabled:y(!e.enabled),onDisabled:e=>{_w.getField(e).each(km.disable)},onEnabled:e=>{_w.getField(e).each(km.enable)}})])})})(e,t,o))),selectbox:CO(((e,t,o)=>((e,t,o)=>{const n=z(e.items,(e=>({text:t.translate(e.text),value:e.value}))),r=e.label.map((e=>Iw(e,t))),s=_w.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:FC,selectBehaviours:gl([km.config({disabled:()=>!e.enabled||t.isDisabled()}),Mw.config({}),Vp("selectbox-change",[Fs(Gr(),((t,o)=>{Os(t,Rw,{name:e.name})}))])])}),a=e.size>1?M.none():M.some(Lh("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[s],a.toArray()])};return _w.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:gl([km.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{_w.getField(e).each(km.disable)},onEnabled:e=>{_w.getField(e).each(km.enable)}}),oy()])})})(e,t.shared.providers,o))),sizeinput:CO(((e,t)=>((e,t)=>{let o=LC;const n=Qs("ratio-event"),r=e=>Lh(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),s=VC.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[r("lock"),r("unlock")],buttonBehaviours:gl([km.config({disabled:()=>!e.enabled||t.isDisabled()}),oy(),Mw.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>_w.parts.field({factory:yb,inputClasses:["tox-textfield"],inputBehaviours:gl([km.config({disabled:()=>!e.enabled||t.isDisabled()}),oy(),Mw.config({}),Vp("size-input-events",[Fs(Lr(),((e,t)=>{Os(e,n,{isField1:o})})),Fs(Gr(),((t,o)=>{Os(t,Rw,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.translate(e))]}),c=VC.parts.field1(a([_w.parts.label(l("Width")),i(!0)])),d=VC.parts.field2(a([_w.parts.label(l("Height")),i(!1)]));return VC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),s])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{HC(ou.getValue(e)).each((e=>{o(e).each((e=>{ou.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:gl([km.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{VC.getField1(e).bind(_w.getField).each(km.disable),VC.getField2(e).bind(_w.getField).each(km.disable),VC.getLock(e).each(km.disable)},onEnabled:e=>{VC.getField1(e).bind(_w.getField).each(km.enable),VC.getField2(e).bind(_w.getField).each(km.enable),VC.getLock(e).each(km.enable)}}),oy(),Vp("size-input-events2",[Fs(n,((e,t)=>{const n=t.event.isField1,r=n?VC.getField1(e):VC.getField2(e),s=n?VC.getField2(e):VC.getField1(e),a=r.map(ou.getValue).getOr(""),i=s.map(ou.getValue).getOr("");o=((e,t)=>{const o=HC(e).toOptional(),n=HC(t).toOptional();return Se(o,n,((e,t)=>zC(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>zC(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(LC))).getOr(LC)})(a,i)}))])])})})(e,t.shared.providers))),slider:CO(((e,t,o)=>((e,t,o)=>{const n=Nk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Ga(t.translate(e.label))]}),r=Nk.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),s=Nk.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return Nk.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,r,s],sliderBehaviours:gl([Jk(),Up.config({})]),onChoose:(t,o,n)=>{Os(t,Rw,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:CO(((e,t,o)=>((e,t,o,n)=>{const r=t.shared.providers,s=t=>{const n=ou.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":wO,type:"url"},minChars:0,responseTime:0,fetch:n=>{const r=((e,t,o)=>{const n=ou.getValue(t),r=void 0!==n.meta.text?n.meta.text:n.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=xO(r,(e=>z(e,(e=>pO(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,xO(r,fO(t)),xO(r,q([vO(t),bO(t),yO(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(mO,t)),[])):n;var s}))})(e.filetype,n,o),s=EC(r,Jf.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:M.none()});return Ux(s)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(Qw)&&Qw.run(e).get(b)},typeaheadBehaviours:gl([...o.getValidationHandler().map((t=>Qw.config({getRoot:e=>tt(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{vt(e.element,"title",r.translate(t))}))}},validator:{validate:o=>{const n=ou.getValue(o);return oO((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=Jo.error(e.message);o(t)}else{const t=Jo.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),km.config({disabled:()=>!e.enabled||r.isDisabled()}),Mw.config({}),Vp("urlinput-events",[Fs(jr(),(t=>{const o=Na(t.element),n=o.trim();n!==o&&Va(t.element,n),"file"===e.filetype&&Os(t,Rw,{name:e.name})})),Fs(Gr(),(t=>{Os(t,Rw,{name:e.name}),s(t)})),Fs(ts(),(t=>{Os(t,Rw,{name:e.name}),s(t)}))])]),eventOrder:{[jr()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:pb(0,0,"normal")},onExecute:(e,t,o)=>{Os(t,zw,{})},onItemExecute:(t,o,n,r)=>{s(t),Os(t,Rw,{name:e.name})}},i=_w.parts.field({...a,factory:eO}),l=e.label.map((e=>Iw(e,r))),c=Ah(((e,t,o=e,n=e)=>Lh(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:r.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},r.icons))("invalid",M.some(wO),"warning")),d=Ah({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Qs("browser.url.event"),g=Ah({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:gl([km.config({disabled:()=>!e.enabled||r.isDisabled()})])}),p=Ah(cO({name:e.name,icon:M.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:M.none(),borderless:!0},(e=>Cs(e,m)),r,[],["tox-browse-url"]));return _w.sketch({dom:Fw([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:gl([km.config({disabled:()=>!e.enabled||r.isDisabled(),onDisabled:e=>{_w.getField(e).each(km.disable),p.getOpt(e).each(km.disable)},onEnabled:e=>{_w.getField(e).each(km.enable),p.getOpt(e).each(km.enable)}}),oy(),Vp("url-input-events",[Fs(m,(t=>{cm.getCurrent(t).each((o=>{const n=ou.getValue(o),r={fieldname:e.name,...n};u.each((n=>{n(r).get((n=>{ou.setValue(o,n),Os(t,Rw,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:CO((e=>{const t=Ul(),o=Ah({dom:{tag:e.tag}}),n=Ul();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:gl([Vp("custom-editor-events",[Ps((r=>{o.getOpt(r).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):iC.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),rC(M.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),Jk()]),components:[o.asSpec()]}})),htmlpanel:CO((e=>"presentation"===e.presets?Sw.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):Sw.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:gl([Mw.config({}),Up.config({})])}))),imagepreview:CO(((e,t,o)=>((e,t)=>{const o=xr(t.getOr({url:""})),n=Ah({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),r=Ah({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),s={};e.height.each((e=>s.height=e));const a=t.map((e=>({url:e.url,zoom:M.from(e.zoom),cachedWidth:M.from(e.cachedWidth),cachedHeight:M.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:s,attributes:{role:"presentation"}},components:[r.asSpec()],behaviours:gl([Jk(),rC(a,(()=>o.get()),((e,t)=>{const s={url:t.url};t.zoom.each((e=>s.zoom=e)),t.cachedWidth.each((e=>s.cachedWidth=e)),t.cachedHeight.each((e=>s.cachedHeight=e)),o.set(s);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=s;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=$t(e),r=Ht(e);return Math.min(n/t,r/o,1)})(e.element,t,o);s.zoom=n}const a=((e,t,o,n,r)=>{const s=o*r,a=n*r,i=Math.max(0,e/2-s/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:s.toString()+"px",height:a.toString()+"px"}})($t(e.element),Ht(e.element),t,o,s.zoom);r.getOpt(e).each((e=>{Tt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var r;t.url!==xt(n,"src")&&(vt(n,"src",t.url),Ba(e.element,"tox-imagepreview__loaded")),a(),(r=n,new Promise(((e,t)=>{const o=()=>{s(),e(r)},n=[jl(r,"load",o),jl(r,"error",(()=>{s(),t("Unable to load data from image: "+r.dom.src)}))],s=()=>L(n,(e=>e.unbind()));r.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(Da(e.element,"tox-imagepreview__loaded"),s.cachedWidth=t.dom.naturalWidth,s.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:CO(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(r=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:z(r,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:z(n,(e=>({dom:{tag:"tr"},components:z(e,o)})))})],behaviours:gl([Mw.config({}),Up.config({})])};var n,r})(e,t.shared.providers))),panel:CO(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:z(e.items,t.shared.interpreter)}))(e,t)))},_O={field:(e,t)=>t,record:y([])},TO=(e,t,o,n)=>{const r=cn(n,{shared:{interpreter:t=>EO(e,t,o,r)}});return EO(e,t,o,r)},EO=(e,t,o,n)=>be(OO,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(r=>r(e,t,o,n))),MO=(e,t,o)=>EO(_O,e,t,o),AO="layout-inset",DO=e=>e.x,BO=(e,t)=>e.x+e.width/2-t.width/2,FO=(e,t)=>e.x+e.width-t.width,IO=e=>e.y,RO=(e,t)=>e.y+e.height-t.height,NO=(e,t)=>e.y+e.height/2-t.height/2,VO=(e,t,o)=>Ei(FO(e,t),RO(e,t),o.insetSouthwest(),Fi(),"southwest",Li(e,{right:0,bottom:3}),AO),HO=(e,t,o)=>Ei(DO(e),RO(e,t),o.insetSoutheast(),Bi(),"southeast",Li(e,{left:1,bottom:3}),AO),zO=(e,t,o)=>Ei(FO(e,t),IO(e),o.insetNorthwest(),Di(),"northwest",Li(e,{right:0,top:2}),AO),LO=(e,t,o)=>Ei(DO(e),IO(e),o.insetNortheast(),Ai(),"northeast",Li(e,{left:1,top:2}),AO),PO=(e,t,o)=>Ei(BO(e,t),IO(e),o.insetNorth(),Ii(),"north",Li(e,{top:2}),AO),UO=(e,t,o)=>Ei(BO(e,t),RO(e,t),o.insetSouth(),Ri(),"south",Li(e,{bottom:3}),AO),WO=(e,t,o)=>Ei(FO(e,t),NO(e,t),o.insetEast(),Vi(),"east",Li(e,{right:0}),AO),jO=(e,t,o)=>Ei(DO(e),NO(e,t),o.insetWest(),Ni(),"west",Li(e,{left:1}),AO),GO=e=>{switch(e){case"north":return PO;case"northeast":return LO;case"northwest":return zO;case"south":return UO;case"southeast":return HO;case"southwest":return VO;case"east":return WO;case"west":return jO}},$O=(e,t,o,n,r)=>Vl(n).map(GO).getOr(PO)(e,t,o,n,r),qO=e=>{switch(e){case"north":return UO;case"northeast":return HO;case"northwest":return VO;case"south":return PO;case"southeast":return LO;case"southwest":return zO;case"east":return jO;case"west":return WO}},XO=(e,t,o,n,r)=>Vl(n).map(qO).getOr(PO)(e,t,o,n,r),KO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},YO=(e,t,o)=>{const n={maxHeightFunction:Zl()};return()=>o()?{type:"node",root:ut(dt(e())),node:M.from(e()),bubble:oc(12,12,KO),layouts:{onRtl:()=>[LO],onLtr:()=>[zO]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:oc(-12,12,KO),layouts:{onRtl:()=>[Xi],onLtr:()=>[Ki]},overrides:n}},JO=(e,t,o)=>()=>o()?{type:"node",root:ut(dt(e())),node:M.from(e()),layouts:{onRtl:()=>[PO],onLtr:()=>[PO]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[Qi],onLtr:()=>[Qi]}},ZO=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng();return M.some(Mc.range(Ie(t.startContainer),t.startOffset,Ie(t.endContainer),t.endOffset))}}),QO=e=>t=>({type:"node",root:e(),node:t}),e_=(e,t,o)=>{const n=Uf(e),r=()=>Ie(e.getBody()),s=()=>Ie(e.getContentAreaContainer()),a=()=>n||!o();return{inlineDialog:YO(s,t,a),banner:JO(s,t,a),cursor:ZO(e,r),node:QO(r)}},t_=e=>(t,o)=>{Sx(e)(t,o)},o_=e=>()=>dx(e),n_=e=>t=>ux(e,t),r_=e=>t=>cx(e,t),s_=e=>()=>Of(e),a_=e=>ye(e,"items"),i_=e=>ye(e,"format"),l_=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],c_=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=c_(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${s(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),d_=e=>rf(e).map((t=>{const o=((e,t)=>{const o=c_(t),n=t=>{L(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return sf(e)?l_.concat(o):o})).getOr(l_),u_=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),m_=(e,t,o,n)=>{const r=t=>z(t,(t=>a_(t)?(e=>{const t=r(e.items);return{...e,type:"submenu",getStyleItems:y(t)}})(t):i_(t)?(e=>u_(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const r=s(t.name)?t.name:Qs(t.title),a=`custom-${r}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(r,i),i})(t)));return r(t)},g_=lC.trim,p_=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},h_=p_("true"),f_=p_("false"),b_=(e,t,o,n,r)=>({type:e,title:t,url:o,level:n,attach:r}),v_=e=>e.innerText||e.textContent,y_=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&w_(e),x_=e=>e&&/^(H[1-6])$/.test(e.nodeName),w_=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return h_(t)}return!1})(e)&&!f_(e),S_=e=>x_(e)&&w_(e),k_=e=>{var t;const o=(e=>e.id?e.id:Qs("h"))(e);return b_("header",null!==(t=v_(e))&&void 0!==t?t:"","#"+o,(e=>x_(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},C_=e=>{const t=e.id||e.name,o=v_(e);return b_("anchor",o||"#"+t,"#"+t,0,b)},O_=e=>g_(e.title).length>0,__=e=>{const t=(e=>{const t=z(Nc(Ie(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>z(U(e,S_),k_))(t).concat((e=>z(U(e,y_),C_))(t)),O_)},T_="tinymce-url-history",E_=e=>s(e)&&/^https?/.test(e),M_=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&K(t,E_));var t})).isNone(),A_=()=>{const e=Ky.getItem(T_);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+T_+" was not valid JSON",e),{};throw e}return M_(t)?t:(console.log("Local storage "+T_+" was not valid format",t),{})},D_=e=>{const t=A_();return be(t,e).getOr([])},B_=(e,t)=>{if(!E_(e))return;const o=A_(),n=be(o,t).getOr([]),r=U(n,(t=>t!==e));o[t]=[e].concat(r).slice(0,5),(e=>{if(!M_(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));Ky.setItem(T_,JSON.stringify(e))})(o)},F_=e=>!!e,I_=e=>ce(lC.makeMap(e,/[, ]/),F_),R_=e=>M.from(yf(e)),N_=e=>M.from(e).filter(s).getOrUndefined(),V_=e=>({getHistory:D_,addToHistory:B_,getLinkInformation:()=>(e=>Sf(e)?M.some({targets:__(e.getBody()),anchorTop:N_(kf(e)),anchorBottom:N_(Cf(e))}):M.none())(e),getValidationHandler:()=>(e=>M.from(xf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=M.from(wf(e)).filter(F_).map(I_);return R_(e).fold(_,(e=>t.fold(T,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?R_(e):M.none():o[t]?R_(e):M.none()})(e,t).map((o=>n=>Px((r=>{const i={filetype:t,fieldname:n.fieldname,...M.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!s(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");r({value:e,meta:t})}),n.value,i)})))))(e,t)}),H_=Zu,z_=Ru,L_=y([ur("shell",!1),Xn("makeItem"),ur("setupItem",b),au("listBehaviours",[Np])]),P_=Bu({name:"items",overrides:()=>({behaviours:gl([Np.config({})])})}),U_=y([P_]),W_=sm({name:y("CustomList")(),configFields:L_(),partFields:U_(),factory:(e,t,o,n)=>{const r=e.shell?{behaviours:[Np.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:r.components,behaviours:su(e.listBehaviours,r.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?M.some(n):ju(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const r=Np.contents(n),s=o.length,a=s-r.length,i=a>0?V(a,(()=>e.makeItem())):[],l=r.slice(s);L(l,(e=>Np.remove(n,e))),L(i,(e=>Np.append(n,e)));const c=Np.contents(n);L(c,((n,r)=>{e.setupItem(t,n,o[r],r)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),j_=y([Xn("dom"),ur("shell",!0),nu("toolbarBehaviours",[Np])]),G_=y([Bu({name:"groups",overrides:()=>({behaviours:gl([Np.config({})])})})]),$_=sm({name:"Toolbar",configFields:j_(),partFields:G_(),factory:(e,t,o,n)=>{const r=e.shell?{behaviours:[Np.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:r.components,behaviours:su(e.toolbarBehaviours,r.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?M.some(n):ju(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Np.set(e,o)}))}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),q_=b,X_=_,K_=y([]);var Y_,J_=Object.freeze({__proto__:null,setup:q_,isDocked:X_,getBehaviours:K_});const Z_=e=>(xe(Dt(e,"position"),"fixed")?M.none():ot(e)).orThunk((()=>{const t=Be("span");return et(e).bind((e=>{Fo(e,t);const o=ot(t);return No(t),o}))})),Q_=e=>Z_(e).map(Wt).getOrThunk((()=>Pt(0,0))),eT=wr([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),tT=(e,t)=>{const o=e.element;Da(o,t.transitionClass),Ba(o,t.fadeOutClass),Da(o,t.fadeInClass),t.onShow(e)},oT=(e,t)=>{const o=e.element;Da(o,t.transitionClass),Ba(o,t.fadeInClass),Da(o,t.fadeOutClass),t.onHide(e)},nT=(e,t,o)=>K(e,(e=>{switch(e){case"bottom":return((e,t)=>e.bottom<=t.bottom)(t,o);case"top":return((e,t)=>e.y>=t.y)(t,o)}})),rT=(e,t)=>t.getInitialPos().map((t=>Go(t.bounds.x,t.bounds.y,$t(e),Ht(e)))),sT=(e,t,o)=>o.getInitialPos().bind((n=>{switch(o.clearInitialPos(),n.position){case"static":return M.some(eT.static());case"absolute":const o=Z_(e).map($o).getOrThunk((()=>$o(ht())));return M.some(eT.absolute(_l("absolute",be(n.style,"left").map((e=>t.x-o.x)),be(n.style,"top").map((e=>t.y-o.y)),be(n.style,"right").map((e=>o.right-t.right)),be(n.style,"bottom").map((e=>o.bottom-t.bottom)))));default:return M.none()}})),aT=(e,t,o)=>{const n=e.element;return xe(Dt(n,"position"),"fixed")?((e,t,o)=>rT(e,o).filter((e=>nT(o.getModes(),e,t))).bind((t=>sT(e,t,o))))(n,t,o):((e,t,o)=>{const n=$o(e);if(nT(o.getModes(),n,t))return M.none();{((e,t,o)=>{o.setInitialPos({style:Bt(e),position:Mt(e,"position")||"static",bounds:t})})(e,n,o);const r=Xo(),s=n.x-r.x,a=t.y-r.y,i=r.bottom-t.bottom,l=n.y<=t.y;return M.some(eT.fixed(_l("fixed",M.some(s),l?M.some(a):M.none(),M.none(),l?M.none():M.some(i))))}})(n,t,o)},iT=(e,t,o)=>{o.setDocked(!1),L(["left","right","top","bottom","position"],(t=>It(e.element,t))),t.onUndocked(e)},lT=(e,t,o,n)=>{const r="fixed"===n.position;o.setDocked(r),Tl(e.element,n),(r?t.onDocked:t.onUndocked)(e)},cT=(e,t,o,n,r=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((s=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(s,n);a!==o.isVisible()&&(o.setVisible(a),r&&!a?(Ia(e.element,[t.fadeOutClass]),t.onHide(e)):(a?tT:oT)(e,t))}))}))},dT=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);o.isDocked()&&cT(e,t,o,n),aT(e,n,o).each((r=>{r.fold((()=>iT(e,t,o)),(n=>lT(e,t,o,n)),(r=>{cT(e,t,o,n,!0),lT(e,t,o,r)}))}))})(e,t,o)},uT=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1),((e,t)=>{const o=e.element;return rT(o,t).bind((e=>sT(o,e,t)))})(e,o).each((n=>{n.fold((()=>iT(e,t,o)),(n=>lT(e,t,o,n)),b)})),o.setVisible(!0),t.contextual.each((t=>{Ra(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),dT(e,t,o)})(e,t,o)};var mT=Object.freeze({__proto__:null,refresh:dT,reset:uT,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n)}),gT=Object.freeze({__proto__:null,events:(e,t)=>As([Ls(Xr(),((o,n)=>{e.contextual.each((e=>{Fa(o.element,e.transitionClass)&&(Ra(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Fs(ms(),((o,n)=>{dT(o,e,t)})),Fs(gs(),((o,n)=>{uT(o,e,t)}))])}),pT=[dr("contextual",[Jn("fadeInClass"),Jn("fadeOutClass"),Jn("transitionClass"),Qn("lazyContext"),wi("onShow"),wi("onShown"),wi("onHide"),wi("onHidden")]),br("lazyViewport",Xo),vr("modes",["top","bottom"],Bn),wi("onDocked"),wi("onUndocked")];const hT=hl({fields:pT,name:"docking",active:gT,apis:mT,state:Object.freeze({__proto__:null,init:e=>{const t=xr(!1),o=xr(!0),n=Ul(),r=xr(e.modes);return ba({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:r.get,setModes:r.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${r.get().join(",")}`})}})}),fT=y(Qs("toolbar-height-change")),bT={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},vT="tox-tinymce--toolbar-sticky-on",yT="tox-tinymce--toolbar-sticky-off",xT=(e,t)=>R(hT.getModes(e),t),wT=e=>{const t=e.element;tt(t).each((o=>{const n="padding-"+hT.getModes(e)[0];if(hT.isDocked(e)){const e=$t(o);_t(t,"width",e+"px"),_t(o,n,(e=>zt(e)+(parseInt(Mt(e,"margin-top"),10)||0)+(parseInt(Mt(e,"margin-bottom"),10)||0))(t)+"px")}else It(t,"width"),It(o,n)}))},ST=(e,t)=>{t?(Ba(e,bT.fadeOutClass),Ia(e,[bT.transitionClass,bT.fadeInClass])):(Ba(e,bT.fadeInClass),Ia(e,[bT.fadeOutClass,bT.transitionClass]))},kT=(e,t)=>{const o=Ie(e.getContainer());t?(Da(o,vT),Ba(o,yT)):(Da(o,yT),Ba(o,vT))},CT=(e,t)=>{const o=Ul(),n=t.getSink,r=e=>{n().each((t=>e(t.element)))},s=t=>{e.inline||wT(t),kT(e,hT.isDocked(t)),t.getSystem().broadcastOn([Hd()],{}),n().each((e=>e.getSystem().broadcastOn([Hd()],{})))},a=e.inline?[]:[yl.config({channels:{[fT()]:{onReceive:wT}}})];return[Up.config({}),hT.config({contextual:{lazyContext:t=>{const o=zt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer(),r=$o(Ie(n)),s=r.height-o,a=r.y+(xT(t,"top")?0:o);return M.some(Go(r.x,a,r.width,s))},onShow:()=>{r((e=>ST(e,!0)))},onShown:e=>{r((e=>Ra(e,[bT.transitionClass,bT.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=Ye(t);kl(o).filter((e=>!Xe(t,e))).filter((t=>Xe(t,Ie(o.dom.body))||Ke(e,t))).each((()=>wl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Cl(e).orThunk((()=>t().toOptional().bind((e=>Cl(e.element))))))(e.element,n).fold(o.clear,o.set),r((e=>ST(e,!1)))},onHidden:()=>{r((e=>Ra(e,[bT.transitionClass])))},...bT},lazyViewport:t=>{const o=Xo(),n=ff(e),r=o.y+(xT(t,"top")?n:0),s=o.height-(xT(t,"bottom")?n:0);return Go(o.x,r,o.width,s)},modes:[t.header.getDockingMode()],onDocked:s,onUndocked:s}),...a]};var OT=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(hT.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(wT)})),e.on("SkinLoaded",(()=>{o().each((e=>{hT.isDocked(e)?hT.reset(e):hT.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(hT.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{hT.refresh(t);const o=t.element;Sg(o)&&((e,t)=>{const o=Ye(t),n=Qe(t).dom.innerHeight,r=Vo(o),s=Ie(e.elm),a=qo(s),i=Ht(s),l=a.y,c=l+i,d=Wt(t),u=Ht(t),m=d.top,g=m+u,p=Math.abs(m-r.top)<2,h=Math.abs(g-(r.top+n))<2;if(p&&l<g)Ho(r.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Ho(r.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{kT(e,!1)}))},isDocked:e=>e().map(hT.isDocked).getOr(!1),getBehaviours:CT});const _T=Cn([Nb,Kn("items",_n([En([Vb,or("items",Bn)]),Bn]))].concat(mv)),TT=[ar("text"),ar("tooltip"),ar("icon"),mr("search",!1,_n([Fn,Cn([ar("placeholder")])],(e=>d(e)?e?M.some({placeholder:M.none()}):M.none():M.some(e)))),Qn("fetch"),br("onSetup",(()=>b))],ET=Cn([Nb,...TT]),MT=e=>Ln("menubutton",ET,e),AT=Cn([Nb,Jb,Yb,Kb,ev,Wb,qb,hr("presets","normal",["normal","color","listpreview"]),sv(1),Gb,$b]);var DT=rm({factory:(e,t)=>{const o={focus:Tp.focusIn,setMenus:(e,o)=>{const n=z(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=MT(o).mapError((e=>Wn(e))).getOrDie();return rO(n,"tox-mbtn",t.backstage,M.some("menuitem"))}));Np.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:gl([Np.config({}),Vp("menubar-events",[Ps((t=>{e.onSetup(t)})),Fs(zr(),((e,t)=>{ri(e.element,".tox-mbtn--active").each((o=>{si(t.event.target,".tox-mbtn").each((t=>{Xe(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{ow.expand(e),ow.close(o),Up.focus(e)}))}))}))}))})),Fs(vs(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{ow.isOpen(o)&&(ow.expand(e),ow.close(o))}))}))}))]),Tp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),M.some(!0))}),Mw.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Xn("dom"),Xn("uid"),Xn("onEscape"),Xn("backstage"),ur("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const BT=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),FT=e=>e.dimension.property,IT=(e,t)=>e.dimension.getDimension(t),RT=(e,t)=>{const o=BT(e,t);Ra(o,[t.shrinkingClass,t.growingClass])},NT=(e,t)=>{Ba(e.element,t.openClass),Da(e.element,t.closedClass),_t(e.element,FT(t),"0px"),Rt(e.element)},VT=(e,t)=>{Ba(e.element,t.closedClass),Da(e.element,t.openClass),It(e.element,FT(t))},HT=(e,t,o,n)=>{o.setCollapsed(),_t(e.element,FT(t),IT(t,e.element)),RT(e,t),NT(e,t),t.onStartShrink(e),t.onShrunk(e)},zT=(e,t,o,n)=>{const r=n.getOrThunk((()=>IT(t,e.element)));o.setCollapsed(),_t(e.element,FT(t),r),Rt(e.element);const s=BT(e,t);Ba(s,t.growingClass),Da(s,t.shrinkingClass),NT(e,t),t.onStartShrink(e)},LT=(e,t,o)=>{const n=IT(t,e.element);("0px"===n?HT:zT)(e,t,o,M.some(n))},PT=(e,t,o)=>{const n=BT(e,t),r=Fa(n,t.shrinkingClass),s=IT(t,e.element);VT(e,t);const a=IT(t,e.element);(r?()=>{_t(e.element,FT(t),s),Rt(e.element)}:()=>{NT(e,t)})(),Ba(n,t.shrinkingClass),Da(n,t.growingClass),VT(e,t),_t(e.element,FT(t),a),o.setExpanded(),t.onStartGrow(e)},UT=(e,t,o)=>{const n=BT(e,t);return!0===Fa(n,t.growingClass)},WT=(e,t,o)=>{const n=BT(e,t);return!0===Fa(n,t.shrinkingClass)};var jT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){It(e.element,FT(t));const o=IT(t,e.element);_t(e.element,FT(t),o)}},grow:(e,t,o)=>{o.isExpanded()||PT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&LT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&HT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:UT,isShrinking:WT,isTransitioning:(e,t,o)=>UT(e,t)||WT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?LT:PT)(e,t,o)},disableTransitions:RT,immediateGrow:(e,t,o)=>{o.isExpanded()||(VT(e,t),_t(e.element,FT(t),IT(t,e.element)),RT(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),GT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return ya(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Sr(t.dimension.property,"0px")})},events:(e,t)=>As([Ls(Xr(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(RT(o,e),t.isExpanded()&&It(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),$T=[Xn("closedClass"),Xn("openClass"),Xn("shrinkingClass"),Xn("growingClass"),nr("getAnimationRoot"),wi("onShrunk"),wi("onStartShrink"),wi("onGrown"),wi("onStartGrow"),ur("expanded",!1),Kn("dimension",jn("property",{width:[Oi("property","width"),Oi("getDimension",(e=>$t(e)+"px"))],height:[Oi("property","height"),Oi("getDimension",(e=>Ht(e)+"px"))]}))];const qT=hl({fields:$T,name:"sliding",active:GT,apis:jT,state:Object.freeze({__proto__:null,init:e=>{const t=xr(e.expanded);return ba({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:S(t.set,!1),setExpanded:S(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),XT="container",KT=[nu("slotBehaviours",[])],YT=e=>"<alloy.field."+e+">",JT=(e,t)=>{const o=t=>Xu(e),n=(t,o)=>(n,r)=>ju(n,e,r).map((e=>t(e,r))).getOr(o),r=(e,t)=>"true"!==xt(e.element,"aria-hidden"),s=n(r,!1),a=n(((e,t)=>{if(r(e)){const o=e.element;_t(o,"display","none"),vt(o,"aria-hidden","true"),Os(e,ys(),{name:t,visible:!1})}})),i=(l=a,(e,t)=>{L(t,(t=>l(e,t)))});var l;const c=n(((e,t)=>{if(!r(e)){const o=e.element;It(o,"display"),kt(o,"aria-hidden"),Os(e,ys(),{name:t,visible:!0})}})),d={getSlotNames:o,getSlot:(t,o)=>ju(t,e,o),isShowing:s,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:c};return{uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.slotBehaviours),apis:d}},ZT=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>ha(e))),QT={...ZT,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),zu(XT,YT(t),o)),record:y(e)}})(),o=e(t),n=t.record(),r=z(n,(e=>Au({name:e,pname:YT(e)})));return em(XT,KT,r,JT,o)}},eE=Cn([Yb,Jb,br("onShow",b),br("onHide",b),qb]),tE=e=>({element:()=>e.element.dom}),oE=(e,t)=>{const o=z(ae(t),(e=>{const o=t[e],n=Pn((e=>Ln("sidebar",eE,e))(o));return{name:e,getApi:tE,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return z(o,(t=>{const n=xr(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Wv([iy(t,n),ly(t,n),Fs(ys(),((e,t)=>{const n=t.event,r=G(o,(e=>e.name===n.name));r.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},nE=e=>QT.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:oE(t,e),slotBehaviours:Wv([Ps((e=>QT.hideAllSlots(e)))])}))),rE=e=>cm.getCurrent(e).bind((e=>qT.isGrowing(e)||qT.hasGrown(e)?cm.getCurrent(e).bind((e=>G(QT.getSlotNames(e),(t=>QT.isShowing(e,t))))):M.none())),sE=Qs("FixSizeEvent"),aE=Qs("AutoSizeEvent");var iE=Object.freeze({__proto__:null,block:(e,t,o,n)=>{vt(e.element,"aria-busy",!0);const r=t.getRoot(e).getOr(e),s=gl([Tp.config({mode:"special",onTab:()=>M.some(!0),onShiftTab:()=>M.some(!0)}),Up.config({})]),a=n(r,s),i=r.getSystem().build(a);Np.append(r,Ya(i)),i.hasConfigured(Tp)&&t.focus&&Tp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Np.remove(r,i)))},unblock:(e,t,o)=>{kt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()}}),lE=[br("getRoot",M.none),fr("focus",!0),wi("onBlock"),wi("onUnblock")];const cE=hl({fields:lE,name:"blocking",apis:iE,state:Object.freeze({__proto__:null,init:()=>{const e=Ll((e=>e.destroy()));return ba({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),dE=e=>{const t=De(e),o=nt(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),r=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),s=0===o.length?{}:{innerHtml:Gs(t)};return{tag:ze(t),classes:r,attributes:n,...s}},uE=e=>cm.getCurrent(e).each((e=>wl(e.element))),mE=(e,t,o)=>{const n=xr(!1),r=Ul(),s=o=>{var r;n.get()&&(!(e=>"focusin"===e.type)(r=o)||!(r.composed?oe(r.composedPath()):M.from(r.target)).map(Ie).filter(Pe).exists((e=>Fa(e,"mce-pastebin"))))&&(o.preventDefault(),uE(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",s),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&s(e)}))}));const a=r=>{r!==n.get()&&(n.set(r),((e,t,o,n)=>{const r=t.element;if(((e,t)=>{const o="tabindex",n="data-mce-tabindex";M.from(e.iframeElement).map(Ie).each((e=>{t?(wt(e,o).each((t=>vt(e,n,t))),vt(e,o,-1)):(kt(e,o),wt(e,n).each((t=>{vt(e,o,t),kt(e,n)})))}))})(e,o),o)cE.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:dE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),It(r,"display"),kt(r,"aria-hidden"),e.hasFocus()&&uE(t);else{const o=cm.getCurrent(t).exists((e=>Sl(e.element)));cE.unblock(t),_t(r,"display","none"),vt(r,"aria-hidden","true"),o&&e.focus()}})(e,t(),r,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,r))};e.on("ProgressState",(t=>{if(r.on(clearTimeout),h(t.time)){const o=Eh.setEditorTimeout(e,(()=>a(t.state)),t.time);r.set(o)}else a(t.state),r.clear()}))},gE=(e,t,o)=>({within:e,extra:t,withinWidth:o}),pE=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return M.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,r=U(n,(e=>e.finish<=t)),s=W(r,((e,t)=>e+t.width),0);return{within:r,extra:n.slice(r.length),withinWidth:s}},hE=e=>z(e,(e=>e.element)),fE=(e,t)=>{const o=z(t,(e=>Ya(e)));$_.setGroups(e,o)},bE=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const r=Gu(e,t,"primary"),s=Nx.getCoupled(e,"overflowGroup");_t(r.element,"visibility","hidden");const a=n.concat([s]),i=se(a,(e=>Cl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),fE(r,a);const l=((e,t,o,n)=>{const r=((e,t,o)=>{const n=pE(t,e,o);return 0===n.extra.length?M.some(n):M.none()})(e,t,o).getOrThunk((()=>pE(t,e-o(n),o))),s=r.within,a=r.extra,i=r.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=hE(e.concat(t));return gE(n,[],o)})(s,a,i):a.length>=1?((e,t,o,n)=>{const r=hE(e).concat([o]);return gE(r,hE(t),n)})(s,a,n,i):((e,t,o)=>gE(hE(e),[],o))(s,0,i)})($t(r.element),t.builtGroups.get(),(e=>$t(e.element)),s);0===l.extra.length?(Np.remove(r,s),o([])):(fE(r,l.within),o(l.extra)),It(r.element,"visibility"),Rt(r.element),i.each(Up.focus)},vE=y([nu("splitToolbarBehaviours",[Nx]),$n("builtGroups",(()=>xr([])))]),yE=y([yi(["overflowToggledClass"]),lr("getOverflowBounds"),Xn("lazySink"),$n("overflowGroups",(()=>xr([]))),wi("onOpened"),wi("onClosed")].concat(vE())),xE=y([Au({factory:$_,schema:j_(),name:"primary"}),Du({schema:j_(),name:"overflow"}),Du({name:"overflow-button"}),Du({name:"overflow-group"})]),wE=y(((e,t)=>{((e,t)=>{const o=Gt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);_t(e,"max-width",o+"px")})(e,Math.floor(t))})),SE=y([yi(["toggledClass"]),Xn("lazySink"),Qn("fetch"),lr("getBounds"),dr("fireDismissalEventInstead",[ur("event",fs())]),dc(),wi("onToggled")]),kE=y([Du({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:gl([Yp.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Du({factory:$_,schema:j_(),name:"toolbar",overrides:e=>({toolbarBehaviours:gl([Tp.config({mode:"cyclic",onEscape:t=>(ju(t,e,"button").each(Up.focus),M.none())})])})})]),CE=(e,t)=>{const o=Nx.getCoupled(e,"toolbarSandbox");Nd.isOpen(o)?Nd.close(o):Nd.open(o,t.toolbar())},OE=(e,t,o,n)=>{const r=o.getBounds.map((e=>e())),s=o.lazySink(e).getOrDie();ud.positionWithinBounds(s,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:wE()}}},r)},_E=(e,t,o,n,r)=>{$_.setGroups(t,r),OE(e,t,o,n),Yp.on(e)},TE=sm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Mh.sketch({...n.button(),action:e=>{CE(e,n)},buttonBehaviours:iu({dump:n.button().buttonBehaviours},[Nx.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=ii();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:gl([Tp.config({mode:"special",onEscape:e=>(Nd.close(e),M.some(!0))}),Nd.config({onOpen:(r,s)=>{o.fetch().get((r=>{_E(e,s,o,t.layouts,r),n.link(e.element),Tp.focusIn(s)}))},onClose:()=>{Yp.off(e),Up.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>li(o,n)||li(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),yl.config({channels:{...Pd({isExtraPart:_,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Wd({doReposition:()=>{Nd.getState(Nx.getCoupled(e,"toolbarSandbox")).each((n=>{OE(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Nd.getState(Nx.getCoupled(t,"toolbarSandbox")).each((r=>{_E(t,r,e,o.layouts,n)}))},reposition:t=>{Nd.getState(Nx.getCoupled(t,"toolbarSandbox")).each((n=>{OE(t,n,e,o.layouts)}))},toggle:e=>{CE(e,n)},getToolbar:e=>Nd.getState(Nx.getCoupled(e,"toolbarSandbox")),isOpen:e=>Nd.isOpen(Nx.getCoupled(e,"toolbarSandbox"))}}),configFields:SE(),partFields:kE(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),EE=y([Xn("items"),yi(["itemSelector"]),nu("tgroupBehaviours",[Tp])]),ME=y([Fu({name:"items",unit:"item"})]),AE=sm({name:"ToolbarGroup",configFields:EE(),partFields:ME(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:su(e.tgroupBehaviours,[Tp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),DE=e=>z(e,(e=>Ya(e))),BE=(e,t,o)=>{bE(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{TE.setGroups(e,DE(n))}))}))},FE=sm({name:"SplitFloatingToolbar",configFields:yE(),partFields:xE(),factory:(e,t,o,n)=>{const r=Ah(TE.sketch({fetch:()=>Px((t=>{t(DE(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Ki,Xi],onRtl:()=>[Xi,Ki],onBottomLtr:()=>[Ji,Yi],onBottomRtl:()=>[Yi,Ji]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:su(e.splitToolbarBehaviours,[Nx.config({others:{overflowGroup:()=>AE.sketch({...n["overflow-group"](),items:[r.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(z(o,t.getSystem().build)),BE(t,r,e)},refresh:t=>BE(t,r,e),toggle:e=>{r.getOpt(e).each((e=>{TE.toggle(e)}))},isOpen:e=>r.getOpt(e).map(TE.isOpen).getOr(!1),reposition:e=>{r.getOpt(e).each((e=>{TE.reposition(e)}))},getOverflow:e=>r.getOpt(e).bind(TE.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),IE=y([yi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),wi("onOpened"),wi("onClosed")].concat(vE())),RE=y([Au({factory:$_,schema:j_(),name:"primary"}),Au({factory:$_,schema:j_(),name:"overflow",overrides:e=>({toolbarBehaviours:gl([qT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{ju(t,e,"overflow-button").each((e=>{Yp.off(e),Up.focus(e)})),e.onClosed(t)},onGrown:t=>{Tp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{ju(t,e,"overflow-button").each(Yp.on)}}),Tp.config({mode:"acyclic",onEscape:t=>(ju(t,e,"overflow-button").each(Up.focus),M.some(!0))})])})}),Du({name:"overflow-button",overrides:e=>({buttonBehaviours:gl([Yp.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Du({name:"overflow-group"})]),NE=(e,t)=>{ju(e,t,"overflow-button").bind((()=>ju(e,t,"overflow"))).each((o=>{VE(e,t),qT.toggleGrow(o)}))},VE=(e,t)=>{ju(e,t,"overflow").each((o=>{bE(e,t,(e=>{const t=z(e,(e=>Ya(e)));$_.setGroups(o,t)})),ju(e,t,"overflow-button").each((e=>{qT.hasGrown(o)&&Yp.on(e)})),qT.refresh(o)}))},HE=sm({name:"SplitSlidingToolbar",configFields:IE(),partFields:RE(),factory:(e,t,o,n)=>{const r="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:su(e.splitToolbarBehaviours,[Nx.config({others:{overflowGroup:e=>AE.sketch({...n["overflow-group"](),items:[Mh.sketch({...n["overflow-button"](),action:t=>{Cs(e,r)}})]})}}),Vp("toolbar-toggle-events",[Fs(r,(t=>{NE(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=z(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),VE(t,e)},refresh:t=>VE(t,e),toggle:t=>NE(t,e),isOpen:t=>((e,t)=>ju(e,t,"overflow").map(qT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),zE=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[AE.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:gl([Mw.config({}),Up.config({})])}},LE=e=>AE.sketch(zE(e)),PE=(e,t)=>{const o=Ps((t=>{const o=z(e.initGroups,LE);$_.setGroups(t,o)}));return gl([sy(e.providers.isDisabled),oy(),Tp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Vp("toolbar-events",[o])])},UE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":zE({title:M.none(),items:[]}),"overflow-button":iO({name:"more",icon:M.some("more-drawer"),enabled:!0,tooltip:M.some("More..."),primary:!1,buttonType:M.none(),borderless:!1},M.none(),e.providers)},splitToolbarBehaviours:PE(e,t)}},WE=e=>{const t=UE(e),o=FE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return FE.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=qo(t),n=Ze(t),r=qo(n),s=Math.max(n.dom.scrollHeight,r.height);return Go(o.x+4,r.y,o.width-8,s)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},jE=e=>{const t=HE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=HE.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=UE(e);return HE.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([fT()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([fT()],{type:"closed"}),e.onToggled(t,!1)}})},GE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return $_.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===Wh.scrolling?["tox-toolbar--scrolling"]:[])},components:[$_.parts.groups({})],toolbarBehaviours:PE(e,t)})},$E=[Zn("type",["button"]),zb,hr("buttonType","secondary",["primary","secondary"]),Qn("onAction")],qE=jn("type",{button:$E}),XE=Cn([vr("buttons",[],qE),Qn("onShow"),Qn("onHide")]);var KE=sm({name:"silver.View",configFields:[Xn("viewConfig")],partFields:[Bu({factory:{sketch:e=>{const t=z(e.buttons,(t=>((e,t)=>cO({text:e.text,enabled:!0,primary:!1,name:"name",icon:M.none(),borderless:!1,buttonType:M.some(e.buttonType)},(t=>{e.onAction()}),t))(t,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:["tox-view__header"]},components:[Sw.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),Sw.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:t})]}}},schema:[Xn("buttons"),Xn("providers")],name:"header"}),Bu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const r={getPane:t=>H_.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:r}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const YE=(e,t,o)=>pe(t,((t,n)=>{const r=Pn(Ln("view",XE,t));return e.slot(n,KE.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:r,components:[...r.buttons.length>0?[KE.parts.header({buttons:r.buttons,providers:o})]:[],KE.parts.pane({})]}))})),JE=(e,t)=>QT.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:YE(o,e,t),slotBehaviours:Wv([Ps((e=>QT.hideAllSlots(e)))])}))),ZE=e=>G(QT.getSlotNames(e),(t=>QT.isShowing(e,t))),QE=(e,t,o)=>{QT.getSlot(e,t).each((e=>{KE.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:y(n)}))}))}))};var eM=rm({factory:(e,t)=>{const o={setViews:(e,o)=>{Np.set(e,[JE(o,t.backstage.shared.providers)])},whichView:e=>cm.getCurrent(e).bind(ZE),toggleView:(e,t,o,n)=>cm.getCurrent(e).exists((r=>{const s=ZE(r),a=s.exists((e=>n===e)),i=QT.getSlot(r,n).isSome();return i&&(QT.hideAllSlots(r),a?((e=>{const t=e.element;_t(t,"display","none"),vt(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;It(t,"display"),kt(t,"aria-hidden")})(e),QT.showSlot(r,n),((e,t)=>{QE(e,t,KE.getOnShow)})(r,n)),s.each((e=>((e,t)=>QE(e,t,KE.getOnHide))(r,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:gl([Np.config({}),cm.config({find:e=>{const t=Np.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[Xn("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,r)=>e.toggleView(t,o,n,r),whichView:(e,t)=>e.whichView(t)}});const tM=z_.optional({factory:DT,name:"menubar",schema:[Xn("backstage")]}),oM=z_.optional({factory:{sketch:e=>W_.sketch({uid:e.uid,dom:e.dom,listBehaviours:gl([Tp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>GE({type:e.type,uid:Qs("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),M.some(!0))}),setupItem:(e,t,o,n)=>{$_.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[Xn("dom"),Xn("onEscape")]}),nM=z_.optional({factory:{sketch:e=>{const t=(e=>e.type===Wh.sliding?jE:e.type===Wh.floating?WE:GE)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),M.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[Xn("dom"),Xn("onEscape"),Xn("getSink")]}),rM=z_.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?CT:K_;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:gl(o(t,e.sharedBackstage))}}},name:"header",schema:[Xn("dom")]}),sM=z_.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_source=TinyMCE&utm_medium=SPAP&utm_campaign=SPAP&utm_id=editorreferral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[Xn("dom")]}),aM=z_.optional({name:"socket",schema:[Xn("dom")]}),iM=z_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:gl([Mw.config({}),Up.config({}),qT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{cm.getCurrent(e).each(QT.hideAllSlots),Cs(e,aE)},onGrown:e=>{Cs(e,aE)},onStartGrow:e=>{Os(e,sE,{width:Dt(e.element,"width").getOr("")})},onStartShrink:e=>{Os(e,sE,{width:$t(e.element)+"px"})}}),Np.config({}),cm.config({find:e=>{const t=Np.contents(e);return oe(t)}})])}],behaviours:gl([Zk(0),Vp("sidebar-sliding-events",[Fs(sE,((e,t)=>{_t(e.element,"width",t.event.width)})),Fs(aE,((e,t)=>{It(e.element,"width")}))])])})},name:"sidebar",schema:[Xn("dom")]}),lM=z_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:gl([Np.config({}),cE.config({focus:!1}),cm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[Xn("dom")]}),cM=z_.optional({factory:eM,name:"viewWrapper",schema:[Xn("backstage")]}),dM=z_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var uM=sm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const r={getSocket:t=>H_.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{H_.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{cm.getCurrent(e).each((e=>{Np.set(e,[nE(t)]);const n=null==o?void 0:o.toLowerCase();s(n)&&ve(t,n)&&cm.getCurrent(e).each((t=>{QT.showSlot(t,n),qT.immediateGrow(e),It(e.element,"width")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{H_.getPart(t,e,"sidebar").each((e=>((e,t)=>{cm.getCurrent(e).each((e=>{cm.getCurrent(e).each((o=>{qT.hasGrown(e)?QT.isShowing(o,t)?qT.shrink(e):(QT.hideAllSlots(o),QT.showSlot(o,t)):(QT.hideAllSlots(o),QT.showSlot(o,t),qT.grow(e))}))}))})(e,o)))},whichSidebar:t=>H_.getPart(t,e,"sidebar").bind(rE).getOrNull(),getHeader:t=>H_.getPart(t,e,"header"),getToolbar:t=>H_.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{H_.getPart(t,e,"toolbar").each((e=>{const t=z(o,LE);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{H_.getPart(t,e,"multiple-toolbar").each((e=>{const t=z(o,(e=>z(e,LE)));W_.setItems(e,t)}))},refreshToolbar:t=>{H_.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{H_.getPart(t,e,"toolbar").each((e=>{var t,o;o=t=>t(e),null!=(t=e.getApis().toggle)?M.some(o(t)):M.none()}))},isToolbarDrawerToggled:t=>H_.getPart(t,e,"toolbar").bind((e=>M.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>H_.getPart(t,e,"throbber"),focusToolbar:t=>{H_.getPart(t,e,"toolbar").orThunk((()=>H_.getPart(t,e,"multiple-toolbar"))).each((e=>{Tp.focusIn(e)}))},setMenubar:(t,o)=>{H_.getPart(t,e,"menubar").each((e=>{DT.setMenus(e,o)}))},focusMenubar:t=>{H_.getPart(t,e,"menubar").each((e=>{DT.focus(e)}))},setViews:(t,o)=>{H_.getPart(t,e,"viewWrapper").each((e=>{eM.setViews(e,o)}))},toggleView:(t,o)=>H_.getPart(t,e,"viewWrapper").exists((e=>eM.toggleView(e,(()=>r.showMainView(t)),(()=>r.hideMainView(t)),o))),whichView:t=>H_.getPart(t,e,"viewWrapper").bind(eM.whichView).getOrNull(),hideMainView:t=>{n=r.isToolbarDrawerToggled(t),n&&r.toggleToolbarDrawer(t),H_.getPart(t,e,"editorContainer").each((e=>{const t=e.element;_t(t,"display","none"),vt(t,"aria-hidden","true")}))},showMainView:t=>{n&&r.toggleToolbarDrawer(t),H_.getPart(t,e,"editorContainer").each((e=>{const t=e.element;It(t,"display"),kt(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:r,behaviours:e.behaviours}},configFields:[Xn("dom"),Xn("behaviours")],partFields:[rM,tM,nM,oM,aM,iM,sM,lM,cM,dM],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const mM={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},gM=e=>e.split(" "),pM=(e,t)=>{const o={...mM,...t.menus},n=ae(t.menus).length>0,r=void 0===t.menubar||!0===t.menubar?gM("file edit view insert format tools table help"):gM(!1===t.menubar?"":t.menubar),a=U(r,(e=>{const o=ve(mM,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=z(a,(n=>{const r=o[n];return((e,t,o)=>{const n=cf(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:r.title,items:gM(r.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>s(e)||"separator"!==e.type))))},hM=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},fM=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),bM=(e,t)=>fM(e,t+"/skin.min.css",e.ui.styleSheetLoader),vM=(e,t)=>{var o;return o=Ie(e.getElement()),mt(o).isSome()?fM(e,t+"/skin.shadowdom.min.css",Gh.DOM.styleSheetLoader):Promise.resolve()},yM=(e,t)=>{const o=Rf(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Ff(t)&&s(o)?Promise.all([bM(t,o),vM(t,o)]).then(hM(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)):hM(t)()},xM=S(yM,!1),wM=S(yM,!0),SM=(e,t)=>o=>{const n=Pl(),r=()=>{o.setActive(e.formatter.match(t));const r=e.formatter.formatChanged(t,o.setActive);n.set(r)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),n.clear()}},kM=(e,t,o)=>n=>{const r=()=>o(n),s=()=>{o(n),e.on(t,r)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),e.off(t,r)}},CM=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},OM=(e,t)=>()=>e.execCommand(t),_M=(e,t,o)=>{const n=(e,n,s,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return M.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>r(e,n,a)));return 0===n&&t.length<=0?M.none():M.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>r(e,n,a)))})}return M.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!s,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},r=(e,t,r)=>{const s="formatter"===e.type&&o.isInvalid(e);return 0===t?s?[]:n(e,t,!1,r).toArray():n(e,t,s,r).toArray()},s=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>r(e,n,t)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(EC(a,Jf.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:M.none()}))}}},TM=(e,t,o)=>{const n=o.dataset,r="basic"===n.type?()=>z(n.data,(e=>u_(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:_M(0,t,o),getStyleItems:r}},EM=(e,t,o)=>{const{items:n,getStyleItems:r}=TM(0,t,o),s=kM(e,"NodeChange",(e=>{const t=e.getComponent();o.updateText(t)}));return CC({text:o.icon.isSome()?M.none():o.text,icon:o.icon,tooltip:M.from(o.tooltip),role:M.none(),fetch:n.getFetch(t,r),onSetup:s,getApi:e=>({getComponent:y(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var MM;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(MM||(MM={}));const AM=(e,t,o)=>{const n=(r=((e,t)=>t===MM.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),z(r,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var r;return{type:"basic",data:n}},DM=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],BM=e=>{const t={type:"basic",data:DM};return{tooltip:"Align",text:M.none(),icon:M.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:M.none,getPreviewFor:e=>M.none,onAction:t=>()=>G(DM,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(DM,(t=>e.formatter.match(t.format))).fold(y("left"),(e=>e.title.toLowerCase()));Os(t,kC,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},FM=(e,t)=>{const o=t(),n=z(o,(e=>e.format));return M.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>ke(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},IM=e=>{const t="Paragraph",o=AM(e,"block_formats",MM.SemiColon);return{tooltip:"Blocks",text:M.some(t),icon:M.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:M.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?M.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):M.none()},onAction:CM(e),updateText:n=>{const r=FM(e,(()=>o.data)).fold(y(t),(e=>e.title));Os(n,SC,{text:r})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},RM=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],NM=e=>{const t=e.split(/\s*,\s*/);return z(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},VM=e=>{const t="System Font",o=()=>{const o=e=>e?NM(e)[0]:"",r=e.queryCommandValue("FontName"),s=n.data,a=r?r.toLowerCase():"",i=G(s,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>ke((e=>0===e.indexOf("-apple-system")&&(()=>{const t=NM(e.toLowerCase());return K(RM,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:r}},n=AM(e,"font_family_formats",MM.SemiColon);return{tooltip:"Fonts",text:M.some(t),icon:M.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>M.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),r=t.fold(y(n),(e=>e.title));Os(e,SC,{text:r})},dataset:n,shouldHide:!1,isInvalid:_}},HM={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},zM={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},LM=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(zM,e).getOr(e),PM=e=>be(HM,e).getOr(""),UM=e=>{const t=()=>{let t=M.none();const o=n.data,r=e.queryCommandValue("FontSize");if(r)for(let e=3;t.isNone()&&e>=0;e--){const n=LM(r,e),s=PM(n);t=G(o,(e=>e.format===r||e.format===n||e.format===s))}return{matchOpt:t,size:r}},o=y(M.none),n=AM(e,"font_size_formats",MM.Space);return{tooltip:"Font sizes",text:M.some("12pt"),icon:M.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),r=o.fold(y(n),(e=>e.title));Os(e,SC,{text:r})},dataset:n,shouldHide:!1,isInvalid:_}},WM=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:M.some(o),icon:M.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:M.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?M.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):M.none()},onAction:CM(e),updateText:t=>{const n=e=>a_(e)?X(e.items,n):i_(e)?[{title:e.title,format:e.format}]:[],r=X(d_(e),n),s=FM(e,y(r)).fold(y(o),(e=>e.title));Os(t,SC,{text:s})},shouldHide:af(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}};var jM=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const r=e(o,n);t.set(r)})),e.renderComponents.each((r=>{const s=r(n,t.get());(e.reuseDom?Mp:Ep)(o,s)}))};return As([Fs(os(),((t,n)=>{const r=n;if(!r.universal){const n=e.channel;R(r.channels,n)&&o(t,r.data)}})),Ps(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),GM=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),$M=[Xn("channel"),nr("renderComponents"),nr("updateState"),nr("initialData"),fr("reuseDom",!0)];const qM=hl({fields:$M,name:"reflecting",active:jM,apis:GM,state:Object.freeze({__proto__:null,init:()=>{const e=xr(M.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(M.none())}}})}),XM=y([Xn("toggleClass"),Xn("fetch"),ki("onExecute"),ur("getHotspot",M.some),ur("getAnchorOverrides",y({})),dc(),ki("onItemExecute"),nr("lazySink"),Xn("dom"),wi("onOpen"),nu("splitDropdownBehaviours",[Nx,Tp,Up]),ur("matchWidth",!1),ur("useMinWidth",!1),ur("eventOrder",{}),nr("role")].concat(Qx())),KM=Au({factory:Mh,schema:[Xn("dom")],name:"arrow",defaults:()=>({buttonBehaviours:gl([Up.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(_s)},buttonBehaviours:gl([Yp.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),YM=Au({factory:Mh,schema:[Xn("dom")],name:"button",defaults:()=>({buttonBehaviours:gl([Up.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),JM=y([KM,YM,Bu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Xn("text")],name:"aria-descriptor"}),Du({schema:[vi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),jx()]),ZM=sm({name:"SplitDropdown",configFields:XM(),partFields:JM(),factory:(e,t,o,n)=>{const r=e=>{cm.getCurrent(e).each((e=>{Fm.highlightFirst(e),Tp.focusIn(e)}))},s=t=>{Xx(e,x,t,n,r,Ch.HighlightMenuAndItem).get(b)},a=t=>{const o=Gu(t,e,"button");return _s(o),M.some(!0)},i={...As([Ps(((t,o)=>{ju(t,e,"aria-descriptor").each((e=>{const o=Qs("aria");vt(e.element,"id",o),vt(t.element,"aria-describedby",o)}))}))]),...Zp(M.some(s))},l={repositionMenus:e=>{Yp.isOn(e)&&Zx(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[ns()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:su(e.splitDropdownBehaviours,[Nx.config({others:{sandbox:t=>{const o=Gu(t,e,"arrow");return Jx(e,t,{onOpen:()=>{Yp.on(o),Yp.on(t)},onClose:()=>{Yp.off(o),Yp.off(t)}})}}}),Tp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(s(e),M.some(!0))}),Up.config({}),Yp.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),QM=e=>({isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t)}),eA=e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t)}),tA=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),oA=Qs("focus-button"),nA=(e,t,o,n,r,s)=>({dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:tA(o,s)},components:uy([e.map((e=>yC(e,s.icons))),t.map((e=>wC(e,"tox-tbtn",s)))]),eventOrder:{[Rr()]:["focusing","alloy.base.behaviour","common-button-display-events"]},buttonBehaviours:gl([sy(s.isDisabled),oy(),Vp("common-button-display-events",[Fs(Rr(),((e,t)=>{t.event.prevent(),Cs(e,oA)}))])].concat(n.map((o=>qM.config({channel:o,initialData:{icon:e,text:t},renderComponents:(e,t)=>uy([e.icon.map((e=>yC(e,s.icons))),e.text.map((e=>wC(e,"tox-tbtn",s)))])}))).toArray()).concat(r.getOr([])))}),rA=(e,t,o)=>{const n=xr(b),r=nA(e.icon,e.text,e.tooltip,M.none(),M.none(),o);return Mh.sketch({dom:r.dom,components:r.components,eventOrder:bC,buttonBehaviours:gl([Vp("toolbar-button-events",[(s={onAction:e.onAction,getApi:t.getApi},js(((e,t)=>{ay(s,e)((t=>{Os(e,fC,{buttonApi:t}),s.onAction(t)}))}))),iy(t,n),ly(t,n)]),sy((()=>!e.enabled||o.isDisabled())),oy()].concat(t.toolbarButtonBehaviours))});var s},sA=(e,t,o)=>rA(e,{toolbarButtonBehaviours:o.length>0?[Vp("toolbarButtonWith",o)]:[],getApi:QM,onSetup:e.onSetup},t),aA=(e,t,o)=>rA(e,{toolbarButtonBehaviours:[Np.config({}),Yp.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Vp("toolbarToggleButtonWith",o)]:[]),getApi:eA,onSetup:e.onSetup},t),iA=(e,t,o)=>n=>Px((e=>t.fetch(e))).map((r=>M.from(dw(cn(kx(Qs("menu-value"),r,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,Jf.CLOSE_ON_EXECUTE,t.select.getOr(_),o),{movement:Ox(t.columns,t.presets),menuBehaviours:Wv("auto"!==t.columns?[]:[Ps(((e,o)=>{Uv(e,4,lb(t.presets)).each((({numRows:t,numColumns:o})=>{Tp.setGridSize(e,t,o)}))}))])}))))),lA=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],cA=(e,t)=>(o,n,r)=>{const s=e(o).mapError((e=>Wn(e))).getOrDie();return t(s,n,r)},dA={button:cA(pv,((e,t)=>{return o=e,n=t.shared.providers,sA(o,n,[]);var o,n})),togglebutton:cA(bv,((e,t)=>{return o=e,n=t.shared.providers,aA(o,n,[]);var o,n})),menubutton:cA(MT,((e,t)=>rO(e,"tox-tbtn",t,M.none()))),splitbutton:cA((e=>Ln("SplitButton",AT,e)),((e,t)=>((e,t)=>{const o=Qs("channel-update-split-dropdown-display"),n=e=>({isEnabled:()=>!km.isDisabled(e),setEnabled:t=>km.set(e,!t),setIconFill:(t,o)=>{ri(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each((e=>{vt(e,"fill",o)}))},setActive:t=>{vt(e.element,"aria-pressed",t),ri(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Yp.set(e,t)))}))},isActive:()=>ri(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Yp.isOn)))}),r=xr(b),s={getApi:n,onSetup:e.onSetup};return ZM.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...tA(e.tooltip,t.providers)}},onExecute:t=>{e.onAction(n(t))},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:gl([ry(t.providers.isDisabled),oy(),Vp("split-dropdown-events",[Fs(oA,Up.focus),iy(s,r),ly(s,r)]),eS.config({})]),eventOrder:{[ps()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:iA(n,e,t.providers),parts:{menu:pb(0,e.columns,e.presets)},components:[ZM.parts.button(nA(e.icon,e.text,M.none(),M.some(o),M.some([Yp.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),ZM.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Vh("chevron-down",t.providers.icons)},buttonBehaviours:gl([ry(t.providers.isDisabled),oy(),Hh()])}),ZM.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared))),grouptoolbarbutton:cA((e=>Ln("GroupToolbarButton",_T,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,r={[lc]:t.shared.header.isPositionedAtTop()?ic.TopToBottom:ic.BottomToTop};if(df(o)===Wh.floating)return((e,t,o,n)=>{const r=t.shared;return TE.sketch({lazySink:r.getSink,fetch:()=>Px((t=>{t(z(o(e.items),LE))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:nA(e.icon,e.text,e.tooltip,M.none(),M.none(),r.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>mA(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,M.none())),r);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},uA={styles:(e,t)=>{const o={type:"advanced",...t.styles};return EM(e,t,WM(e,o))},fontsize:(e,t)=>EM(e,t,UM(e)),fontfamily:(e,t)=>EM(e,t,VM(e)),blocks:(e,t)=>EM(e,t,IM(e)),align:(e,t)=>EM(e,t,BM(e))},mA=(e,t,o,n)=>{const r=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=z(lA,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(uA,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):s(t)?(e=>{const t=e.split("|");return z(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=z(r,(r=>{const s=X(r.items,(r=>0===r.trim().length?[]:((e,t,o,n,r,s)=>be(t,o.toLowerCase()).orThunk((()=>s.bind((e=>se(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(uA,o.toLowerCase()).map((t=>t(e,r)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(dA,e.type).fold((()=>(console.error("skipping button defined by",e),M.none())),(n=>M.some(n(e,t,o)))))(t,r,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),M.none()))))(e,t.buttons,r,t.allowToolbarGroups,o,n).toArray()));return{title:M.from(e.translate(r.name)),items:s}}));return U(a,(e=>e.items.length>0))},gA=(e,t,o,n)=>{const r=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,s)){const t=a.map((t=>{const r={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return mA(e,r,n,M.none())}));uM.setToolbars(r,t)}else uM.setToolbar(r,mA(e,o,n,M.none()))},pA=_o(),hA=pA.os.isiOS()&&pA.os.version.major<=12;var fA=Object.freeze({__proto__:null,render:(e,t,o,n,r)=>{const{mainUi:s,uiMotherships:a}=t,i=xr(0),l=s.outerContainer;xM(e);const d=Ie(r.targetNode),u=ut(dt(d));((e,t)=>{Cd(e,t,Do)})(d,s.mothership),((e,t)=>{kd(e,t.dialogUi.mothership)})(u,t),e.on("PostRender",(()=>{uM.setSidebar(l,o.sidebar,Df(e)),gA(e,t,o,n),i.set(e.getWin().innerWidth),uM.setMenubar(l,pM(e,o)),uM.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let r=e.getWin();const s=e.getDoc().documentElement,a=xr(Pt(r.innerWidth,r.innerHeight)),i=xr(Pt(s.offsetWidth,s.offsetHeight)),l=()=>{const t=a.get();t.left===r.innerWidth&&t.top===r.innerHeight||(a.set(Pt(r.innerWidth,r.innerHeight)),qy(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set(Pt(t.offsetWidth,t.offsetHeight)),qy(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(r,"resize",l),n.bind(r,"scroll",d);const u=Gl(Ie(e.getBody()),"load",c);e.on("hide",(()=>{L(o,(e=>{_t(e.element,"display","none")}))})),e.on("show",(()=>{L(o,(e=>{It(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(r,"resize",l),n.unbind(r,"scroll",d),r=null}))})(e,t)}));const m=uM.getSocket(l).getOrDie("Could not find expected socket element");if(hA){Tt(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=jl(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}ty(e,t),e.addCommand("ToggleSidebar",((t,o)=>{uM.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=uM.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(uM.toggleView(l,o)){const t=l.element;s.mothership.broadcastOn([Vd()],{target:t}),L(a,(e=>{e.broadcastOn([Vd()],{target:t})})),c(uM.whichView(l))&&(e.focus(),e.nodeChanged())}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=uM.whichView(l))&&void 0!==e?e:""}));const g=df(e);g!==Wh.sliding&&g!==Wh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(uM.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{ey(t,!e)},isEnabled:()=>!km.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const bA=e=>/^[0-9\.]+(|px)$/i.test(""+e)?M.some(parseInt(""+e,10)):M.none(),vA=e=>h(e)?e+"px":e,yA=(e,t,o)=>{const n=t.filter((t=>e<t)),r=o.filter((t=>e>t));return n.or(r).getOr(e)},xA=e=>{const t=Qh(e),o=ef(e),n=of(e);return bA(t).map((e=>yA(e,o,n)))},{ToolbarLocation:wA,ToolbarMode:SA}=qf,kA=(e,t)=>{const o=$o(e);return{pos:t?o.y:o.bottom,bounds:o}};var CA=Object.freeze({__proto__:null,render:(e,t,o,n,r)=>{const{mainUi:s}=t,a=Ul(),i=Ie(r.targetNode),l=((e,t,o,n,r)=>{const{mainUi:s,uiMotherships:a}=o,i=Gh.DOM,l=Uf(e),c=Gf(e),d=of(e).or(xA(e)),u=n.shared.header,m=u.isPositionedAtTop,g=df(e),p=g===SA.sliding||g===SA.floating,h=xr(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?Ht(e.components()[1].element):0)):0,v=()=>{L(a,(e=>{e.broadcastOn([Hd()],{})}))},x=(e=!1)=>{if(f()){if(l||r.on((e=>{const o=d.getOrThunk((()=>{const e=bA(Mt(ht(),"margin-left")).getOr(0);return $t(ht())-Wt(t).left+e}));_t(e.element,"max-width",o+"px")})),p&&uM.refreshToolbar(s.outerContainer),l||r.on((e=>{const o=uM.getToolbar(s.outerContainer),n=b(o),r=$o(t),a=m()?Math.max(r.y-Ht(e.element)+n,0):r.bottom;Tt(s.outerContainer.element,{position:"absolute",top:Math.round(a)+"px",left:Math.round(r.x)+"px"})})),c){const t=e?hT.reset:hT.refresh;r.on(t)}v()}},w=(o=!0)=>{!l&&c&&f()&&r.on((n=>{const a=u.getDockingMode(),i=(o=>{switch(mf(e)){case wA.auto:const e=uM.getToolbar(s.outerContainer),n=b(e),r=Ht(o.element)-n,a=$o(t);if(a.y>r)return"top";{const e=Ze(t),o=Math.max(e.dom.scrollHeight,Ht(e));return a.bottom<o-r||Xo().bottom<a.bottom-r?"bottom":"top"}case wA.bottom:return"bottom";case wA.top:default:return"top"}})(n);var l;i!==a&&(l=i,r.on((e=>{hT.setModes(e,[l]),u.setDockingMode(l);const t=m()?ic.TopToBottom:ic.BottomToTop;vt(e.element,lc,t)})),o&&x(!0))}))};return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),_t(s.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{It(e.element,"display")})),w(!1),x()},hide:()=>{h.set(!1),_t(s.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{_t(e.element,"display","none")}))},update:x,updateMode:w,repositionPopups:v}})(e,i,t,n,a),c=hf(e);wM(e);const d=()=>{if(a.isSet())return void l.show();a.set(uM.getHeader(s.outerContainer).getOrDie());const r=Wf(e);kd(r,s.mothership),((e,t)=>{kd(e,t.dialogUi.mothership)})(r,t),gA(e,t,o,n),uM.setMenubar(s.outerContainer,pM(e,o)),l.show(),((e,t,o,n)=>{const r=xr(kA(t,o.isPositionedAtTop())),s=n=>{const{pos:s,bounds:a}=kA(t,o.isPositionedAtTop()),{pos:i,bounds:l}=r.get(),c=a.height!==l.height||a.width!==l.width;r.set({pos:s,bounds:a}),c&&qy(e,n),o.isVisible()&&(i!==s?o.update(!0):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(!0))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>s(e)))})),e.on("ScrollWindow",(()=>o.updateMode()));const a=Pl();a.set(Gl(Ie(e.getBody()),"load",(e=>s(e.raw)))),e.on("remove",(()=>{a.clear()}))})(e,i,l,c),e.nodeChanged()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),ty(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{ey(t,!e)},isEnabled:()=>!km.isDisabled(s.outerContainer)};return{editorContainer:s.outerContainer.element.dom,api:u}}});const OA="contexttoolbar-hide",_A=(e,t)=>Fs(fC,((o,n)=>{const r=(e=>({hide:()=>Cs(e,is()),getValue:()=>ou.getValue(e)}))(e.get(o));t.onAction(r,n.event.buttonApi)})),TA=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Ah(yb.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:gl([Tp.config({mode:"special",onEnter:e=>r.findPrimary(e).map((e=>(_s(e),!0))),onLeft:(e,t)=>(t.cut(),M.none()),onRight:(e,t)=>(t.cut(),M.none())})])})),r=((e,t,o)=>{const n=z(t,(t=>Ah(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...r}=t.original,s=Pn(bv({...r,type:"togglebutton",onAction:b}));return aA(s,o,[_A(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...r}=t.original,s=Pn(pv({...r,type:"button",onAction:b}));return sA(s,o,[_A(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>z(n,(e=>e.asSpec())),findPrimary:e=>se(t,((t,o)=>t.primary?M.from(n[o]).bind((t=>t.getOpt(e))).filter(k(km.isDisabled)):M.none()))}})(n,e.commands,t);return[{title:M.none(),items:[n.asSpec()]},{title:M.none(),items:r.asSpecs()}]},EA=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,MA=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=at(Ie(e.startContainer),e.startOffset).element;return(Ue(o)?et(o):M.some(o)).filter(Pe).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Vo();return Go(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=qo(Ie(e.getBody()));return Go(o.x+t.left,o.y+t.top,t.width,t.height)}},AA=(e,t,o,n=0)=>{const r=Lo(window),s=$o(Ie(e.getContentAreaContainer())),a=If(e)||Vf(e)||zf(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(s,r,n);if(e.inline&&!a)return Go(i,r.y,l,r.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,r,s)=>{const a=Ie(e.getContainer()),i=ri(a,".tox-editor-header").getOr(a),l=$o(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+s,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-s,o.bottom)};const u="line"===r?$o(a):t;return d?{y:Math.max(l.bottom+s,o.y),bottom:Math.min(u.bottom-s,o.bottom)}:{y:Math.max(u.y+s,o.y),bottom:Math.min(l.y-s,o.bottom)}})(e,s,r,a,o,n);return Go(i,c,l,d-c)}},DA={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},BA={maxHeightFunction:Zl(),maxWidthFunction:wE()},FA=e=>"node"===e,IA=(e,t,o,n,r)=>{const s=MA(e),a=n.lastElement().exists((e=>Xe(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=at(Ie(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Xe(n.element,t)})(e,o)?a?$O:PO:a?((e,o,r)=>{const a=Dt(e,"position");_t(e,"position",o);const i=EA(s,$o(t),-20)&&!n.isReposition()?XO:$O;return a.each((t=>_t(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?r.y+Vo().top:r.y)+(Ht(t)+12)<=s.y?PO:UO},RA=(e,t,o,n)=>{const r=t=>(n,r,s,a,i)=>({...IA(e,a,t,o,i)({...n,y:i.y,height:i.height},r,s,a,i),alwaysFit:!0}),s=e=>FA(n)?[r(e)]:[];return t?{onLtr:e=>[Qi,Xi,Ki,Yi,Ji,Zi].concat(s(e)),onRtl:e=>[Qi,Ki,Xi,Ji,Yi,Zi].concat(s(e))}:{onLtr:e=>[Zi,Qi,Yi,Xi,Ji,Ki].concat(s(e)),onRtl:e=>[Zi,Qi,Ji,Ki,Yi,Xi].concat(s(e))}},NA=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:r}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:r}},VA=(e,t)=>{const o={},n=[],r=[],s={},a={},i=ae(e);return L(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Pn(Ln("ContextForm",Cv,i));o[e]=l,l.launch.map((o=>{s["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?r.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Ln("ContextToolbar",Ov,o)).each((o=>{"editor"===t.scope?r.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:r,lookupTable:a,formNavigators:s}},HA=Qs("forward-slide"),zA=Qs("backward-slide"),LA=Qs("change-slide-event"),PA="tox-pop--resizing",UA="tox-pop--transition",WA=(e,t,o,n)=>{const r=n.backstage,s=r.shared,a=_o().deviceType.isTouch,i=Ul(),l=Ul(),c=Ul(),d=Ka((e=>{const t=xr([]);return Th.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Th.getContent(e).each((e=>{It(e.element,"visibility")})),Ba(e.element,PA),It(e.element,"width")},inlineBehaviours:gl([Vp("context-toolbar-events",[Ls(Xr(),((e,t)=>{"width"===t.event.raw.propertyName&&(Ba(e.element,PA),It(e.element,"width"))})),Fs(LA,((e,t)=>{const o=e.element;It(o,"width");const n=$t(o);Th.setContent(e,t.event.contents),Da(o,PA);const r=$t(o);_t(o,"width",n+"px"),Th.getContent(e).each((e=>{t.event.focus.bind((e=>(wl(e),Cl(o)))).orThunk((()=>(Tp.focusIn(e),kl(dt(o)))))})),setTimeout((()=>{_t(e.element,"width",r+"px")}),0)})),Fs(HA,((e,o)=>{Th.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:kl(dt(e.element))}]))})),Os(e,LA,{contents:o.event.forwardContents,focus:M.none()})})),Fs(zA,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Os(e,LA,{contents:Ya(o.bar),focus:o.focus})}))}))]),Tp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Cs(o,zA),M.some(!0))))})]),lazySink:()=>Jo.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),M.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=FA(t)?1:0;return AA(e,s,t,o)},m=()=>!(e.removed||a()&&r.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>pt(e)&&(e=>Pe(e)&&He(e.dom))(e))).map(qo).getOrThunk((()=>MA(e))))(e,i.get()):MA(e);return t.height<=0||!EA(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Th.hide(d)},h=()=>{if(Th.isOpen(d)){const e=d.element;It(e,"display"),g()?_t(e,"display","none"):(l.set(0),Th.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:gl([Tp.config({mode:"acyclic"}),Vp("pop-dialog-wrap-events",[Ps((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Tp.focusIn(t)))})),Us((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Xt((()=>VA(t,(e=>{const t=y([e]);Os(d,HA,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),r={...o,...v().formNavigators},a=df(e)===Wh.scrolling?Wh.scrolling:Wh.default,i=q(z(t,(t=>"contexttoolbar"===t.type?((t,o)=>mA(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,M.some(["form:"])))(r,t):((e,t)=>TA(e,t))(t,s.providers))));return GE({type:a,uid:Qs("context-toolbar"),initGroups:i,onEscape:M.none,cyclicKeying:!0,providers:s.providers})},x=(t,n)=>{if(w.cancel(),!m())return;const r=y(t),p=t[0].position,h=((t,n)=>{const r="node"===t?s.anchors.node(n):s.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:oc(12,0,DA),layouts:{onLtr:()=>[el],onRtl:()=>[tl]},overrides:BA}:{bubble:oc(0,12,DA,1/12),layouts:RA(e,o,n,t),overrides:BA})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>ud.getMode(o)});return cn(r,c)})(p,n);c.set(p),l.set(1);const b=d.element;It(b,"display"),(e=>xe(Se(e,i.get(),Xe),!0))(n)||(Ba(b,UA),ud.reset(o,d)),Th.showWithinBounds(d,f(r),{anchor:h,transition:{classes:[UA],mode:"placement"}},(()=>M.some(u()))),n.fold(i.clear,i.set),g()&&_t(b,"display","none")},w=WC((()=>{e.hasFocus()&&!e.removed&&(Fa(d.element,UA)?w.throttle():((e,t)=>{const o=Ie(t.getBody()),n=e=>Xe(e,o),r=Ie(t.selection.getNode());return(e=>!n(e)&&!Ke(o,e))(r)?M.none():((e,t,o)=>{const n=NA(e,t);if(n.contextForms.length>0)return M.some({elem:e,toolbars:[n.contextForms[0]]});{const t=NA(e,o);if(t.contextForms.length>0)return M.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),r=t("node");if(n||r){if(r&&n){const e=o("node"),t=z(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return M.some({elem:e,toolbars:o})}return M.none()}})(r,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?M.none():Or(t,(e=>{if(Pe(e)){const{contextToolbars:t,contextForms:n}=NA(e,o.inNodeScope),r=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return r.length>0?M.some({elem:e,toolbars:r}):M.none()}return M.none()}),e))(n,r,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,M.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",w.throttle),e.on(OA,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],ke(t.target!==e,t.target)),Th.getContent(d).each(Tp.focusIn)}))})),e.on("focusout",(t=>{Eh.setEditorTimeout(e,(()=>{Cl(o.element).isNone()&&Cl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&w.throttle()})),e.on("NodeChange",(e=>{Cl(d.element).fold(w.throttle,b)}))}))},jA={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},GA=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),$A=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),r=Ul();return z(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:s=>{const a=e=>{e&&(r.on((e=>e.setActive(!1))),r.set(s)),s.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{r.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},qA={name:"lineheight",text:"Line height",icon:"line-height",getOptions:Nf,hash:e=>((e,t)=>((e,t)=>M.from(GA.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(jA[t],(t=>e===t)))))(n,t)?M.some({value:o,unit:n}):M.none()})))(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:x,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>M.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t)},XA=e=>kM(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent"))})),KA=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),()=>e.off("PastePlainTextToggle",n)},YA=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},JA=e=>{(e=>{(e=>{lC.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:SM(e,t.name),onAction:YA(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:SM(e,o),onAction:YA(e,o)})}})(e),(e=>{lC.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"print",text:"Print",action:"mcePrint",icon:"print"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:OM(e,t.action)})}))})(e),(e=>{lC.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:OM(e,t.action),onSetup:SM(e,t.name)})}))})(e)})(e),(e=>{lC.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:OM(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:YA(e,"code")})})(e)},ZA=(e,t)=>kM(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),QA=e=>kM(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),eD=(e,t)=>{(e=>{L([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:OM(e,t.cmd),onSetup:SM(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:OM(e,"JustifyNone")})})(e),JA(e),((e,t)=>{((e,t)=>{const o=TM(0,t,BM(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=TM(0,t,VM(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=TM(0,t,WM(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=TM(0,t,IM(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=TM(0,t,UM(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:ZA(e,"hasUndo"),onAction:OM(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:ZA(e,"hasRedo"),onAction:OM(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:ZA(e,"hasUndo"),onAction:OM(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:ZA(e,"hasRedo"),onAction:OM(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=mx(e),o=gx(e),n=xr(t),r=xr(o);xx(e,"forecolor","forecolor","Text color",n),xx(e,"backcolor","hilitecolor","Background color",r),wx(e,"forecolor","forecolor","Text color"),wx(e,"backcolor","hilitecolor","Background color")})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:OM(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:QA(e),onAction:OM(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:XA(e),onAction:OM(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:OM(e,"indent")})})(e)})(e),(e=>{$A(e,qA),(e=>M.from(lf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=Ie(e.selection.getNode());return _r(t,(e=>M.some(e).filter(Pe).bind((e=>wt(e,"lang").map((t=>({code:t,customCode:wt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Pl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),o.clear}}))))(e).each((t=>$A(e,t)))})(e),(e=>{const t=xr(Af(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:KA(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:KA(e,t)})})(e)},tD=e=>s(e)?e.split(/[ ,]/):e,oD=e=>t=>t.options.get(e),nD=oD("contextmenu_never_use_native"),rD=oD("contextmenu_avoid_overlap"),sD=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},aD=(e,t)=>({type:"makeshift",x:e,y:t}),iD=e=>"longpress"===e.type||0===e.type.indexOf("touch"),lD=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(iD(e)){const t=e.touches[0];return aD(t.pageX,t.pageY)}return aD(e.pageX,e.pageY)})(t):((e,t)=>{const o=Gh.DOM.getPos(e);return((e,t,o)=>aD(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(iD(e)){const t=e.touches[0];return aD(t.clientX,t.clientY)}return aD(e.clientX,e.clientY)})(t)):cD(e),cD=e=>({type:"selection",root:Ie(e.selection.getNode())}),dD=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:M.some(Ie(e.selection.getNode())),root:Ie(e.getBody())}))(e);case"point":return lD(e,t);case"selection":return cD(e)}},uD=(e,t,o,n,r,s)=>{const a=o(),i=dD(e,t,s);EC(a,Jf.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:M.none()}).map((e=>{t.preventDefault(),Th.showMenuAt(r,{anchor:i},{menu:{markers:ub("normal")},data:e})}))},mD={onLtr:()=>[Qi,Xi,Ki,Yi,Ji,Zi,PO,UO,LO,HO,zO,VO],onRtl:()=>[Qi,Ki,Xi,Ji,Yi,Zi,PO,UO,zO,VO,LO,HO]},gD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},pD=(e,t,o,n,r,s)=>{const a=_o(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,r,s,a)=>{const i=((e,t,o)=>{const n=dD(e,t,o);return{bubble:oc(0,"point"===o?12:0,gD),layouts:mD,overrides:{maxWidthFunction:wE(),maxHeightFunction:Zl()},...n}})(e,t,s);EC(o,Jf.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:M.none()}).map((o=>{t.preventDefault();const l=a?Ch.HighlightMenuAndItem:Ch.HighlightNone;Th.showMenuWithinBounds(r,{anchor:i},{menu:{markers:ub("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>M.some(AA(e,n.shared,"node"===s?"node":"selection")))),e.dispatch(OA)}))})(e,t,a,n,r,s,!(c||i||l&&d))};if((l||i)&&"node"!==s){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Eh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),s()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const r=()=>s();e.once("longpresscancel",r);const s=()=>{e.off("touchend",o),e.off("longpresscancel",r),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],r=o.getRng();return zc(e.getWin(),Mc.domRange(r)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},hD=e=>s(e)?"|"===e:"separator"===e.type,fD={type:"separator"},bD=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(s(e))return e;switch(e.type){case"separator":return fD;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return s(t)?t:z(t,bD)}};default:const n=e;return{type:"menuitem",...t(n),onAction:(o=n.onAction,()=>o())}}var o},vD=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!hD(e))).fold((()=>[]),(e=>[fD]));return e.concat(o).concat(t).concat([fD])},yD=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),xD=(e,t)=>yD(e,t)?e.selection.getStart(!0):t.target,wD=(e,t,o)=>{const n=_o().deviceType.isTouch,r=Ka(Th.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:gl([Vp("dismissContextMenu",[Fs(fs(),((t,o)=>{Nd.close(t),e.focus()}))])])})),a=()=>Th.hide(r),i=t=>{if(nD(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!nD(e))(e,t)||(e=>0===sD(e).length)(e))return;const a=((e,t)=>{const o=rD(e),n=yD(e,t)?"selection":"point";if(Ee(o)){const r=xD(e,t);return mw(Ie(r),o)?"node":n}return n})(e,t);(n()?pD:uD)(e,t,(()=>{const o=xD(e,t),n=e.ui.registry.getAll(),r=sD(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(s(n))return vD(t,n.split(" "));if(n.length>0){const e=z(n,bD);return vD(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&hD(n[n.length-1])&&n.pop(),n})(n.contextMenus,r,o)}),o,r,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},SD=wr([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),kD=e=>t=>t.translate(-e.left,-e.top),CD=e=>t=>t.translate(e.left,e.top),OD=e=>(t,o)=>j(e,((e,t)=>t(e)),Pt(t,o)),_D=(e,t,o)=>e.fold(OD([CD(o),kD(t)]),OD([kD(t)]),OD([])),TD=(e,t,o)=>e.fold(OD([CD(o)]),OD([]),OD([CD(t)])),ED=(e,t,o)=>e.fold(OD([]),OD([kD(o)]),OD([CD(t),kD(o)])),MD=(e,t,o)=>{const n=e.fold(((e,t)=>({position:M.some("absolute"),left:M.some(e+"px"),top:M.some(t+"px")})),((e,t)=>({position:M.some("absolute"),left:M.some(e-o.left+"px"),top:M.some(t-o.top+"px")})),((e,t)=>({position:M.some("fixed"),left:M.some(e+"px"),top:M.some(t+"px")})));return{right:M.none(),bottom:M.none(),...n}},AD=(e,t,o,n)=>{const r=(e,r)=>(s,a)=>{const i=e(t,o,n);return r(s.getOr(i.left),a.getOr(i.top))};return e.fold(r(ED,DD),r(TD,BD),r(_D,FD))},DD=SD.offset,BD=SD.absolute,FD=SD.fixed,ID=(e,t)=>{const o=xt(e,t);return u(o)?NaN:parseInt(o,10)},RD=(e,t,o,n,r,s)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=ID(o,t.leftAttr),r=ID(o,t.topAttr);return isNaN(n)||isNaN(r)?M.none():M.some(Pt(n,r))})(e,t).fold((()=>o),(e=>FD(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?VD(e,t,a,r,s):HD(e,t,a,r,s),l=_D(a,r,s);return((e,t,o)=>{const n=e.element;vt(n,t.leftAttr,o.left+"px"),vt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:FD(l.left,l.top),extra:M.none()})),(e=>({coord:e.output,extra:e.extra})))},ND=(e,t,o,n)=>se(e,(e=>{const r=e.sensor,s=((e,t,o,n,r,s)=>{const a=TD(e,r,s),i=TD(t,r,s);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,r,e.range.left,e.range.top,o,n);return s?M.some({output:AD(e.output,t,o,n),extra:e.extra}):M.none()})),VD=(e,t,o,n,r)=>{const s=t.getSnapPoints(e);return ND(s,o,n,r).orThunk((()=>{const e=j(s,((e,t)=>{const s=t.sensor,a=((e,t,o,n,r,s)=>{const a=TD(e,r,s),i=TD(t,r,s),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return Pt(l,c)})(o,s,t.range.left,t.range.top,n,r);return e.deltas.fold((()=>({deltas:M.some(a),snap:M.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:M.some(a),snap:M.some(t)}:e))}),{deltas:M.none(),snap:M.none()});return e.snap.map((e=>({output:AD(e.output,o,n,r),extra:e.extra})))}))},HD=(e,t,o,n,r)=>{const s=t.getSnapPoints(e);return ND(s,o,n,r)};var zD=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const r=t.getTarget(e.element);if(t.repositionTarget){const t=Ye(e.element),o=Vo(t),s=Q_(r),a=((e,t,o)=>({coord:AD(e.output,e.output,t,o),extra:e.extra}))(n,o,s),i=MD(a.coord,0,s);Et(r,i)}}});const LD="data-initial-z-index",PD=(e,t)=>{e.getSystem().addToGui(t),(e=>{et(e.element).filter(Pe).each((t=>{Dt(t,"z-index").each((e=>{vt(t,LD,e)})),_t(t,"z-index",Mt(e.element,"z-index"))}))})(t)},UD=e=>{(e=>{et(e.element).filter(Pe).each((e=>{wt(e,LD).fold((()=>It(e,"z-index")),(t=>_t(e,"z-index",t))),kt(e,LD)}))})(e),e.getSystem().removeFromGui(e)},WD=(e,t,o)=>e.getSystem().build(Sw.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var jD=dr("snaps",[Xn("getSnapPoints"),wi("onSensor"),Xn("leftAttr"),Xn("topAttr"),ur("lazyViewport",Xo),ur("mustSnap",!1)]);const GD=[ur("useFixed",_),Xn("blockerClass"),ur("getTarget",x),ur("onDrag",b),ur("repositionTarget",!0),ur("onDrop",b),br("getBounds",Xo),jD],$D=(e,t)=>({bounds:e.getBounds(),height:zt(t.element),width:qt(t.element)}),qD=(e,t,o,n,r)=>{const s=o.update(n,r),a=o.getStartData().getOrThunk((()=>$D(t,e)));s.each((o=>{((e,t,o,n)=>{const r=t.getTarget(e.element);if(t.repositionTarget){const s=Ye(e.element),a=Vo(s),i=Q_(r),l=(e=>{return(t=Dt(e,"left"),o=Dt(e,"top"),n=Dt(e,"position"),r=(e,t,o)=>("fixed"===o?FD:DD)(parseInt(e,10),parseInt(t,10)),t.isSome()&&o.isSome()&&n.isSome()?M.some(r(t.getOrDie(),o.getOrDie(),n.getOrDie())):M.none()).getOrThunk((()=>{const t=Wt(e);return BD(t.left,t.top)}));var t,o,n,r})(r),c=((e,t,o,n,r,s,a)=>((e,t,o,n,r)=>{const s=r.bounds,a=TD(t,o,n),i=zi(a.left,s.x,s.x+s.width-r.width),l=zi(a.top,s.y,s.y+s.height-r.height),c=BD(i,l);return t.fold((()=>{const e=ED(c,o,n);return DD(e.left,e.top)}),y(c),(()=>{const e=_D(c,o,n);return FD(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=s.left,i=s.top,t.fold(((e,t)=>DD(e+a,t+i)),((e,t)=>BD(e+a,t+i)),((e,t)=>FD(e+a,t+i))));var t,a,i;const l=_D(e,n,r);return FD(l.left,l.top)}),(t=>{const a=RD(e,t,o,s,n,r);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,r,a))(e,t.snaps,l,a,i,n,o),d=MD(c,0,i);Et(r,d)}t.onDrag(e,r,n)})(e,t,a,o)}))},XD=(e,t,o,n)=>{t.each(UD),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;kt(o,t.leftAttr),kt(o,t.topAttr)})(e,t)})(e,t)}));const r=o.getTarget(e.element);n.reset(),o.onDrop(e,r)},KD=e=>(t,o)=>{const n=e=>{o.setStartData($D(t,e))};return As([Fs(ms(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var YD=Object.freeze({__proto__:null,getData:e=>M.from(Pt(e.x,e.y)),getDelta:(e,t)=>Pt(t.left-e.left,t.top-e.top)});const JD=(e,t,o)=>[Fs(Rr(),((n,r)=>{if(0!==r.event.raw.button)return;r.stop();const s=()=>XD(n,M.some(l),e,t),a=gw(s,200),i={drop:s,delayDrop:a.schedule,forceDrop:s,move:o=>{a.cancel(),qD(n,e,t,YD,o)}},l=WD(n,e.blockerClass,(e=>As([Fs(Rr(),e.forceDrop),Fs(Hr(),e.drop),Fs(Nr(),((t,o)=>{e.move(o.event)})),Fs(Vr(),e.delayDrop)]))(i));o(n),PD(n,l)}))],ZD=[...GD,Oi("dragger",{handlers:KD(JD)})];var QD=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return M.some(Pt(t.clientX,t.clientY))})(t):M.none()},getDelta:(e,t)=>Pt(t.left-e.left,t.top-e.top)});const eB=(e,t,o)=>{const n=Ul(),r=o=>{XD(o,n.get(),e,t),n.clear()};return[Fs(Dr(),((s,a)=>{a.stop();const i=()=>r(s),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{qD(s,e,t,QD,o)}},c=WD(s,e.blockerClass,(e=>As([Fs(Dr(),e.forceDrop),Fs(Fr(),e.drop),Fs(Ir(),e.drop),Fs(Br(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(s),PD(s,c)})),Fs(Br(),((o,n)=>{n.stop(),qD(o,e,t,QD,n.event)})),Fs(Fr(),((e,t)=>{t.stop(),r(e)})),Fs(Ir(),r)]},tB=ZD,oB=[...GD,Oi("dragger",{handlers:KD(eB)})],nB=[...GD,Oi("dragger",{handlers:KD(((e,t,o)=>[...JD(e,t,o),...eB(e,t,o)]))})];var rB=Object.freeze({__proto__:null,mouse:tB,touch:oB,mouseOrTouch:nB}),sB=Object.freeze({__proto__:null,init:()=>{let e=M.none(),t=M.none();const o=y({});return ba({readState:o,reset:()=>{e=M.none(),t=M.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=M.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=M.some(e)}})}});const aB=bl({branchKey:"mode",branches:rB,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:M.from(e.extra)})},state:sB,apis:zD}),iB=(e,t,o,n,r,s)=>e.fold((()=>aB.snap({sensor:BD(o-20,n-20),range:Pt(r,s),output:BD(M.some(o),M.some(n)),extra:{td:t}})),(e=>{const r=o-20,s=n-20,a=e.element.dom.getBoundingClientRect();return aB.snap({sensor:BD(r,s),range:Pt(40,40),output:BD(M.some(o-a.width/2),M.some(n-a.height/2)),extra:{td:t}})})),lB=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const r=n.td;((e,t)=>e.exists((e=>Xe(e,t))))(t.get(),r)||(t.set(r),o(r))},mustSnap:!0}),cB=e=>Ah(Mh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:gl([aB.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),eS.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),dB=(e,t)=>{const o=xr([]),n=xr([]),r=xr(!1),s=Ul(),a=Ul(),i=e=>{const o=qo(e);return iB(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=qo(e);return iB(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=lB((()=>z(o.get(),(e=>i(e)))),s,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=lB((()=>z(n.get(),(e=>l(e)))),a,(t=>{s.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=cB(c),m=cB(d),g=Ka(u.asSpec()),p=Ka(m.asSpec()),h=(t,o,n,r)=>{const s=n(o);aB.snapTo(t,s),((t,o,n,s)=>{const a=o.dom.getBoundingClientRect();It(t.element,"display");const i=Qe(Ie(e.getBody())).dom.innerHeight,l=a[r]<0,c=((e,t)=>e[r]>t)(a,i);(l||c)&&_t(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");_o().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{r.get()||(vd(t,g),vd(t,p),r.set(!0)),s.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{s.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{r.get()&&(wd(g),wd(p),r.set(!1)),s.clear(),a.clear()})))},uB=(e,t,o)=>{var n;const r=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:gl([Tp.config({mode:"flow",selector:"div[role=button]"}),km.config({disabled:o.isDisabled}),oy(),Mw.config({}),Np.config({}),Vp("elementPathEvents",[Ps(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Tp.focusIn(t))),e.on("NodeChange",(n=>{const s=(t=>{const o=[];let n=t.length;for(;n-- >0;){const s=t[n];if(1===s.nodeType&&"BR"!==(r=s).nodeName&&!r.getAttribute("data-mce-bogus")&&"bookmark"!==r.getAttribute("data-mce-type")){const t=Xy(e,s);if(t.isDefaultPrevented()||o.push({name:t.name,element:s}),t.isPropagationStopped())break}}var r;return o})(n.parents),a=s.length>0?j(s,((t,n,s)=>{const a=((t,n,r)=>Mh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":r,"aria-level":r+1}},components:[Ga(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:gl([ny(o.isDisabled),oy()])}))(n.name,n.element,s);return 0===s?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[Ga(` ${r} `)]},a])}),[]):[];Np.set(t,a)}))}))])]),components:[]}};var mB;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(mB||(mB={}));const gB=(e,t,o)=>{const n=Ie(e.getContainer()),r=((e,t,o,n,r)=>{const s={height:yA(n+t.top,tf(e),nf(e))};return o===mB.Both&&(s.width=yA(r+t.left,ef(e),of(e))),s})(e,t,o,Ht(n),$t(n));le(r,((e,t)=>{h(e)&&_t(n,t,vA(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},pB=(e,t,o,n)=>{const r=Pt(20*o,20*n);return gB(e,r,t),M.some(!0)},hB=(e,t)=>({dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const o=(()=>{const o=[];return Tf(e)&&o.push(uB(e,{},t)),e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Np.set(e,[Ga(t.translate(["{0} "+n,o[n]]))]);return Mh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:gl([ny(t.isDisabled),oy(),Mw.config({}),Np.config({}),ou.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Vp("wordcount-events",[js((e=>{const t=ou.getValue(e),n="words"===t.mode?"characters":"words";ou.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Ps((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=ou.getValue(t);ou.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[ns()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Ef(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Dh.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:gl([Up.config({})])}]}),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]})(),n=((e,t)=>{const o=(e=>{const t=Mf(e);return!1===t?mB.None:"both"===t?mB.Both:mB.Vertical})(e);return o===mB.None?M.none():M.some(Lh("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},behaviours:[aB.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,r)=>gB(e,r,o),blockerClass:"tox-blocker"}),Tp.config({mode:"special",onLeft:()=>pB(e,o,-1,0),onRight:()=>pB(e,o,1,0),onUp:()=>pB(e,o,0,-1),onDown:()=>pB(e,o,0,1)}),Mw.config({}),Up.config({})]},t.icons))})(e,t);return o.concat(n.toArray())})()}),fB=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),bB=e=>{const t=e.inline,o=t?CA:fA,n=Gf(e)?OT:J_,r=(()=>{const e=Ul(),t=Ul(),o=Ul();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>[...e.get().map((e=>e.mothership)).toArray()],setupDialogUi:t=>{e.set(t)},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),s=Ul(),a=Ul(),i=_o().deviceType.isTouch()?["tox-platform-touch"]:[],l=Lf(e),c=df(e),d=Ah({dom:{tag:"div",classes:["tox-anchorbar"]}}),u=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(uM.getHeader),m=r.lazyGetInOuterOrDie("anchor bar",d.getOpt),g=r.lazyGetInOuterOrDie("toolbar",uM.getToolbar),p=r.lazyGetInOuterOrDie("throbber",uM.getThrobber),h=((e,t,o)=>{const n=xr(!1),r=(e=>{const t=xr(Lf(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),s={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Dh.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},a=V_(t),i=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?M.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):M.none()},n=xr([]),r=xr([]),s=xr(!1);return e.on("PreInit",(r=>{const s=d_(e),a=m_(e,s,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=m_(e,n.items,t,o);r.set(a),s.set(n.replace)})),{getData:()=>{const e=s.get()?[]:n.get(),t=r.get();return e.concat(t)}}})(t),l=(e=>({colorPicker:t_(e),hasCustomColors:o_(e),getColors:n_(e),getColorCols:r_(e)}))(t),c=(e=>({isDraggableModal:s_(e)}))(t),d={shared:{providers:s,anchors:e_(t,o,r.isPositionedAtTop),header:r},urlinput:a,styles:i,colorinput:l,dialog:c,isContextMenuOpen:()=>n.get(),setContextMenuState:e=>n.set(e)},u={...d,shared:{...d.shared,interpreter:e=>MO(e,{},u),getSink:e.popup}},m={...d,shared:{...d.shared,interpreter:e=>MO(e,{},m),getSink:e.dialog}};return{popup:u,dialog:m}})({popup:()=>Jo.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>Jo.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,m),f=x,b=()=>{const o=(()=>{const t={attributes:{[lc]:l?ic.BottomToTop:ic.TopToBottom}},o=uM.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:h.popup,onEscape:()=>{e.focus()}}),n=uM.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:h.popup.shared.getSink,providers:h.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:c,lazyToolbar:g,lazyHeader:()=>u().getOrDie("Could not find header element"),...t}),r=uM.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:h.popup.shared.providers,onEscape:()=>{e.focus()},type:c}),s=zf(e),a=Vf(e),i=If(e),m=Bf(e),p=uM.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),f=s||a||i,b=m?[p,o]:[o];return uM.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(f?[]:["tox-editor-header--empty"]),...t},components:q([i?b:[],s?[r]:a?[n]:[],Uf(e)?[]:[d.asSpec()]]),sticky:Gf(e),editor:e,sharedBackstage:h.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[uM.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),uM.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},r=uM.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:h.popup}),a=uM.parts.viewWrapper({backstage:h.popup}),m=_f(e)&&!t?M.some(hB(e,h.popup.shared.providers)):M.none(),p=q([l?[]:[o],t?[]:[n],l?[o]:[]]),f=uM.parts.editorContainer({components:q([p,t?[]:m.toArray()])}),b=jf(e),v={role:"application",...Dh.isRtl()?{dir:"rtl"}:{},...b?{"aria-hidden":"true"}:{}},y=Ka(uM.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(l?["tox-tinymce--toolbar-bottom"]:[]).concat(i),styles:{visibility:"hidden",...b?{opacity:"0",border:"0"}:{}},attributes:v},components:[f,...t?[]:[a],r],behaviours:gl([oy(),km.config({disableClass:"tox-tinymce--disabled"}),Tp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),x=kw(y);return s.set(x),{mothership:x,outerContainer:y}},v=t=>{const o=vA((e=>{const t=(e=>{const t=Zh(e),o=tf(e),n=nf(e);return bA(t).map((e=>yA(e,o,n)))})(e);return t.getOr(Zh(e))})(e)),n=vA((e=>xA(e).getOr(Qh(e)))(e));return e.inline||(Ft("div","width",n)&&_t(t.element,"width",n),Ft("div","height",o)?_t(t.element,"height",o):_t(t.element,"height","400px")),o};return{popups:{backstage:h.popup,getMothership:()=>fB("popups",a)},dialogs:{backstage:h.dialog,getMothership:()=>fB("dialogs",a)},renderUI:()=>{const t=b(),s=(()=>{const t=Wf(e),o=Xe(ht(),t)&&"grid"===Mt(t,"display"),r={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(i),attributes:{...Dh.isRtl()?{dir:"rtl"}:{}}},behaviours:gl([ud.config({useFixed:()=>n.isDocked(u)})])},s={dom:{styles:{width:document.body.clientWidth+"px"}},events:As([Fs(gs(),(e=>{_t(e.element,"width",document.body.clientWidth+"px")}))])},l=Ka(cn(r,o?s:{})),c=kw(l);return a.set(c),{sink:l,mothership:c}})(),l=f(s);r.dialogUi.set(s),r.popupUi.set(l),r.mainUi.set(t);return(t=>{const{mainUi:r,popupUi:s,uiMotherships:a}=t;ce(uf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:d,sidebars:m,views:g}=e.ui.registry.getAll(),f=Hf(e),b={menuItems:l,menus:$f(e),menubar:bf(e),toolbar:f.getOrThunk((()=>vf(e))),allowToolbarGroups:c===Wh.floating,buttons:i,sidebar:m,views:g};var y;y=r.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{uM.focusMenubar(y)})),e.addShortcut("alt+F10","focus toolbar",(()=>{uM.focusToolbar(y)})),e.addCommand("ToggleToolbarDrawer",(()=>{uM.toggleToolbarDrawer(y)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>uM.isToolbarDrawerToggled(y))),((e,t,o)=>{const n=(e,n)=>{L([t,...o],(t=>{t.broadcastEvent(e,n)}))},r=(e,n)=>{L([t,...o],(t=>{t.broadcastOn([e],n)}))},s=e=>r(Vd(),{target:e.target}),a=Po(),i=jl(a,"touchstart",s),l=jl(a,"touchmove",(e=>n(ds(),e))),c=jl(a,"touchend",(e=>n(us(),e))),d=jl(a,"mousedown",s),u=jl(a,"mouseup",(e=>{0===e.raw.button&&r(zd(),{target:e.target})})),m=e=>r(Vd(),{target:Ie(e.target)}),g=e=>{0===e.button&&r(zd(),{target:Ie(e.target)})},p=()=>{L(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(ms(),$l(e)),f=e=>{r(Hd(),{}),n(gs(),$l(e))},b=()=>r(Hd(),{}),v=t=>{t.state&&r(Vd(),{target:Ie(e.getContainer())})},y=e=>{r(Vd(),{target:Ie(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",b),e.on("AfterProgressState",v),e.on("DismissPopups",y)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",b),e.off("AfterProgressState",v),e.off("DismissPopups",y),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind()})),e.on("detach",(()=>{L([t,...o],Od),L([t,...o],(e=>e.destroy()))}))})(e,r.mothership,a),n.setup(e,h.popup.shared,u),eD(e,h.popup),wD(e,h.popup.shared.getSink,h.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();L(ae(t),(o=>{const n=t[o],r=()=>xe(M.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(r())},onSetup:t=>{t.setActive(r());const o=()=>t.setActive(r());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),mE(e,p,h.popup.shared),WA(e,d,s.sink,{backstage:h.popup}),dB(e,s.sink);const x={targetNode:e.getElement(),height:v(r.outerContainer)};return o.render(e,t,b,h.popup,x)})({popupUi:l,dialogUi:s,mainUi:t,uiMotherships:r.getUiMotherships()})}}},vB=y([Xn("lazySink"),nr("dragBlockClass"),br("getBounds",Xo),ur("useTabstopAt",T),ur("eventOrder",{}),nu("modalBehaviours",[Tp]),Si("onExecute"),Ci("onEscape")]),yB={sketch:x},xB=y([Bu({name:"draghandle",overrides:(e,t)=>({behaviours:gl([aB.config({mode:"mouse",getTarget:e=>oi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Au({schema:[Xn("dom")],name:"title"}),Au({factory:yB,schema:[Xn("dom")],name:"close"}),Au({factory:yB,schema:[Xn("dom")],name:"body"}),Bu({factory:yB,schema:[Xn("dom")],name:"footer"}),Du({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[ur("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ur("components",[])],name:"blocker"})]),wB=sm({name:"ModalDialog",configFields:vB(),partFields:xB(),factory:(e,t,o,n)=>{const r=Ul(),s=Qs("modal-events"),a={...e.eventOrder,[ps()]:[s].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{r.set(t);const o=e.lazySink(t).getOrDie(),s=n.blocker(),a=o.getSystem().build({...s,components:s.components.concat([Ya(t)]),behaviours:gl([Up.config({}),Vp("dialog-blocker-events",[Ls(Lr(),(()=>{Tp.focusIn(t)}))])])});vd(o,a),Tp.focusIn(t)},hide:e=>{r.clear(),et(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{wd(e)}))}))},getBody:t=>Gu(t,e,"body"),getFooter:t=>Gu(t,e,"footer"),setIdle:e=>{cE.unblock(e)},setBusy:(e,t)=>{cE.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:su(e.modalBehaviours,[Np.config({}),Tp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt}),cE.config({getRoot:r.get}),Vp(s,[Ps((t=>{((e,t)=>{const o=wt(e,"id").fold((()=>{const e=Qs("dialog-label");return vt(t,"id",e),e}),x);vt(e,"aria-labelledby",o)})(t.element,Gu(t,e,"title").element),((e,t)=>{const o=M.from(xt(e,"id")).fold((()=>{const e=Qs("dialog-describe");return vt(t,"id",e),e}),x);vt(e,"aria-describedby",o)})(t.element,Gu(t,e,"body").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),SB=Cn([Nb,Vb].concat(Bv)),kB=Fn,CB=[cv("button"),Yb,hr("align","end",["start","end"]),rv,nv,ir("buttonType",["primary","secondary"])],OB=[...CB,zb],_B=[Zn("type",["submit","cancel","custom"]),...OB],TB=[Zn("type",["menu"]),Kb,Jb,Yb,or("items",SB),...CB],EB=jn("type",{submit:_B,cancel:_B,custom:_B,menu:TB}),MB=[Nb,zb,Zn("level",["info","warn","error","success"]),Pb,ur("url","")],AB=Cn(MB),DB=[Nb,zb,nv,cv("button"),Yb,ov,ir("buttonType",["primary","secondary","toolbar"]),rv],BB=Cn(DB),FB=[Nb,Vb],IB=FB.concat([Zb]),RB=FB.concat([Hb,nv]),NB=Cn(RB),VB=Fn,HB=IB.concat([sv("auto")]),zB=Cn(HB),LB=En([Ub,zb,Pb]),PB=IB.concat([pr("storageKey","default")]),UB=Cn(PB),WB=Bn,jB=Cn(IB),GB=Bn,$B=FB.concat([pr("tag","textarea"),Jn("scriptId"),Jn("scriptUrl"),mr("settings",void 0,Nn)]),qB=FB.concat([pr("tag","textarea"),Qn("init")]),XB=Hn((e=>Ln("customeditor.old",kn(qB),e).orThunk((()=>Ln("customeditor.new",kn($B),e))))),KB=Bn,YB=Cn(IB),JB=On(vn),ZB=e=>[Nb,Yn("columns"),e],QB=[Nb,Jn("html"),hr("presets","presentation",["presentation","document"])],eF=Cn(QB),tF=IB.concat([fr("sandboxed",!0),fr("transparent",!0)]),oF=Cn(tF),nF=Bn,rF=Cn(FB.concat([ar("height")])),sF=Cn([Jn("url"),sr("zoom"),sr("cachedWidth"),sr("cachedHeight")]),aF=IB.concat([ar("inputMode"),ar("placeholder"),fr("maximized",!1),nv]),iF=Cn(aF),lF=Bn,cF=e=>[Nb,Hb,e],dF=[zb,Ub],uF=[zb,or("items",((e,t)=>{const o=Xt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,(()=>mF)))],mF=_n([Cn(dF),Cn(uF)]),gF=IB.concat([or("items",mF),nv]),pF=Cn(gF),hF=Bn,fF=IB.concat([tr("items",[zb,Ub]),gr("size",1),nv]),bF=Cn(fF),vF=Bn,yF=IB.concat([fr("constrain",!0),nv]),xF=Cn(yF),wF=Cn([Jn("width"),Jn("height")]),SF=FB.concat([Hb,gr("min",0),gr("max",0)]),kF=Cn(SF),CF=Dn,OF=[Nb,or("header",Bn),or("cells",On(Bn))],_F=Cn(OF),TF=IB.concat([ar("placeholder"),fr("maximized",!1),nv]),EF=Cn(TF),MF=Bn,AF=IB.concat([hr("filetype","file",["image","media","file"]),nv]),DF=Cn(AF),BF=Cn([Ub,av]),FF=e=>Gn("items","items",{tag:"required",process:{}},On(Hn((t=>Ln(`Checking item of ${e}`,IF,t).fold((e=>Jo.error(Wn(e))),(e=>Jo.value(e))))))),IF=wn((()=>{return Vn("type",{alertbanner:AB,bar:Cn((e=FF("bar"),[Nb,e])),button:BB,checkbox:NB,colorinput:UB,colorpicker:jB,dropzone:YB,grid:Cn(ZB(FF("grid"))),iframe:oF,input:iF,listbox:pF,selectbox:bF,sizeinput:xF,slider:kF,textarea:EF,urlinput:DF,customeditor:XB,htmlpanel:eF,imagepreview:rF,collection:zB,label:Cn(cF(FF("label"))),table:_F,panel:NF});var e})),RF=[Nb,ur("classes",[]),or("items",IF)],NF=Cn(RF),VF=[cv("tab"),Lb,or("items",IF)],HF=[Nb,tr("tabs",VF)],zF=Cn(HF),LF=OB,PF=EB,UF=Cn([Jn("title"),Kn("body",Vn("type",{panel:NF,tabpanel:zF})),pr("size","normal"),or("buttons",PF),ur("initialData",{}),br("onAction",b),br("onChange",b),br("onSubmit",b),br("onClose",b),br("onCancel",b),br("onTabChange",b)]),WF=Cn([Zn("type",["cancel","custom"]),...LF]),jF=Cn([Jn("title"),Jn("url"),sr("height"),sr("width"),cr("buttons",WF),br("onAction",b),br("onCancel",b),br("onClose",b),br("onMessage",b)]),GF=e=>a(e)?[e].concat(X(fe(e),GF)):l(e)?X(e,GF):[],$F=e=>s(e.type)&&s(e.name),qF={checkbox:VB,colorinput:WB,colorpicker:GB,dropzone:JB,input:lF,iframe:nF,imagepreview:sF,selectbox:vF,sizeinput:wF,slider:CF,listbox:hF,size:wF,textarea:MF,urlinput:BF,customeditor:KB,collection:LB,togglemenuitem:kB},XF=e=>{const t=(e=>U(GF(e),$F))(e),o=X(t,(e=>(e=>M.from(qF[e.type]))(e).fold((()=>[]),(t=>[Kn(e.name,t)]))));return Cn(o)},KF=e=>{var t;return{internalDialog:Pn(Ln("dialog",UF,e)),dataValidator:XF(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},YF={open:(e,t)=>{const o=KF(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Pn(Ln("dialog",jF,t))),redial:e=>KF(e)},JF=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?Jo.error(t):Jo.value(o)},ZF=(e,t,o)=>{const n=Ah(jk.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:z(e.items,(e=>TO(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:gl([Tp.config({mode:"acyclic",useTabstopAt:k(hC)}),(r=n,cm.config({find:r.getOpt})),oC(n,{postprocess:e=>JF(e).fold((e=>(console.error(e),{})),x)})])};var r},QF=rm({name:"TabButton",configFields:[ur("uid",void 0),Xn("value"),Gn("dom","dom",gn((()=>({attributes:{role:"tab",id:Qs("aria"),"aria-selected":"false"}}))),Mn()),nr("action"),ur("domModification",{}),nu("tabButtonBehaviours",[Up,Tp,ou]),Xn("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Zp(e.action),behaviours:su(e.tabButtonBehaviours,[Up.config({}),Tp.config({mode:"execution",useSpace:!0,useEnter:!0}),ou.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),eI=y([Xn("tabs"),Xn("dom"),ur("clickToDismiss",!1),nu("tabbarBehaviours",[Fm,Tp]),yi(["tabClass","selectedClass"])]),tI=Fu({factory:QF,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Fm.dehighlight(e,t),Os(e,ws(),{tabbar:e,button:t})},o=(e,t)=>{Fm.highlight(e,t),Os(e,xs(),{tabbar:e,button:t})};return{action:n=>{const r=n.getSystem().getByUid(e.uid).getOrDie(),s=Fm.isHighlighted(r,n);(s&&e.clickToDismiss?t:s?b:o)(r,n)},domModification:{classes:[e.markers.tabClass]}}}}),oI=y([tI]),nI=sm({name:"Tabbar",configFields:eI(),partFields:oI(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:su(e.tabbarBehaviours,[Fm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{vt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{vt(t.element,"aria-selected","false")}}),Tp.config({mode:"flow",getInitial:e=>Fm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),rI=rm({name:"Tabview",configFields:[nu("tabviewBehaviours",[Np])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:su(e.tabviewBehaviours,[Np.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),sI=y([ur("selectFirst",!0),wi("onChangeTab"),wi("onDismissTab"),ur("tabs",[]),nu("tabSectionBehaviours",[])]),aI=Au({factory:nI,schema:[Xn("dom"),er("markers",[Xn("tabClass"),Xn("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),iI=Au({factory:rI,name:"tabview"}),lI=y([aI,iI]),cI=sm({name:"TabSection",configFields:sI(),partFields:lI(),factory:(e,t,o,n)=>{const r=(t,o)=>{ju(t,e,"tabbar").each((e=>{o(e).each(_s)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:ru(e.tabSectionBehaviours),events:As(q([e.selectFirst?[Ps(((e,t)=>{r(e,Fm.getFirst)}))]:[],[Fs(xs(),((t,o)=>{(t=>{const o=ou.getValue(t);ju(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const r=o.view();wt(t.element,"id").each((e=>{vt(n.element,"aria-labelledby",e)})),Np.set(n,r),e.onChangeTab(n,t,r)}))}))})(o.event.button)})),Fs(ws(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>ju(t,e,"tabview").map((e=>Np.contents(e))).getOr([]),showTab:(e,t)=>{r(e,(e=>{const o=Fm.getCandidates(e);return G(o,(e=>ou.getValue(e)===t)).filter((t=>!Fm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),dI=(e,t)=>{_t(e,"height",t+"px"),_t(e,"flex-basis",t+"px")},uI=(e,t,o)=>{oi(e,'[role="dialog"]').each((e=>{ri(e,'[role="tablist"]').each((n=>{o.get().map((o=>(_t(t,"height","0"),_t(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=Ze(e).dom,r=oi(e,".tox-dialog-wrap").getOr(e);let s;s="fixed"===Mt(r,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Ht(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+$t(o)?Math.max(Ht(o),a):a,l=parseInt(Mt(e,"margin-top"),10)||0,c=parseInt(Mt(e,"margin-bottom"),10)||0;return s-(Ht(e)+l+c-i)})(e,t,n))))).each((e=>{dI(t,e)}))}))}))},mI=e=>ri(e,'[role="tabpanel"]'),gI="send-data-to-section",pI="send-data-to-view",hI=(e,t,o)=>{const n=xr({}),r=e=>{const t=ou.getValue(e),o=JF(t).getOr({}),r=n.get(),s=cn(r,o);n.set(s)},s=e=>{const t=n.get();ou.setValue(e,t)},a=xr(null),i=z(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[Ga(o.shared.providers.translate(e.title))],view:()=>[jk.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:z(e.items,(e=>TO(n,e,t,o))),formBehaviours:gl([Tp.config({mode:"acyclic",useTabstopAt:k(hC)}),Vp("TabView.form.events",[Ps(s),Us(r)]),yl.config({channels:kr([{key:gI,value:{onReceive:r}},{key:pI,value:{onReceive:s}}])})])})))]}))),l=(e=>{const t=Ul(),o=[Ps((o=>{const n=o.element;mI(n).each((r=>{_t(r,"visibility","hidden"),o.getSystem().getByDom(r).toOptional().each((o=>{const n=((e,t,o)=>z(e,((n,r)=>{Np.set(o,e[r].view());const s=t.dom.getBoundingClientRect();return Np.set(o,[]),s.height})))(e,r,o),s=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);s.fold(t.clear,t.set)})),uI(n,r,t),It(r,"visibility"),((e,t)=>{oe(e).each((e=>cI.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{uI(n,r,t)}))}))})),Fs(gs(),(e=>{const o=e.element;mI(o).each((e=>{uI(o,e,t)}))})),Fs(Ww,((e,o)=>{const n=e.element;mI(n).each((e=>{const o=kl(dt(e));_t(e,"visibility","hidden");const r=Dt(e,"height").map((e=>parseInt(e,10)));It(e,"height"),It(e,"flex-basis");const s=e.dom.getBoundingClientRect().height;r.forall((e=>s>e))?(t.set(s),uI(n,e,t)):r.each((t=>{dI(e,t)})),It(e,"visibility"),o.each(wl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return cI.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=ou.getValue(t);Os(e,Uw,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[cI.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[nI.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:gl([Mw.config({})])}),cI.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:gl([Vp("tabpanel",l.extraEvents),Tp.config({mode:"acyclic"}),cm.config({find:e=>oe(cI.getViewItems(e))}),rC(M.none(),(e=>(e.getSystem().broadcastOn([gI],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([pI],{})}))])})},fI=Qs("update-dialog"),bI=Qs("update-title"),vI=Qs("update-body"),yI=Qs("update-footer"),xI=Qs("body-send-message"),wI=(e,t,o,n,r)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...r?{"aria-live":"polite"}:{}}},components:[],behaviours:gl([Zk(0),qM.config({channel:`${vI}-${t}`,updateState:(e,t)=>M.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[hI(t,e.initialData,n)]:[ZF(t,e.initialData,n)]},initialData:e})])});function SI(e){return SI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},SI(e)}function kI(e,t){return kI=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},kI(e,t)}function CI(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function OI(e,t,o){return OI=CI()?Reflect.construct:function(e,t,o){var n=[null];n.push.apply(n,t);var r=new(Function.bind.apply(e,n));return o&&kI(r,o.prototype),r},OI.apply(null,arguments)}function _I(e){return function(e){if(Array.isArray(e))return TI(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return TI(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?TI(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function TI(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}var EI=Object.hasOwnProperty,MI=Object.setPrototypeOf,AI=Object.isFrozen,DI=Object.getPrototypeOf,BI=Object.getOwnPropertyDescriptor,FI=Object.freeze,II=Object.seal,RI=Object.create,NI="undefined"!=typeof Reflect&&Reflect,VI=NI.apply,HI=NI.construct;VI||(VI=function(e,t,o){return e.apply(t,o)}),FI||(FI=function(e){return e}),II||(II=function(e){return e}),HI||(HI=function(e,t){return OI(e,_I(t))});var zI,LI=YI(Array.prototype.forEach),PI=YI(Array.prototype.pop),UI=YI(Array.prototype.push),WI=YI(String.prototype.toLowerCase),jI=YI(String.prototype.match),GI=YI(String.prototype.replace),$I=YI(String.prototype.indexOf),qI=YI(String.prototype.trim),XI=YI(RegExp.prototype.test),KI=(zI=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return HI(zI,t)});function YI(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),r=1;r<o;r++)n[r-1]=arguments[r];return VI(e,t,n)}}function JI(e,t){MI&&MI(e,null);for(var o=t.length;o--;){var n=t[o];if("string"==typeof n){var r=WI(n);r!==n&&(AI(t)||(t[o]=r),n=r)}e[n]=!0}return e}function ZI(e){var t,o=RI(null);for(t in e)VI(EI,e,[t])&&(o[t]=e[t]);return o}function QI(e,t){for(;null!==e;){var o=BI(e,t);if(o){if(o.get)return YI(o.get);if("function"==typeof o.value)return YI(o.value)}e=DI(e)}return function(e){return console.warn("fallback value for",e),null}}var eR=FI(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),tR=FI(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),oR=FI(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),nR=FI(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),rR=FI(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),sR=FI(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),aR=FI(["#text"]),iR=FI(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),lR=FI(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),cR=FI(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),dR=FI(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),uR=II(/\{\{[\w\W]*|[\w\W]*\}\}/gm),mR=II(/<%[\w\W]*|[\w\W]*%>/gm),gR=II(/^data-[\-\w.\u00B7-\uFFFF]/),pR=II(/^aria-[\-\w]+$/),hR=II(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),fR=II(/^(?:\w+script|data):/i),bR=II(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),vR=II(/^html$/i),yR=function(){return"undefined"==typeof window?null:window},xR=function(e,t){if("object"!==SI(e)||"function"!=typeof e.createPolicy)return null;var o=null,n="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(n)&&(o=t.currentScript.getAttribute(n));var r="dompurify"+(o?"#"+o:"");try{return e.createPolicy(r,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}},wR=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:yR(),o=function(t){return e(t)};if(o.version="2.3.8",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;var n=t.document,r=t.document,s=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,c=t.NodeFilter,d=t.NamedNodeMap,u=void 0===d?t.NamedNodeMap||t.MozNamedAttrMap:d,m=t.HTMLFormElement,g=t.DOMParser,p=t.trustedTypes,h=l.prototype,f=QI(h,"cloneNode"),b=QI(h,"nextSibling"),v=QI(h,"childNodes"),y=QI(h,"parentNode");if("function"==typeof a){var x=r.createElement("template");x.content&&x.content.ownerDocument&&(r=x.content.ownerDocument)}var w=xR(p,n),S=w?w.createHTML(""):"",k=r,C=k.implementation,O=k.createNodeIterator,_=k.createDocumentFragment,T=k.getElementsByTagName,E=n.importNode,M={};try{M=ZI(r).documentMode?r.documentMode:{}}catch(e){}var A={};o.isSupported="function"==typeof y&&C&&void 0!==C.createHTMLDocument&&9!==M;var D,B,F=uR,I=mR,R=gR,N=pR,V=fR,H=bR,z=hR,L=null,P=JI({},[].concat(_I(eR),_I(tR),_I(oR),_I(rR),_I(aR))),U=null,W=JI({},[].concat(_I(iR),_I(lR),_I(cR),_I(dR))),j=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),G=null,$=null,q=!0,X=!0,K=!1,Y=!1,J=!1,Z=!1,Q=!1,ee=!1,te=!1,oe=!1,ne=!0,re=!0,se=!1,ae={},ie=null,le=JI({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ce=null,de=JI({},["audio","video","img","source","image","track"]),ue=null,me=JI({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml",fe=he,be=!1,ve=["application/xhtml+xml","text/html"],ye="text/html",xe=null,we=r.createElement("form"),Se=function(e){return e instanceof RegExp||e instanceof Function},ke=function(e){xe&&xe===e||(e&&"object"===SI(e)||(e={}),e=ZI(e),L="ALLOWED_TAGS"in e?JI({},e.ALLOWED_TAGS):P,U="ALLOWED_ATTR"in e?JI({},e.ALLOWED_ATTR):W,ue="ADD_URI_SAFE_ATTR"in e?JI(ZI(me),e.ADD_URI_SAFE_ATTR):me,ce="ADD_DATA_URI_TAGS"in e?JI(ZI(de),e.ADD_DATA_URI_TAGS):de,ie="FORBID_CONTENTS"in e?JI({},e.FORBID_CONTENTS):le,G="FORBID_TAGS"in e?JI({},e.FORBID_TAGS):{},$="FORBID_ATTR"in e?JI({},e.FORBID_ATTR):{},ae="USE_PROFILES"in e&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,X=!1!==e.ALLOW_DATA_ATTR,K=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Y=e.SAFE_FOR_TEMPLATES||!1,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,ne=!1!==e.SANITIZE_DOM,re=!1!==e.KEEP_CONTENT,se=e.IN_PLACE||!1,z=e.ALLOWED_URI_REGEXP||z,fe=e.NAMESPACE||he,e.CUSTOM_ELEMENT_HANDLING&&Se(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(j.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Se(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(j.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(j.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),D=D=-1===ve.indexOf(e.PARSER_MEDIA_TYPE)?ye:e.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===D?function(e){return e}:WI,Y&&(X=!1),te&&(ee=!0),ae&&(L=JI({},_I(aR)),U=[],!0===ae.html&&(JI(L,eR),JI(U,iR)),!0===ae.svg&&(JI(L,tR),JI(U,lR),JI(U,dR)),!0===ae.svgFilters&&(JI(L,oR),JI(U,lR),JI(U,dR)),!0===ae.mathMl&&(JI(L,rR),JI(U,cR),JI(U,dR))),e.ADD_TAGS&&(L===P&&(L=ZI(L)),JI(L,e.ADD_TAGS)),e.ADD_ATTR&&(U===W&&(U=ZI(U)),JI(U,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&JI(ue,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(ie===le&&(ie=ZI(ie)),JI(ie,e.FORBID_CONTENTS)),re&&(L["#text"]=!0),J&&JI(L,["html","head","body"]),L.table&&(JI(L,["tbody"]),delete G.tbody),FI&&FI(e),xe=e)},Ce=JI({},["mi","mo","mn","ms","mtext"]),Oe=JI({},["foreignobject","desc","title","annotation-xml"]),_e=JI({},["title","style","font","a","script"]),Te=JI({},tR);JI(Te,oR),JI(Te,nR);var Ee=JI({},rR);JI(Ee,sR);var Me=function(e){var t=y(e);t&&t.tagName||(t={namespaceURI:he,tagName:"template"});var o=WI(e.tagName),n=WI(t.tagName);return e.namespaceURI===pe?t.namespaceURI===he?"svg"===o:t.namespaceURI===ge?"svg"===o&&("annotation-xml"===n||Ce[n]):Boolean(Te[o]):e.namespaceURI===ge?t.namespaceURI===he?"math"===o:t.namespaceURI===pe?"math"===o&&Oe[n]:Boolean(Ee[o]):e.namespaceURI===he&&!(t.namespaceURI===pe&&!Oe[n])&&!(t.namespaceURI===ge&&!Ce[n])&&!Ee[o]&&(_e[o]||!Te[o])},Ae=function(e){UI(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=S}catch(t){e.remove()}}},De=function(e,t){try{UI(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){UI(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!U[e])if(ee||te)try{Ae(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){var t,o;if(Q)e="<remove></remove>"+e;else{var n=jI(e,/^[\r\n\t ]+/);o=n&&n[0]}"application/xhtml+xml"===D&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var s=w?w.createHTML(e):e;if(fe===he)try{t=(new g).parseFromString(s,D)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(fe,"template",null);try{t.documentElement.innerHTML=be?"":s}catch(e){}}var a=t.body||t.documentElement;return e&&o&&a.insertBefore(r.createTextNode(o),a.childNodes[0]||null),fe===he?T.call(t,J?"html":"body")[0]:J?t.documentElement:a},Fe=function(e){return O.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Ie=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore)},Re=function(e){return"object"===SI(i)?e instanceof i:e&&"object"===SI(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Ne=function(e,t,n){A[e]&&LI(A[e],(function(e){e.call(o,t,n,xe)}))},Ve=function(e){var t;if(Ne("beforeSanitizeElements",e,null),Ie(e))return Ae(e),!0;if(XI(/[\u0080-\uFFFF]/,e.nodeName))return Ae(e),!0;var n=B(e.nodeName);if(Ne("uponSanitizeElement",e,{tagName:n,allowedTags:L}),e.hasChildNodes()&&!Re(e.firstElementChild)&&(!Re(e.content)||!Re(e.content.firstElementChild))&&XI(/<[/\w]/g,e.innerHTML)&&XI(/<[/\w]/g,e.textContent))return Ae(e),!0;if("select"===n&&XI(/<template/i,e.innerHTML))return Ae(e),!0;if(!L[n]||G[n]){if(!G[n]&&ze(n)){if(j.tagNameCheck instanceof RegExp&&XI(j.tagNameCheck,n))return!1;if(j.tagNameCheck instanceof Function&&j.tagNameCheck(n))return!1}if(re&&!ie[n]){var r=y(e)||e.parentNode,s=v(e)||e.childNodes;if(s&&r)for(var a=s.length-1;a>=0;--a)r.insertBefore(f(s[a],!0),b(e))}return Ae(e),!0}return e instanceof l&&!Me(e)?(Ae(e),!0):"noscript"!==n&&"noembed"!==n||!XI(/<\/no(script|embed)/i,e.innerHTML)?(Y&&3===e.nodeType&&(t=e.textContent,t=GI(t,F," "),t=GI(t,I," "),e.textContent!==t&&(UI(o.removed,{element:e.cloneNode()}),e.textContent=t)),Ne("afterSanitizeElements",e,null),!1):(Ae(e),!0)},He=function(e,t,o){if(ne&&("id"===t||"name"===t)&&(o in r||o in we))return!1;if(X&&!$[t]&&XI(R,t));else if(q&&XI(N,t));else if(!U[t]||$[t]){if(!(ze(e)&&(j.tagNameCheck instanceof RegExp&&XI(j.tagNameCheck,e)||j.tagNameCheck instanceof Function&&j.tagNameCheck(e))&&(j.attributeNameCheck instanceof RegExp&&XI(j.attributeNameCheck,t)||j.attributeNameCheck instanceof Function&&j.attributeNameCheck(t))||"is"===t&&j.allowCustomizedBuiltInElements&&(j.tagNameCheck instanceof RegExp&&XI(j.tagNameCheck,o)||j.tagNameCheck instanceof Function&&j.tagNameCheck(o))))return!1}else if(ue[t]);else if(XI(z,GI(o,H,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==$I(o,"data:")||!ce[e])if(K&&!XI(V,GI(o,H,"")));else if(o)return!1;return!0},ze=function(e){return e.indexOf("-")>0},Le=function(e){var t,o,n,r;Ne("beforeSanitizeAttributes",e,null);var s=e.attributes;if(s){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:U};for(r=s.length;r--;){var i=t=s[r],l=i.name,c=i.namespaceURI;o="value"===l?t.value:qI(t.value),n=B(l);var d=o;if(a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Ne("uponSanitizeAttribute",e,a),o=a.attrValue,!a.forceKeepAttr)if(a.keepAttr)if(XI(/\/>/i,o))De(l,e);else{Y&&(o=GI(o,F," "),o=GI(o,I," "));var u=B(e.nodeName);if(He(u,n,o)){if(o!==d)try{c?e.setAttributeNS(c,l,o):e.setAttribute(l,o)}catch(t){De(l,e)}}else De(l,e)}else De(l,e)}Ne("afterSanitizeAttributes",e,null)}},Pe=function e(t){var o,n=Fe(t);for(Ne("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Ne("uponSanitizeShadowNode",o,null),Ve(o)||(o.content instanceof s&&e(o.content),Le(o));Ne("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e,r){var a,l,c,d,u;if((be=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Re(e)){if("function"!=typeof e.toString)throw KI("toString is not a function");if("string"!=typeof(e=e.toString()))throw KI("dirty is not a string, aborting")}if(!o.isSupported){if("object"===SI(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(Re(e))return t.toStaticHTML(e.outerHTML)}return e}if(Z||ke(r),o.removed=[],"string"==typeof e&&(se=!1),se){if(e.nodeName){var m=B(e.nodeName);if(!L[m]||G[m])throw KI("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(l=(a=Be("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===l.nodeName||"HTML"===l.nodeName?a=l:a.appendChild(l);else{if(!ee&&!Y&&!J&&-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;if(!(a=Be(e)))return ee?null:oe?S:""}a&&Q&&Ae(a.firstChild);for(var g=Fe(se?e:a);c=g.nextNode();)3===c.nodeType&&c===d||Ve(c)||(c.content instanceof s&&Pe(c.content),Le(c),d=c);if(d=null,se)return e;if(ee){if(te)for(u=_.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return U.shadowroot&&(u=E.call(n,u,!0)),u}var p=J?a.outerHTML:a.innerHTML;return J&&L["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&XI(vR,a.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+p),Y&&(p=GI(p,F," "),p=GI(p,I," ")),w&&oe?w.createHTML(p):p},o.setConfig=function(e){ke(e),Z=!0},o.clearConfig=function(){xe=null,Z=!1},o.isValidAttribute=function(e,t,o){xe||ke({});var n=B(e),r=B(t);return He(n,r,o)},o.addHook=function(e,t){"function"==typeof t&&(A[e]=A[e]||[],UI(A[e],t))},o.removeHook=function(e){if(A[e])return PI(A[e])},o.removeHooks=function(e){A[e]&&(A[e]=[])},o.removeAllHooks=function(){A={}},o}();const SR=e=>wR().sanitize(e),kR=qh.deviceType.isTouch(),CR=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),OR=(e,t)=>wB.parts.close(Mh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:gl([Mw.config({})])})),_R=()=>wB.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),TR=(e,t)=>wB.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:dE(`<p>${SR(t.translate(e))}</p>`)}]}]}),ER=e=>wB.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),MR=(e,t)=>[Sw.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),Sw.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],AR=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",r=t+"__disable-scroll";return wB.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),M.some(!0)),useTabstopAt:e=>!hC(e),dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:dE(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:kR?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:gl([Up.config({}),Vp("dialog-events",e.dialogEvents.concat([Ls(Lr(),((e,t)=>{Tp.focusIn(e)}))])),Vp("scroll-lock",[Ps((()=>{Da(ht(),r)})),Us((()=>{Ba(ht(),r)}))]),...e.extraBehaviours]),eventOrder:{[ns()]:["dialog-events"],[ps()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[hs()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},DR=e=>Mh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},components:[Lh("close",{tag:"div",classes:["tox-icon"]},e.icons)],action:e=>{Cs(e,Vw)}}),BR=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:gl([qM.config({channel:`${bI}-${t}`,initialData:e,renderComponents:e=>[Ga(n.translate(e.title))]})])}),FR=()=>({dom:dE('<div class="tox-dialog__draghandle"></div>')}),IR=(e,t,o)=>((e,t,o)=>{const n=wB.parts.title(BR(e,t,M.none(),o)),r=wB.parts.draghandle(FR()),s=wB.parts.close(DR(o)),a=[n].concat(e.draggable?[r]:[]).concat([s]);return Sw.sketch({dom:dE('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),RR=(e,t,o)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:dE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),NR=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{wB.setBusy(e(),((e,n)=>RR(o.message,n,t)))},onUnblock:()=>{wB.setIdle(e())}}),VR=(e,t,o,n)=>Ka(AR({...e,lazySink:n.shared.getSink,extraBehaviours:[qM.config({channel:`${fI}-${e.id}`,updateState:(e,t)=>M.some(t),initialData:t}),sC({}),...e.extraBehaviours],onEscape:e=>{Cs(e,Vw)},dialogEvents:o,eventOrder:{[os()]:[qM.name(),yl.name()],[ps()]:["scroll-lock",qM.name(),"messages","dialog-events","alloy.base.behaviour"],[hs()]:["alloy.base.behaviour","dialog-events","messages",qM.name(),"scroll-lock"]}})),HR=e=>z(e,(e=>"menu"===e.type?(e=>{const t=z(e.items,(e=>({...e,storage:xr(!1)})));return{...e,items:t}})(e):e)),zR=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),LR=(e,t)=>[Vs(Lr(),pC),e(Nw,((e,o)=>{t.onClose(),o.onClose()})),e(Vw,((e,t,o,n)=>{t.onCancel(e),Cs(n,Nw)})),Fs(Pw,((e,o)=>t.onUnblock())),Fs(Lw,((e,o)=>t.onBlock(o.event)))],PR=(e,t,o)=>{const n=(t,o)=>Fs(t,((t,n)=>{r(t,((r,s)=>{o(e(),r,n.event,t)}))})),r=(e,t)=>{qM.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...LR(n,t),n(zw,((e,t)=>t.onSubmit(e))),n(Rw,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Hw,((e,t,n,r)=>{const s=()=>Tp.focusIn(r),a=e=>St(e,"disabled")||wt(e,"aria-disabled").exists((e=>"true"===e)),i=dt(r.element),l=kl(i);t.onAction(e,{name:n.name,value:n.value}),kl(i).fold(s,(e=>{a(e)||l.exists((t=>Ke(e,t)&&a(t)))?s():o().toOptional().filter((t=>!Ke(t.element,e))).each(s)}))})),n(Uw,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Us((t=>{const o=e();ou.setValue(t,o.getData())}))]},UR=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),r=(e,t)=>Sw.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:z(t,(e=>e.memento.asSpec()))});return[r("start",n.pass),r("end",n.fail)]},WR=(e,t,o)=>({dom:dE('<div class="tox-dialog__footer"></div>'),components:[],behaviours:gl([qM.config({channel:`${yI}-${t}`,initialData:e,updateState:(e,t)=>{const n=z(t.buttons,(e=>{const t=Ah(((e,t)=>uO(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return M.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:UR})])}),jR=(e,t,o)=>wB.parts.footer(WR(e,t,o)),GR=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=cm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return jk.getField(o,t).orThunk((()=>{const o=e.getFooter();return qM.getState(o).get().bind((e=>e.lookupByName(t)))}))}return M.none()},$R=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},r={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...ou.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const s=r.getData(),a=cn(s,t),i=((e,t)=>{const o=e.getRoot();return qM.getState(o).get().map((e=>Pn(Ln("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();ou.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{GR(e,t).each(o?km.enable:km.disable)},focus:t=>{GR(e,t).each(Up.focus)},block:e=>{if(!s(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Os(t,Lw,{message:e})}))},unblock:()=>{n((e=>{Cs(e,Pw)}))},showTab:t=>{n((o=>{const n=e.getBody();qM.getState(n).get().exists((e=>e.isTabPanel()))&&cm.getCurrent(n).each((e=>{cI.showTab(e,t)}))}))},redial:o=>{n((n=>{const s=e.getId(),a=t(o);n.getSystem().broadcastOn([`${fI}-${s}`],a),n.getSystem().broadcastOn([`${bI}-${s}`],a.internalDialog),n.getSystem().broadcastOn([`${vI}-${s}`],a.internalDialog),n.getSystem().broadcastOn([`${yI}-${s}`],a.internalDialog),r.setData(a.initialData)}))},close:()=>{n((e=>{Cs(e,Nw)}))}};return r};var qR=tinymce.util.Tools.resolve("tinymce.util.URI");const XR=["insertContent","setContent","execCommand","close","block","unblock"],KR=e=>a(e)&&-1!==XR.indexOf(e.mceAction),YR=(e,t,o,n)=>{const r=Qs("dialog"),i=IR(e.title,r,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[mC({dom:{tag:"iframe",attributes:{src:e.url}},behaviours:gl([Mw.config({}),Up.config({})])})]}],behaviours:gl([Tp.config({mode:"acyclic",useTabstopAt:k(hC)})])};return wB.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?M.none():M.some(jR({buttons:e},r,n)))),u=((e,t)=>{const o=(t,o)=>Fs(t,((t,r)=>{n(t,((n,s)=>{o(e(),n,r.event,t)}))})),n=(e,t)=>{qM.getState(e).get().each((o=>{t(o,e)}))};return[...LR(o,t),o(Hw,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})((()=>x),NR((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new qR(e.url,{base_uri:new qR(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=Pl(),v=[Vp("messages",[Ps((()=>{const t=jl(Ie(window),"message",(t=>{if(h.isSameOrigin(new qR(t.raw.origin))){const n=t.raw.data;KR(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!KR(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Us(b.clear)]),yl.config({channels:{[xI]:{onReceive:(e,t)=>{ri(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=VR({id:r,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},e,u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!s(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Os(t,Lw,{message:e})}))},unblock:()=>{t((e=>{Cs(e,Pw)}))},close:()=>{t((e=>{Cs(e,Nw)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([xI],e)}))}}})(y);return{dialog:y,instanceApi:x}},JR=(e,t,o)=>t&&o?[]:[hT.config({contextual:{lazyContext:()=>M.some($o(Ie(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})],ZR=e=>{const t=e.editor,o=Gf(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const r=()=>{wB.hide(l),n()},s=Ah(uO({name:"close-alert",text:"OK",primary:!0,buttonType:M.some("primary"),align:"end",enabled:!0,icon:M.none()},"cancel",e)),a=_R(),i=OR(r,t.providers),l=Ka(AR({lazySink:()=>t.getSink(),header:CR(a,i),body:TR(o,t.providers),footer:M.some(ER(MR([],[s.asSpec()]))),onEscape:r,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Fs(Vw,r)],eventOrder:{}}));wB.show(l);const c=s.get(l);Up.focus(c)}}})(e.backstages.dialog),r=(e=>{const t=e.shared;return{open:(o,n)=>{const r=e=>{wB.hide(c),n(e)},s=Ah(uO({name:"yes",text:"Yes",primary:!0,buttonType:M.some("primary"),align:"end",enabled:!0,icon:M.none()},"submit",e)),a=uO({name:"no",text:"No",primary:!1,buttonType:M.some("secondary"),align:"end",enabled:!0,icon:M.none()},"cancel",e),i=_R(),l=OR((()=>r(!1)),t.providers),c=Ka(AR({lazySink:()=>t.getSink(),header:CR(i,l),body:TR(o,t.providers),footer:M.some(ER(MR([],[a,s.asSpec()]))),onEscape:()=>r(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Fs(Vw,(()=>r(!1))),Fs(zw,(()=>r(!0)))],eventOrder:{}}));wB.show(c);const d=s.get(c);Up.focus(d)}}})(e.backstages.dialog),s=(t,o)=>YF.open(((t,n,r)=>{const s=n,a=((e,t,o)=>{const n=Qs("dialog"),r=e.internalDialog,s=IR(r.title,n,o),a=((e,t,o)=>{const n=wI(e,t,M.none(),o,!1);return wB.parts.body(n)})({body:r.body,initialData:r.initialData},n,o),i=HR(r.buttons),l=zR(i),c=jR({buttons:i},n,o),d=PR((()=>h),NR((()=>g),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(r.size),m={id:n,header:s,body:a,footer:M.some(c),extraClasses:u,extraBehaviours:[],extraStyles:{}},g=VR(m,e,d,o),p={getId:y(n),getRoot:y(g),getBody:()=>wB.getBody(g),getFooter:()=>wB.getFooter(g),getFormWrapper:()=>{const e=wB.getBody(g);return cm.getCurrent(e).getOr(e)}},h=$R(p,t.redial,l);return{dialog:g,instanceApi:h}})({dataValidator:r,initialData:s,internalDialog:t},{redial:YF.redial,closeWindow:()=>{wB.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return wB.show(a.dialog),a.instanceApi.setData(s),a.instanceApi}),t),a=(n,r,s,a=!1)=>YF.open(((n,i,l)=>{const c=Pn(Ln("data",l,i)),d=Ul(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Th.reposition(e),hT.refresh(e)})),g=((e,t,o,n)=>{const r=Qs("dialog"),s=Qs("dialog-label"),a=Qs("dialog-content"),i=e.internalDialog,l=Ah(((e,t,o,n)=>Sw.sketch({dom:dE('<div class="tox-dialog__header"></div>'),components:[BR(e,t,M.some(o),n),FR(),DR(n)],containerBehaviours:gl([aB.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>si(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},r,s,o.shared.providers)),c=Ah(((e,t,o,n,r)=>wI(e,t,M.some(o),n,r))({body:i.body,initialData:i.initialData},r,a,o,n)),d=HR(i.buttons),u=zR(d),m=Ah(((e,t,o)=>WR(e,t,o))({buttons:d},r,o)),g=PR((()=>h),{onBlock:e=>{cE.block(p,((t,n)=>RR(e.message,n,o.shared.providers)))},onUnblock:()=>{cE.unblock(p)},onClose:()=>t.closeWindow()},o.shared.getSink),p=Ka({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:{role:"dialog","aria-labelledby":s,"aria-describedby":a}},eventOrder:{[os()]:[qM.name(),yl.name()],[ns()]:["execute-on-form"],[ps()]:["reflecting","execute-on-form"]},behaviours:gl([Tp.config({mode:"cyclic",onEscape:e=>(Cs(e,Nw),M.some(!0)),useTabstopAt:e=>!hC(e)&&("button"!==ze(e)||"disabled"!==xt(e,"disabled"))}),qM.config({channel:`${fI}-${r}`,updateState:(e,t)=>M.some(t),initialData:e}),Up.config({}),Vp("execute-on-form",g.concat([Ls(Lr(),((e,t)=>{Tp.focusIn(e)}))])),cE.config({getRoot:()=>M.some(p)}),Np.config({}),sC({})]),components:[l.asSpec(),c.asSpec(),m.asSpec()]}),h=$R({getId:y(r),getRoot:y(p),getFooter:()=>m.get(p),getBody:()=>c.get(p),getFormWrapper:()=>{const e=c.get(p);return cm.getCurrent(e).getOr(e)}},t.redial,u);return{dialog:p,instanceApi:h}})({dataValidator:l,initialData:c,internalDialog:n},{redial:YF.redial,closeWindow:()=>{d.on(Th.hide),t.off("ResizeEditor",m),d.clear(),s(g.instanceApi)}},e.backstages.popup,a),p=Ka(Th.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:gl([Vp("window-manager-inline-events",[Fs(fs(),((e,t)=>{Cs(g.dialog,Vw)}))]),...JR(t,o,u)]),isExtraPart:(e,t)=>(e=>mw(e,".tox-alert-dialog")||mw(e,".tox-confirm-dialog"))(t)}));return d.set(p),Th.showWithin(p,Ya(g.dialog),{anchor:r},M.some(ht())),o&&u||(hT.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),Tp.focusIn(g.dialog),g.instanceApi}),n);return{open:(t,o,n)=>void 0!==o&&"toolbar"===o.inline?a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o.ariaAttrs):void 0!==o&&"cursor"===o.inline?a(t,e.backstages.popup.shared.anchors.cursor(),n,o.ariaAttrs):s(t,n),openUrl:(o,n)=>((o,n)=>YF.openUrl((o=>{const r=YR(o,{closeWindow:()=>{wB.hide(r.dialog),n(r.instanceApi)}},t,e.backstages.dialog);return wB.show(r.dialog),r.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{r.open(e,t)}}};E.add("silver",(e=>{(e=>{Yh(e),(e=>{const t=e.options.register,o=e=>f(e,s)?{value:ax(e),valid:!0}:{valid:!1,message:"Must be an array of strings."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:"number",default:sx(ux(e,"default").length)}),t("color_cols_foreground",{processor:"number",default:sx(ux(e,nx).length)}),t("color_cols_background",{processor:"number",default:sx(ux(e,rx).length)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:lx}),t("color_default_background",{processor:"string",default:lx})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:s(e)||f(e,s)?{value:tD(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);const{dialogs:t,popups:o,renderUI:n}=bB(e);uw(e,o.backstage.shared);const r=ZR({editor:e,backstages:{popup:o.backstage,dialog:t.backstage}});return{renderUI:n,getWindowManagerImpl:y(r),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,r=()=>{const t=$o(Ie(e.getContentAreaContainer())),o=Xo(),n=zi(o.x,t.x,t.right),r=zi(o.y,t.y,t.bottom),s=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return M.some(Go(n,r,s-n,a-r))};return{open:(t,s)=>{const a=()=>{s(),Th.hide(l)},i=Ka(Uh.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=Ka(Th.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&Eh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=Ya(i),o={maxHeightFunction:Zl()},s=e.notificationManager.getNotifications();if(s[0]===c){const e={...n.anchors.banner(),overrides:o};Th.showWithinBounds(l,t,{anchor:e},r)}else I(s,c).each((e=>{const n=s[e-1].getEl(),a={type:"node",root:ht(),node:M.some(Ie(n)),overrides:o,layouts:{onRtl:()=>[Qi],onLtr:()=>[Qi]}};Th.showWithinBounds(l,t,{anchor:a},r)}))},text:e=>{Uh.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{Uh.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:o.backstage},o.getMothership())}}))}();