<template>
  <base-content scrollable>
    <div class="q-gutter-md q-ma-md">
      <q-btn
        color="primary"
        :label="t('admin.Add') + t('admin.Root') + t('admin.Menu')"
        class="q-my-md"
        @click="showAddParentForm()"
      />
      <q-hierarchy
        v-if="tableData"
        separator="cell"
        dense
        :columns="columns"
        :data="tableData"
      >
        <template #body="props">
          <TreeTd :tree-td="props" first-td="sort" class="text-center" />
          <td class="text-center">
            {{ props.item.id }}
          </td>
          <td class="text-center">
            {{ props.item.name }}
          </td>
          <td class="text-center">
            {{ props.item.level + 1 }}
          </td>
          <td class="text-center">
            {{ props.item.weight }}
          </td>
          <td class="text-center">
            <div class="q-gutter-xs">
              <q-btn-group>
                <q-btn
                  size="md"
                  dense
                  color="primary"
                  :label="t('admin.Edit')"
                  @click="showEditForm(props.item)"
                />
                <q-btn
                  size="md"
                  dense
                  color="positive"
                  :label="
                    t('admin.Add') + t('admin.Children') + t('admin.Menu')
                  "
                  @click="showAddChildrenForm(props.item)"
                />
                <q-btn
                  size="md"
                  dense
                  color="negative"
                  :label="t('admin.Delete')"
                  @click="handleDelete(props.item)"
                />
              </q-btn-group>
            </div>
          </td>
        </template>
      </q-hierarchy>
      <q-card v-else class="q-py-md text-center">
        <h5>暂无目录信息</h5>
      </q-card>
      <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
    </div>
  </base-content>
</template>

<script setup>
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import TreeTd from "src/components/TreeTd/index.vue";
import useTableData from "src/composables/useTableData";
import RecordDetail from "src/pages/productCategory/modules/recordDetail.vue";
import { computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";

const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/business/category/list",
  create: "/api/business/category",
  edit: "/api/business/category",
  delete: "/api/business/category",
};
const columns = computed(() => {
  return [
    { name: "sort", align: "center", label: t("admin.Sort"), field: "sort" },
    { name: "id", align: "center", label: t("ID"), field: "id" },
    { name: "name", align: "center", label: t("admin.Name"), field: "name" },
    {
      name: "level",
      align: "center",
      label: t("admin.Level"),
      field: "level",
    },
    {
      name: "weight",
      align: "center",
      label: t("admin.Sort"),
      field: "weight",
    },
    {
      name: "actions",
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  getTableData,
  handleFinish,
  handleDelete,
} = useTableData(url);

onMounted(() => {
  queryParams.value.parent_id = 0;
  pagination.value.order = "weight";
  pagination.value.descending = true;
  pagination.value.rowsPerPage = 9999;
  getTableData();
});

const showEditForm = (item) => {
  recordDetailDialog.value.formType = "edit";
  recordDetailDialog.value.show(item);
};

const showAddParentForm = () => {
  recordDetailDialog.value.formType = "add";
  recordDetailDialog.value.show();
  recordDetailDialog.value.recordDetail.value.parent_id = 0;
  recordDetailDialog.value.recordDetail.value.parent_name = "根目录";
  recordDetailDialog.value.recordDetail.value.category_level = 1;
};
const showAddChildrenForm = (item) => {
  recordDetailDialog.value.formType = "add";
  recordDetailDialog.value.show();
  recordDetailDialog.value.recordDetail.value.parent_id = item.id;
  recordDetailDialog.value.recordDetail.value.parent_name = item.name;
  recordDetailDialog.value.recordDetail.value.category_level =
    item.category_level + 1;
};
</script>
