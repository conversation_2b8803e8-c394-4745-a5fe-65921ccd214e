<template>
  <q-dialog v-model="recordDetailVisible" position="top">
    <q-card style="width: 800px; max-width: 80vw">
      <q-card-section>
        <div class="text-h6">
          {{ formTypeName }} {{ t("admin.Api") }}:
          {{ recordDetail.value.path }}
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section>
        <q-form ref="recordDetailForm">
          <div class="row">
            <q-input
              v-model.number="recordDetail.value.name"
              class="col"
              type="text"
              :label="t('admin.Name')"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.group"
              class="col"
              :label="t('admin.ApiGroup')"
              :rules="[
                (val) => (val && val.length > 0) || t('admin.NeedInput'),
              ]"
            />
            <q-select
              v-model="recordDetail.value.method"
              class="col"
              :options="methodOptions"
              :label="t('admin.Api') + t('admin.Method')"
              :rules="[
                (val) => (val && val.length > 0) || t('admin.NeedInput'),
              ]"
            />
          </div>
          <div class="row">
            <q-input
              v-model="recordDetail.value.path"
              class="col"
              :label="t('admin.Api') + t('admin.Path')"
              :rules="[
                (val) => (val && val.length > 0) || t('admin.NeedInput'),
              ]"
            />
          </div>
          <q-input
            v-model="recordDetail.value.backup"
            class="col"
            :label="t('admin.Backup')"
          />
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          :label="t('admin.Save')"
          color="primary"
          @click="handleAddOrEdit"
        />
        <q-btn v-close-popup :label="t('admin.Cancel')" color="negative" />
      </q-card-actions>

      <q-inner-loading :showing="loading">
        <q-spinner-gears size="50px" color="primary" />
      </q-inner-loading>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useRecordDetail from "src/composables/useRecordDetail";

import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emit = defineEmits(["handleFinish"]);
const url = {
  create: "/api/permission",
  edit: "/api/permission",
};

const {
  formType,
  formTypeName,
  recordDetail,
  recordDetailVisible,
  loading,
  show,
  recordDetailForm,
  handleAddOrEdit,
} = useRecordDetail(url, emit);

defineExpose({
  show,
  formType,
  recordDetail,
});

const methodOptions = ["POST", "GET", "PUT", "DELETE", "OPTION"];
</script>
