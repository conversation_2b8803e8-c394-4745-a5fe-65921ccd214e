<template>
  <div>
    <div class="row q-my-md">
      <q-input v-model="title" class="col q-mx-sm" :label="t('admin.Title')" />
      <q-input
        v-model="seoKw"
        class="col q-mx-sm"
        :label="t('admin.Seo') + t('admin.Keyword')"
      />
    </div>
    <div class="row q-my-md">
      <q-input
        v-model="slogan"
        class="col q-mx-sm"
        :label="t('admin.Slogan')"
      />
    </div>
    <div class="row">
      <Tinymce :value="content" class="col" @getContent="getContent" />
    </div>
  </div>
</template>

<script setup>
import Tinymce from "src/components/Editor/TinyMce.vue";
import { toRefs } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  title: {
    type: String,
    required: false,
    default: "",
  },
  seoKw: {
    type: String,
    required: false,
    default: "",
  },
  slogan: {
    type: String,
    required: false,
    default: "",
  },
  content: {
    type: String,
    required: false,
    default: "",
  },
});
const { title, seoKw, slogan, content } = toRefs(props);
const { t } = useI18n();

const getContent = (v) => {
  content.value = v;
};
</script>
