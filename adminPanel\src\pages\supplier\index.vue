<template>
  <BaseContent scrollable>
    <div class="row q-ma-md">
      <div class="col">
        <q-table
          v-model:pagination="pagination"
          row-key="id"
          separator="cell"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          @request="onRequest"
        >
          <template #top="props">
            <q-btn
              color="primary"
              :label="t('admin.Add') + t('admin.Supplier')"
              @click="showAddForm"
            />
            <q-space />
            <q-btn
              flat
              round
              dense
              :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
              class="q-ml-md"
              @click="props.toggleFullscreen"
            />
          </template>

          <template #body-cell-status="props">
            <q-td :props="props">
              <q-chip
                v-if="props.row.status === 'on'"
                square
                outline
                size="sm"
                color="positive"
                label="启用"
              />
              <q-chip v-else square outline size="sm" label="停用" />
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    color="primary"
                    :label="t('admin.Edit')"
                    size="sm"
                    @click="showEditForm(props.row)"
                  />
                  <q-btn
                    v-if="props.row.status === 'on'"
                    color="warning"
                    label="停用"
                    size="sm"
                    @click="handleDetailStatus(props.row)"
                  />
                  <q-btn
                    v-else
                    color="positive"
                    label="启用"
                    size="sm"
                    @click="handleDetailStatus(props.row)"
                  />
                  <q-btn
                    color="negative"
                    :label="t('admin.Delete')"
                    size="sm"
                    @click="handleDelete(props.row)"
                  />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
    <RecordDetail ref="recordDetailDialog" @handleFinish="handleFinish" />
  </BaseContent>
</template>

<script setup>
import { useQuasar } from "quasar";
import { putAction } from "src/api/manage";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import RecordDetail from "./modules/recordDetail.vue";

const $q = useQuasar();
const { t } = useI18n();
const url = {
  list: "/api/supplier/list",
  create: "/api/supplier",
  edit: "/api/supplier",
  delete: "/api/supplier",
};
const columns = computed(() => {
  return [
    {
      name: "name",
      required: true,
      align: "center",
      label: t("admin.Name"),
      field: "name",
    },
    {
      name: "serial",
      align: "center",
      label: t("admin.Serial"),
      field: "serial",
    },
    {
      name: "status",
      align: "center",
      label: t("admin.Status"),
      field: "status",
    },
    {
      name: "actions",
      required: true,
      align: "center",
      label: t("admin.Actions"),
      field: "actions",
    },
  ];
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  recordDetailDialog,
  onRequest,
  getTableData,
  showAddForm,
  showEditForm,
  handleDelete,
  handleFinish,
} = useTableData(url);

onMounted(() => {
  pagination.value.order = "created_at";
  pagination.value.desc = "false";
  getTableData();
});

const itemStatus = ref();
const handleDetailStatus = async (item) => {
  if (item.status === "on") {
    itemStatus.value = "off";
  } else {
    itemStatus.value = "on";
  }
  const { code, message } = await putAction(url.status, {
    id: item.id,
    status: itemStatus.value,
  });
  if (code === 200) {
    $q.notify({
      type: "positive",
      message: message,
    });
    await getTableData();
  } else {
    $q.notify({
      type: "warning",
      message: message,
    });
  }
};
</script>
